"""
Redis 键值存储客户端

提供基于 Redis 的连接池管理
"""

import json
import traceback
from typing import Any, Dict, List, Optional, Set

import redis.asyncio as redis
from redis.asyncio import Redis

from src.config import config
from src.rag.tools.logger import logger


class RedisClient:
    """Redis 键值存储客户端"""

    def __init__(self, embedding_func=None):
        """
        初始化 Redis 存储客户端
        
        Args:
            embedding_func: 嵌入函数（保持兼容性）
        """
        self.embedding_func = embedding_func

        # 从统一配置获取Redis配置
        db_config = config.database
        
        # 基本连接配置
        self.host = db_config.redis_host
        self.port = db_config.redis_port
        self.password = db_config.redis_password or ''
        self.db = db_config.redis_db
        # 只有在有密码的情况下才设置用户名
        self.username = None if not self.password else 'default'

        # 连接参数配置
        self.decode_responses = True
        self.socket_timeout = 5.0
        self.socket_connect_timeout = 5.0
        self.max_connections = db_config.redis_pool_size

        # Redis key 配置
        self.key_prefix = "mixrag"
        self.key_separator = ":"

        # 连接相关
        self.redis = None
        self._connection_pool = None

        logger.info(f"Redis storage initialized: {self.host}:{self.port}/{self.db}")

    async def initialize(self):
        """初始化 Redis 连接"""
        # 准备连接参数
        pool_kwargs = {
            "host": self.host,
            "port": self.port,
            "db": self.db,
            "decode_responses": self.decode_responses,
            "socket_timeout": self.socket_timeout,
            "socket_connect_timeout": self.socket_connect_timeout,
            "max_connections": self.max_connections,
        }

        # 如果有用户名和密码，添加认证信息
        if self.password and self.password.strip():
            if self.username and self.username != 'default':
                pool_kwargs["username"] = self.username
            pool_kwargs["password"] = self.password

        # 创建连接池
        self._connection_pool = redis.ConnectionPool(**pool_kwargs)
        self.redis = Redis(connection_pool=self._connection_pool)

        # 测试连接
        await self.redis.ping()

        # 如果有用户名和密码，显示在日志中
        auth_info = f"{self.username}@" if self.username and self.username != 'default' and self.password and self.password.strip() else ""
        logger.info(f"Redis storage connected successfully: {auth_info}{self.host}:{self.port}/{self.db}")

    async def finalize(self):
        """清理 Redis 连接"""
        try:
            if self.redis:
                await self.redis.aclose()
                self.redis = None

            if self._connection_pool:
                await self._connection_pool.aclose()
                self._connection_pool = None

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to finalize Redis storage: {e}")

    def _get_redis_key(self, key: str) -> str:
        """获取完整的 Redis 键名"""
        return f"{self.key_prefix}{self.key_separator}{key}"

    def _serialize_value(self, value: Dict[str, Any]) -> str:
        """序列化值为 JSON 字符串"""
        try:
            return json.dumps(value, ensure_ascii=False)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to serialize value: {e}")
            return "{}"

    def _deserialize_value(self, value: str) -> Optional[Dict[str, Any]]:
        """反序列化 JSON 字符串为字典"""
        if not value:
            return None

        try:
            return json.loads(value)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to deserialize value: {e}")
            return None

    async def get_by_id(self, id: str) -> Optional[Dict[str, Any]]:
        """
        根据 ID 获取值
        
        Args:
            id: 键 ID
            
        Returns:
            值字典或 None
        """
        try:
            if not self.redis:
                logger.error("Redis client not initialized")
                return None

            redis_key = self._get_redis_key(id)
            value = await self.redis.get(redis_key)

            if value is None:
                return None

            result = self._deserialize_value(value)
            logger.debug(f"Retrieved value for key: {id}")
            return result

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to get value by ID {id}: {e}")
            return None

    async def get_by_ids(self, ids: List[str]) -> List[Dict[str, Any]]:
        """
        根据 ID 列表获取值
        
        Args:
            ids: 键 ID 列表
            
        Returns:
            值列表
        """
        try:
            if not self.redis or not ids:
                return [None] * len(ids)

            redis_keys = [self._get_redis_key(id) for id in ids]
            values = await self.redis.mget(redis_keys)

            results = []
            for value in values:
                if value is None:
                    results.append(None)
                else:
                    deserialized = self._deserialize_value(value)
                    results.append(deserialized.copy() if deserialized else None)

            logger.debug(f"Retrieved {len(results)} values")
            return results

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to get values by IDs: {e}")
            return [None] * len(ids)

    async def upsert(self, data: Dict[str, Dict[str, Any]]) -> None:
        """
        插入或更新数据
        
        Args:
            data: 要插入/更新的数据字典
        """
        if not self.redis or not data:
            return

        mapping = {}
        for key, value in data.items():
            redis_key = self._get_redis_key(key)
            serialized_value = self._serialize_value(value)
            mapping[redis_key] = serialized_value

        await self.redis.mset(mapping)
        logger.info(f"Successfully upserted {len(data)} records")

    async def delete(self, ids: List[str]) -> None:
        """
        删除指定 ID 的数据
        
        Args:
            ids: 要删除的 ID 列表
        """
        if not self.redis or not ids:
            return

        redis_keys = [self._get_redis_key(id) for id in ids]
        deleted_count = await self.redis.delete(*redis_keys)

    async def filter_keys(self, keys: Set[str]) -> Set[str]:
        """
        过滤不存在的键
        
        Args:
            keys: 要检查的键集合
            
        Returns:
            不存在的键集合
        """
        if not self.redis or not keys:
            return keys

        redis_keys = [self._get_redis_key(key) for key in keys]
        existing_flags = await self.redis.exists(*redis_keys)

        if isinstance(existing_flags, int):
            existing_flags = [existing_flags]

        missing_keys = set()
        for i, (original_key, exists) in enumerate(zip(keys, existing_flags)):
            if not exists:
                missing_keys.add(original_key)

        logger.debug(f"Found {len(missing_keys)} missing keys out of {len(keys)}")
        return missing_keys

    async def get_all(self) -> Dict[str, Any]:
        """
        获取所有数据
        
        Returns:
            所有数据字典
        """
        if not self.redis:
            return {}

        pattern = f"{self.key_prefix}{self.key_separator}*"
        all_data = {}

        async for redis_key in self.redis.scan_iter(match=pattern, count=100):
            original_key = redis_key.replace(f"{self.key_prefix}{self.key_separator}", "", 1)
            value = await self.redis.get(redis_key)

            if value:
                deserialized_value = self._deserialize_value(value)
                if deserialized_value:
                    all_data[original_key] = deserialized_value
                else:
                    # 如果反序列化失败，记录警告但不添加到结果中
                    logger.warning(f"Failed to deserialize value for key: {original_key}, skipping")

        return all_data

    async def drop(self) -> Dict[str, str]:
        """
        删除所有数据
        
        Returns:
            操作结果
        """
        if not self.redis:
            return {"status": "error", "message": "Redis client not available"}

        pattern = f"{self.key_prefix}{self.key_separator}*"
        keys_to_delete = []

        async for key in self.redis.scan_iter(match=pattern, count=100):
            keys_to_delete.append(key)

        if keys_to_delete:
            deleted_count = await self.redis.delete(*keys_to_delete)
            return {"status": "success", "message": f"Deleted {deleted_count} records"}
        else:
            return {"status": "success", "message": "No data to delete"}

    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        if not self.redis:
            return {"status": "error", "message": "Redis client not available"}

        await self.redis.ping()
        info = await self.redis.info("server")
        keys_count = await self._get_keys_count()

        auth_info = f"{self.username}@" if self.username and self.username != 'default' and self.password and self.password.strip() else ""
        return {
            "status": "healthy",
            "redis_version": info.get("redis_version", "unknown"),
            "keys_count": keys_count,
            "connection": f"{auth_info}{self.host}:{self.port}/{self.db}",
            "config": {
                "host": self.host,
                "port": self.port,
                "database": self.db,
                "username": self.username if self.password and self.password.strip() else None,
                "has_password": bool(self.password and self.password.strip()),
                "max_connections": self.max_connections,
                "socket_timeout": self.socket_timeout
            }
        }

    async def _get_keys_count(self) -> int:
        """获取当前命名空间的键数量"""
        if not self.redis:
            return 0

        pattern = f"{self.key_prefix}{self.key_separator}*"
        count = 0

        async for _ in self.redis.scan_iter(match=pattern, count=100):
            count += 1

        return count

    # 兼容性方法
    async def index_done_callback(self) -> None:
        """索引完成回调 - Redis 是实时持久化的，这里不需要特殊操作"""

    async def drop_cache_by_modes(self, modes: Optional[List[str]] = None) -> bool:
        """根据模式删除缓存"""
        if not modes:
            return False

        await self.delete(modes)
        return True

# 请使用单例模式 不要额外实例化
redis_client = RedisClient()
