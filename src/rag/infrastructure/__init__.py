"""
基础设施模块

提供各种存储后端的客户端和操作接口
"""

# 配置和基础模型
from .schemas.base import (
    BaseStorage,
    BaseKVBaseStorage, 
    BaseVectorBaseStorage,
    BaseGraphBaseStorage,
    TextChunkSchema,
    DocStatus,
    DocProcessingStatus,
    QueryParam
)

# 存储客户端
from .db.client import postgre_sql_client
from .milvus.client import milvus_client
from .minio.client import min_io_client
from .neo4j.client import neo4j_connection_manager
from .redis.client import redis_client

# 存储操作管理器
from .db.document_operations import document_operations
from .db.pipeline_operations import pipeline_operations
from .milvus.vector_operations import milvus_vector_operations
from .neo4j.graph_operations import neo4j_graph_operations
from .minio.object_operations import object_operations
# from .redis.document_operations import redis_document_operations

__all__ = [
    # 配置
    "QueryParam",
    
    # 基础模型
    "BaseStorage",
    "BaseKVBaseStorage",
    "BaseVectorBaseStorage", 
    "BaseGraphBaseStorage",
    "TextChunkSchema",
    "DocStatus",
    "DocProcessingStatus",
    
    # 存储客户端
    "postgre_sql_client",
    "milvus_client",
    "min_io_client",
    "object_operations",
    "neo4j_connection_manager",
    "redis_client",

    # 存储操作管理器
    "document_operations",
    "pipeline_operations",
    "milvus_vector_operations",
    "neo4j_graph_operations",
    # "redis_document_operations"

]
