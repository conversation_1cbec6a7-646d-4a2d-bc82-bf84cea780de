"""
知识库数据库操作

提供知识库相关数据的 CRUD 操作
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from .client import postgre_sql_client
from src.rag.tools.logger import logger
from src.rag.infrastructure.milvus.vector_operations import milvus_vector_operations
from src.rag.infrastructure.minio.object_operations import object_operations


class KnowledgeBaseOperations:
    """知识库数据库操作类"""

    async def list_knowledge_bases(
            self,
            page: int = 1,
            page_size: int = 20,
            search_query: str = None,
            sort_by: str = "create_time",
            sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """列出知识库"""
        offset = (page - 1) * page_size

        # 构建查询条件
        where_conditions = ["1=1"]
        params = []
        param_count = 0

        # 处理搜索查询
        if search_query:
            param_count += 1
            where_conditions.append(f"(kb_name ILIKE ${param_count} OR kb_des ILIKE ${param_count})")
            params.append(f"%{search_query}%")

        where_clause = " AND ".join(where_conditions)

        # 构建排序子句
        valid_sort_fields = {
            'create_time', 'kb_name', 'doc_count'
        }
        if sort_by not in valid_sort_fields:
            sort_by = 'create_time'

        sort_order = sort_order.upper()
        if sort_order not in ['ASC', 'DESC']:
            sort_order = 'DESC'

        order_clause = f"ORDER BY {sort_by} {sort_order}"

        async with postgre_sql_client.pool.acquire() as conn:
            # 获取总数
            count_query = f"SELECT COUNT(*) FROM mixrag_knowledge_base WHERE {where_clause}"
            total = await conn.fetchval(count_query, *params)

            # 获取知识库列表
            list_query = f"""
            SELECT id, kb_name, kb_des, doc_count, create_time
            FROM mixrag_knowledge_base
            WHERE {where_clause}
            {order_clause}
            LIMIT ${param_count + 1} OFFSET ${param_count + 2}
            """
            params.extend([page_size, offset])

            rows = await conn.fetch(list_query, *params)

            # 转换为字典列表
            knowledge_bases = []
            for row in rows:
                kb_dict = dict(row)
                knowledge_bases.append(kb_dict)

            return {
                "records": knowledge_bases,
                "total": total,
                "page": page,
                "page_size": page_size
            }

    async def get_knowledge_base_by_id(self, kb_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取知识库详情"""
        async with postgre_sql_client.pool.acquire() as conn:
            query = """
            SELECT kb.id, kb.kb_name, kb.kb_des, kb.doc_count, kb.create_time,
                   COALESCE(SUM(d.chunks_count), 0) as chunks_count,
                   COALESCE(SUM(d.entities_count), 0) as entities_count,
                   COALESCE(SUM(d.relationships_count), 0) as relationships_count,
                   COALESCE(SUM(d.file_size), 0) as total_file_size
            FROM mixrag_knowledge_base kb
            LEFT JOIN mixrag_documents d ON kb.id = d.kb_id
            WHERE kb.id = $1
            GROUP BY kb.id, kb.kb_name, kb.kb_des, kb.doc_count, kb.create_time
            """
            row = await conn.fetchrow(query, kb_id)
            
            if row:
                return dict(row)
            return None

    async def create_knowledge_base(self, kb_name: str, kb_des: str = None) -> Dict[str, Any]:
        """创建知识库"""
        kb_id = str(uuid.uuid4())
        create_time = datetime.now(timezone.utc).replace(tzinfo=None)  # 使用UTC时间但移除时区信息

        async with postgre_sql_client.pool.acquire() as conn:
            query = """
            INSERT INTO mixrag_knowledge_base (id, kb_name, kb_des, doc_count, create_time)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, kb_name, kb_des, doc_count, create_time
            """
            row = await conn.fetchrow(query, kb_id, kb_name, kb_des, 0, create_time)

            if row:
                logger.info(f"Created knowledge base: {kb_id}")
                return dict(row)
            else:
                raise Exception("Failed to create knowledge base")

    async def update_knowledge_base(
            self, 
            kb_id: str, 
            kb_name: str = None, 
            kb_des: str = None
    ) -> Optional[Dict[str, Any]]:
        """更新知识库"""
        # 构建更新字段
        update_fields = []
        params = []
        param_count = 0

        if kb_name is not None:
            param_count += 1
            update_fields.append(f"kb_name = ${param_count}")
            params.append(kb_name)

        if kb_des is not None:
            param_count += 1
            update_fields.append(f"kb_des = ${param_count}")
            params.append(kb_des)

        if not update_fields:
            # 没有字段需要更新，直接返回当前数据
            return await self.get_knowledge_base_by_id(kb_id)

        param_count += 1
        params.append(kb_id)

        async with postgre_sql_client.pool.acquire() as conn:
            query = f"""
            UPDATE mixrag_knowledge_base 
            SET {', '.join(update_fields)}
            WHERE id = ${param_count}
            RETURNING id, kb_name, kb_des, doc_count, create_time
            """
            row = await conn.fetchrow(query, *params)
            
            if row:
                logger.info(f"Updated knowledge base: {kb_id}")
                return dict(row)
            return None

    async def delete_knowledge_base(self, kb_id: str) -> bool:
        """删除知识库"""
        async with postgre_sql_client.pool.acquire() as conn:
            async with conn.transaction():
                # 1. 获取知识库信息
                kb_info = await conn.fetchrow("SELECT kb_name FROM mixrag_knowledge_base WHERE id = $1", kb_id)
                if not kb_info:
                    logger.warning(f"Knowledge base with id {kb_id} not found.")
                    return False
                kb_name = kb_info['kb_name']

                # 2. 获取知识库下的所有文档ID
                doc_ids_rows = await conn.fetch("SELECT doc_id FROM mixrag_documents WHERE kb_id = $1", kb_id)
                doc_ids = [row['doc_id'] for row in doc_ids_rows]

                # 3. 从 Milvus 删除向量
                if doc_ids:
                    try:
                        for doc_id in doc_ids:
                            await milvus_vector_operations.delete_by_doc_id(doc_id)
                        logger.info(f"Deleted vectors for knowledge base {kb_id} from Milvus.")
                    except Exception as e:
                        logger.error(f"Failed to delete vectors for kb {kb_id}: {e}")
                        raise  # 事务将回滚

                # 4. 从 MinIO 删除文件
                try:
                    minio_files = await object_operations.list_files(prefix=f"{kb_name}/")
                    for file in minio_files:
                        await object_operations.delete_file(file['object_key'])
                    logger.info(f"Deleted files for knowledge base {kb_id} from MinIO.")
                except Exception as e:
                    logger.error(f"Failed to delete files for kb {kb_id} from MinIO: {e}")
                    raise  # 事务将回滚
                
                # 5. 从数据库删除文档和知识库记录
                await conn.execute("DELETE FROM mixrag_documents WHERE kb_id = $1", kb_id)
                result = await conn.execute("DELETE FROM mixrag_knowledge_base WHERE id = $1", kb_id)
                
                deleted_count = int(result.split()[-1])
                if deleted_count > 0:
                    logger.info(f"Deleted knowledge base: {kb_id}")
                    return True
                return False

    async def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        async with postgre_sql_client.pool.acquire() as conn:
            query = """
            SELECT 
                COUNT(DISTINCT kb.id) as total_knowledge_bases,
                COALESCE(SUM(kb.doc_count), 0) as total_documents,
                COALESCE(SUM(d.chunks_count), 0) as total_chunks,
                COALESCE(SUM(d.entities_count), 0) as total_entities,
                COALESCE(SUM(d.relationships_count), 0) as total_relationships,
                COALESCE(SUM(d.file_size), 0) as total_file_size,
                CASE 
                    WHEN COUNT(DISTINCT kb.id) > 0 
                    THEN COALESCE(SUM(kb.doc_count), 0)::float / COUNT(DISTINCT kb.id)
                    ELSE 0 
                END as avg_docs_per_kb
            FROM mixrag_knowledge_base kb
            LEFT JOIN mixrag_documents d ON kb.id = d.kb_id
            """
            row = await conn.fetchrow(query)
            
            if row:
                return dict(row)
            return {
                "total_knowledge_bases": 0,
                "total_documents": 0,
                "total_chunks": 0,
                "total_entities": 0,
                "total_relationships": 0,
                "total_file_size": 0,
                "avg_docs_per_kb": 0.0
            }

    async def search_knowledge_bases(
            self,
            query: str,
            page: int = 1,
            page_size: int = 20
    ) -> Dict[str, Any]:
        """搜索知识库"""
        return await self.list_knowledge_bases(
            page=page,
            page_size=page_size,
            search_query=query,
            sort_by="create_time",
            sort_order="desc"
        )

    async def update_doc_count(self, kb_id: str) -> bool:
        """更新知识库的文档数量"""
        async with postgre_sql_client.pool.acquire() as conn:
            query = """
            UPDATE mixrag_knowledge_base 
            SET doc_count = (
                SELECT COUNT(*) 
                FROM mixrag_documents 
                WHERE kb_id = $1
            )
            WHERE id = $1
            """
            result = await conn.execute(query, kb_id)
            
            # 检查是否更新成功
            updated_count = int(result.split()[-1])
            return updated_count > 0


# 创建全局实例
knowledge_base_operations = KnowledgeBaseOperations()