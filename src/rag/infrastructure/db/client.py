"""
PostgreSQL 数据库存储客户端

提供数据库连接池管理
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional

import asyncpg

from src.config import config
from src.rag.tools.logger import logger


class PostgreSQLClient:
    """PostgreSQL 数据库存储客户端"""

    def __init__(
            self,
            host: str = None,
            port: int = None,
            database: str = None,
            user: str = None,
            password: str = None,
            min_size: int = None,
            max_size: int = None,
            command_timeout: float = None,
            server_settings: Dict[str, str] = None
    ):
        """
        初始化 PostgreSQL 存储客户端
        
        Args:
            host: PostgreSQL 主机地址
            port: PostgreSQL 端口
            database: 数据库名称
            user: 用户名
            password: 密码
            dsn: 数据源名称（优先级最高）
            min_size: 连接池最小大小
            max_size: 连接池最大大小
            command_timeout: 命令超时时间（秒）
            server_settings: 服务器设置
        """
        # 从统一配置或参数获取配置
        db_config = config.database

        self.host = host or db_config.postgres_host
        self.port = port or db_config.postgres_port
        self.database = database or db_config.postgres_db
        self.user = user or db_config.postgres_user
        self.password = password or db_config.postgres_password

        # 连接池配置
        self.min_size = min_size or db_config.postgres_pool_size
        self.max_size = max_size or (db_config.postgres_pool_size + db_config.postgres_max_overflow)
        self.command_timeout = command_timeout or 60.0
        self.server_settings = server_settings or {}

        # 使用连接参数
        self.pool = None

    def _get_connection_info(self) -> str:
        """获取连接信息字符串"""
        return f"{self.host}:{self.port}/{self.database}"

    async def drop(self) -> Dict[str, str]:
        """删除所有相关表数据"""
        tables_to_clear = [
            "mixrag_pipeline_task",  # 先删除任务表（有外键依赖）
            "mixrag_pipeline",  # 再删除流水线表
            "mixrag_documents"  # 最后删除文档表
        ]

        deleted_counts = {}
        total_deleted = 0

        async with self.pool.acquire() as conn:
            # 逐个清空表
            for table_name in tables_to_clear:
                # 先查询表中的记录数
                count_query = f"SELECT COUNT(*) FROM {table_name}"
                count_before = await conn.fetchval(count_query)

                # 删除所有记录
                delete_query = f"DELETE FROM {table_name}"
                result = await conn.execute(delete_query)

                # 解析删除结果
                if result.startswith("DELETE "):
                    deleted_count = int(result.split(" ")[1])
                else:
                    deleted_count = count_before

                deleted_counts[table_name] = {
                    "count_before": count_before,
                    "deleted": deleted_count
                }
                total_deleted += deleted_count

                logger.info(f"Cleared table {table_name}: {deleted_count} records deleted")

        logger.info(f"PostgreSQL drop operation completed: {total_deleted} total records deleted")

        return {
            "status": "success",
            "message": f"Successfully cleared {len(tables_to_clear)} tables, {total_deleted} total records deleted",
            "tables": deleted_counts,
            "total_deleted": total_deleted
        }

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        async with self.pool.acquire() as conn:
            # 执行简单查询测试连接
            result = await conn.fetchval("SELECT 1")

            # 获取连接池状态
            pool_status = {
                "size": self.pool.get_size(),
                "min_size": self.pool.get_min_size(),
                "max_size": self.pool.get_max_size(),
                "free_connections": self.pool.get_idle_size()
            }

            return {
                "status": "healthy",
                "message": "PostgreSQL connection is healthy",
                "database": self.database,
                "connection_info": self._get_connection_info(),
                "pool_status": pool_status
            }

    async def initialize(self):
        """初始化 PostgreSQL 连接池"""
        self.pool = await asyncpg.create_pool(
            host=self.host,
            port=self.port,
            database=self.database,
            user=self.user,
            password=self.password,
            min_size=self.min_size,
            max_size=self.max_size,
            command_timeout=self.command_timeout,
            server_settings=self.server_settings
        )

        logger.info(f"PostgreSQL connection pool created: {self._get_connection_info()}")

    async def finalize(self):
        """清理 PostgreSQL 连接池"""
        await self.pool.close()

# 请使用单例模式 不要额外实例化
postgre_sql_client = PostgreSQLClient()
