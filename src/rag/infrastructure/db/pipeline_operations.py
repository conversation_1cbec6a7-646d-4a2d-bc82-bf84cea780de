"""
任务管理数据库操作

提供任务和流水线的数据库操作接口
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List

from .client import postgre_sql_client
from src.rag.pipeline.models.data_models import Pipeline, PipelineTask
from src.rag.pipeline.models.enums import TaskStatus, PipelineStatus, TaskType
from src.rag.tools import logger


class PipelineOperations:

    async def update_task_status(
            self,
            task_id: str,
            status: TaskStatus,
            error_message: Optional[str] = None,
    ) -> bool:
        """更新任务状态（支持普通任务和流水线任务）"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 构建更新字段
            update_fields = ["status = $2"]
            params = [task_id, status.value]
            param_count = 2

            if status == TaskStatus.RUNNING:
                param_count += 1
                update_fields.append(f"started_at = ${param_count}")
                params.append(datetime.utcnow())
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                param_count += 1
                update_fields.append(f"completed_at = ${param_count}")
                params.append(datetime.utcnow())

            if error_message:
                param_count += 1
                update_fields.append(f"error_message = ${param_count}")
                params.append(error_message)

            sql = f"UPDATE mixrag_pipeline_task SET {', '.join(update_fields)} WHERE id = $1"
            result = await conn.execute(sql, *params)

            if result != "UPDATE 0":
                logger.debug(f"成功更新流水线任务 {task_id} 的状态为 {status.value}")

            return result != "UPDATE 0"

    async def mark_dependent_tasks_failed(self, failed_task_id: str) -> int:
        """标记依赖于失败任务的所有任务为失败状态"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 对于流水线任务，查找所有依赖于失败任务的任务
            dependent_tasks = await conn.fetch("""
                                               SELECT id
                                               FROM mixrag_pipeline_task
                                               WHERE pre_task_id = $1
                                                 AND status = 'pending'
                                               """, failed_task_id)

            count = 0
            for task in dependent_tasks:
                success = await self.update_task_status(
                    task['id'],
                    TaskStatus.FAILED,
                    error_message=f"依赖任务 {failed_task_id} 失败"
                )
                if success:
                    count += 1
                    # 递归标记依赖于当前任务的任务
                    count += await self.mark_dependent_tasks_failed(task['id'])

            return count

    async def add_task_to_pipeline(self, pipeline_id: str, task: PipelineTask) -> bool:
        """添加任务到流水线"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 获取当前流水线中任务的最大 step_order
            max_step_order = await conn.fetchval("""
                                                 SELECT COALESCE(MAX(step_order), -1) + 1
                                                 FROM mixrag_pipeline_task
                                                 WHERE pipeline_id = $1
                                                 """, pipeline_id)

            # 插入任务记录
            await conn.execute("""
                               INSERT INTO mixrag_pipeline_task (id, pipeline_id, task_type, status,
                                                                 input_data, output_data, error_message,
                                                                 retry_count, max_retries, step_order,
                                                                 dependencies, created_at, started_at, completed_at)
                               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                               """,
                               task.id,
                               pipeline_id,
                               task.task_type.value,
                               task.status.value,
                               json.dumps(task.parameters) if task.parameters else None,
                               None,  # output_data 初始为空
                               task.error_message,
                               task.retry_count,
                               task.max_retries,
                               max_step_order if hasattr(task, 'step_order') else max_step_order,
                               json.dumps(task.dependencies) if task.dependencies else None,
                               task.created_at,
                               task.started_at,
                               task.completed_at
                               )

            logger.info(f"任务添加到流水线成功: {task.id} -> {pipeline_id}")
            return True

    async def get_pipelines(
        self,
        status: Optional[PipelineStatus] = None,
        created_by: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> List[Pipeline]:
        """获取流水线列表"""
        async with postgre_sql_client.pool.acquire() as conn:
            offset = (page - 1) * page_size

            # 构建查询条件
            conditions = []
            params = []
            param_count = 0

            if status:
                param_count += 1
                conditions.append(f"status = ${param_count}")
                params.append(status.value)

            if created_by:
                param_count += 1
                conditions.append(f"created_by = ${param_count}")
                params.append(created_by)

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            param_count += 1
            limit_param = f"${param_count}"
            params.append(page_size)

            param_count += 1
            offset_param = f"${param_count}"
            params.append(offset)

            query = f"""
            SELECT * FROM mixrag_pipeline
            {where_clause}
            ORDER BY created_at DESC
            LIMIT {limit_param} OFFSET {offset_param}
            """

            rows = await conn.fetch(query, *params)

            pipelines = []
            for row in rows:
                # 获取关联的任务 ID
                task_ids = []
                try:
                    task_rows = await conn.fetch(
                        "SELECT id FROM mixrag_pipeline_task WHERE pipeline_id = $1 ORDER BY step_order",
                        row['id']
                    )
                    task_ids = [task_row['id'] for task_row in task_rows]
                except Exception:
                    pass

                pipeline = Pipeline(
                    id=row['id'],
                    name=row['name'],
                    description=row['description'],
                    status=PipelineStatus(row['status']),
                    created_at=row['created_at'],
                    started_at=row['started_at'],
                    completed_at=row['completed_at'],
                    created_by=row.get('created_by'),  # 添加 created_by 字段
                    doc_id=row.get('doc_id'),  # 添加文档ID字段
                )
                pipelines.append(pipeline)

            return pipelines

    async def count_pipelines(self, filters: Dict[str, Any] = None) -> int:
        """统计流水线数量"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 构建查询条件
            conditions = []
            params = []
            param_count = 0

            if filters:
                if filters.get('status'):
                    param_count += 1
                    conditions.append(f"status = ${param_count}")
                    params.append(filters['status'])

                if filters.get('created_by'):
                    param_count += 1
                    conditions.append(f"created_by = ${param_count}")
                    params.append(filters['created_by'])

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"SELECT COUNT(*) FROM mixrag_pipeline{where_clause}"
            count = await conn.fetchval(query, *params)
            return count or 0

    async def get_pipelines_by_doc_id(self, doc_id: str) -> List[PipelineTask]:
        """根据文档ID获取相关的流水线列表"""
        async with postgre_sql_client.pool.acquire() as conn:
            rows = await conn.fetch("""
                                    SELECT id,
                                           name,
                                           description,
                                           status,
                                           created_at,
                                           started_at,
                                           completed_at,
                                           created_by,
                                           error_message,
                                           doc_id
                                    FROM mixrag_pipeline
                                    WHERE doc_id = $1
                                    ORDER BY created_at DESC
                                    """, doc_id)

            pipelines = []
            for row in rows:
                pipeline_data = {
                    'pipeline_id': row['id'],
                    'name': row['name'],
                    'description': row['description'],
                    'status': row['status'],
                    'created_at': row['created_at'],
                    'started_at': row['started_at'],
                    'completed_at': row['completed_at'],
                    'created_by': row.get('created_by'),
                    'doc_id': row.get('doc_id'),
                    'error_message': row['error_message']
                }
                pipelines.append(pipeline_data)

            logger.debug(f"找到 {len(pipelines)} 个与文档 {doc_id} 相关的流水线")
            return pipelines

    async def delete_pipeline(self, pipeline_id: str) -> bool:
        """删除流水线及其所有任务"""
        async with postgre_sql_client.pool.acquire() as conn:
            async with conn.transaction():
                # 首先删除流水线任务
                await conn.execute("""
                                   DELETE
                                   FROM mixrag_pipeline_task
                                   WHERE pipeline_id = $1
                                   """, pipeline_id)

                # 然后删除流水线记录
                result = await conn.execute("""
                                            DELETE
                                            FROM mixrag_pipeline
                                            WHERE id = $1
                                            """, pipeline_id)

                # 检查是否删除了记录
                if result == "DELETE 0":
                    logger.warning(f"流水线 {pipeline_id} 不存在")
                    return False

                logger.info(f"流水线 {pipeline_id} 及其任务删除成功")
                return True

    async def save_pipeline(self, pipeline: Pipeline) -> bool:
        """保存流水线"""
        async with postgre_sql_client.pool.acquire() as conn:
            query = """
                    INSERT INTO mixrag_pipeline (id, name, description, status,
                                                 created_at, started_at, completed_at,
                                                 created_by, error_message, doc_id)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) ON CONFLICT (id) DO
                    UPDATE SET
                        name = EXCLUDED.name,
                        description = EXCLUDED.description,
                        status = EXCLUDED.status,
                        started_at = EXCLUDED.started_at,
                        completed_at = EXCLUDED.completed_at,
                        created_by = EXCLUDED.created_by,
                        error_message = EXCLUDED.error_message,
                        doc_id = EXCLUDED.doc_id \
                    """

            await conn.execute(
                query,
                pipeline.id,
                pipeline.name,
                pipeline.description,
                pipeline.status.value,
                pipeline.created_at,
                pipeline.started_at,
                pipeline.completed_at,
                getattr(pipeline, 'created_by', None),  # 安全获取 created_by
                pipeline.error_message,
                pipeline.doc_id
            )

            logger.debug(f"流水线 {pipeline.id} 保存成功")
            return True

    async def get_pipeline(self, pipeline_id: str) -> Optional[Pipeline]:
        """获取流水线"""
        async with postgre_sql_client.pool.acquire() as conn:
            query = "SELECT * FROM mixrag_pipeline WHERE id = $1"
            row = await conn.fetchrow(query, pipeline_id)

            if row:
                # 获取关联的任务 ID
                task_ids = []
                try:
                    task_rows = await conn.fetch(
                        "SELECT id FROM mixrag_pipeline_task WHERE pipeline_id = $1 ORDER BY step_order",
                        row['id']
                    )
                    task_ids = [task_row['id'] for task_row in task_rows]
                except Exception:
                    pass

                return Pipeline(
                    id=row['id'],
                    name=row['name'],
                    description=row['description'],
                    status=PipelineStatus(row['status']),
                    created_at=row['created_at'],
                    started_at=row['started_at'],
                    completed_at=row['completed_at'],
                    created_by=row.get('created_by'),  # 添加 created_by 字段
                    doc_id=row.get('doc_id'),  # 添加文档ID字段
                )
            return None

    async def get_task_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 任务统计
            task_stats = await conn.fetchrow("""
                                             SELECT COUNT(*)                                             as total_tasks,
                                                    COUNT(CASE WHEN status = 'pending' THEN 1 END)       as pending_tasks,
                                                    COUNT(CASE WHEN status = 'running' THEN 1 END)       as running_tasks,
                                                    COUNT(CASE WHEN status = 'completed' THEN 1 END)     as completed_tasks,
                                                    COUNT(CASE WHEN status = 'failed' THEN 1 END)        as failed_tasks,
                                                    AVG(EXTRACT(EPOCH FROM (completed_at - started_at))) as avg_execution_time
                                             FROM mixrag_pipeline_task
                                             WHERE started_at IS NOT NULL
                                             """)

            # 流水线统计
            pipeline_stats = await conn.fetchrow("""
                                                 SELECT COUNT(*)                                         as total_pipelines,
                                                        COUNT(CASE WHEN status = 'pending' THEN 1 END)   as pending_pipelines,
                                                        COUNT(CASE WHEN status = 'running' THEN 1 END)   as running_pipelines,
                                                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_pipelines,
                                                        COUNT(CASE WHEN status = 'failed' THEN 1 END)    as failed_pipelines
                                                 FROM mixrag_pipeline
                                                 """)

            return {
                "tasks": {
                    "total": task_stats["total_tasks"] or 0,
                    "pending": task_stats["pending_tasks"] or 0,
                    "running": task_stats["running_tasks"] or 0,
                    "completed": task_stats["completed_tasks"] or 0,
                    "failed": task_stats["failed_tasks"] or 0,
                    "avg_execution_time": float(task_stats["avg_execution_time"] or 0)
                },
                "pipelines": {
                    "total": pipeline_stats["total_pipelines"] or 0,
                    "pending": pipeline_stats["pending_pipelines"] or 0,
                    "running": pipeline_stats["running_pipelines"] or 0,
                    "completed": pipeline_stats["completed_pipelines"] or 0,
                    "failed": pipeline_stats["failed_pipelines"] or 0
                }
            }

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息（别名方法）"""
        return await self.get_task_stats()

    async def get_pipeline_tasks(self, pipeline_id: str) -> List[PipelineTask]:
        """获取流水线中的任务列表"""
        async with postgre_sql_client.pool.acquire() as conn:
            query = """
                    SELECT *
                    FROM mixrag_pipeline_task
                    WHERE pipeline_id = $1
                    ORDER BY step_order ASC \
                    """
            rows = await conn.fetch(query, pipeline_id)

            tasks = []
            for row in rows:
                # 解析dependencies字段
                dependencies = []
                if row["dependencies"]:
                    try:
                        dependencies = json.loads(row["dependencies"])
                    except (json.JSONDecodeError, TypeError):
                        dependencies = []

                # 解析parameters字段
                parameters = {}
                if row["input_data"]:
                    try:
                        parameters = json.loads(row["input_data"])
                    except (json.JSONDecodeError, TypeError):
                        parameters = {}

                # 解析output_data字段
                output_data = {}
                if row["output_data"]:
                    try:
                        output_data = json.loads(row["output_data"])
                    except (json.JSONDecodeError, TypeError):
                        output_data = {}

                task = PipelineTask(**{
                    "id": row["id"],
                    "pipeline_id": row["pipeline_id"],
                    "task_type": TaskType(row["task_type"]),
                    "status": TaskStatus(row["status"]),
                    "parameters": parameters,
                    "output_data": output_data,
                    "error_message": row["error_message"],
                    "retry_count": row["retry_count"],
                    "max_retries": row["max_retries"],
                    "step_order": row["step_order"],
                    "dependencies": dependencies,
                    "created_at": row["created_at"],
                    "started_at": row["started_at"],
                    "completed_at": row["completed_at"]
                })
                tasks.append(task)

            return tasks

    async def create_pipeline(self, pipeline: Pipeline) -> bool:
        """
        创建新的流水线记录

        Args:
            pipeline: 流水线对象

        Returns:
            bool: 创建是否成功
        """
        async with postgre_sql_client.pool.acquire() as conn:
            # 插入流水线记录（不包含input_data和output_data列）
            await conn.execute("""
                               INSERT INTO mixrag_pipeline (id, name, description, status, created_by,
                                                            created_at, started_at, completed_at,
                                                            error_message, doc_id)
                               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                               """,
                               pipeline.id,
                               pipeline.name,
                               pipeline.description,
                               pipeline.status.value,
                               pipeline.created_by,
                               pipeline.created_at,
                               pipeline.started_at,
                               pipeline.completed_at,
                               pipeline.error_message,
                               pipeline.doc_id
                               )

            logger.info(f"流水线创建成功: {pipeline.id}")
            return True

    async def update_pipeline_status(
            self,
            pipeline_id: str,
            status: PipelineStatus,
            started_at: Optional[datetime] = None,
            completed_at: Optional[datetime] = None,
            error_message: Optional[str] = None,
    ) -> bool:
        """
        更新流水线状态

        Args:
            pipeline_id: 流水线ID
            status: 新状态
            started_at: 开始时间
            completed_at: 完成时间

        Returns:
            bool: 更新是否成功
        """
        async with postgre_sql_client.pool.acquire() as conn:
            # 构建更新SQL
            update_fields = ["status = $2"]
            params = [pipeline_id, status.value]
            param_count = 2

            if started_at is not None:
                param_count += 1
                update_fields.append(f"started_at = ${param_count}")
                params.append(started_at)

            if completed_at is not None:
                param_count += 1
                update_fields.append(f"completed_at = ${param_count}")
                params.append(completed_at)

            if error_message is not None:
                param_count += 1
                update_fields.append(f"error_message = ${param_count}")
                params.append(error_message)

            sql = f"UPDATE mixrag_pipeline SET {', '.join(update_fields)} WHERE id = $1"

            await conn.execute(sql, *params)
            logger.info(f"流水线状态更新成功: {pipeline_id} -> {status.value}")
            return True

    async def update_task_output_data(self, task_id: str, output_data: dict) -> bool:
        """更新任务的输出数据"""
        async with postgre_sql_client.pool.acquire() as conn:
            await conn.execute("""
                               UPDATE mixrag_pipeline_task
                               SET output_data = $1
                               WHERE id = $2
                               """,
                               json.dumps(output_data) if output_data else None,
                               task_id)
            logger.info(f"任务输出数据更新成功: {task_id}")
            return True

    async def get_pending_pipelines(self, limit: int = 10) -> List[Pipeline]:
        """获取待处理的流水线"""
        return await self.get_pipelines(
            status=PipelineStatus.PENDING,
            page=1,
            page_size=limit
        )


# 全局任务数据库实例
pipeline_operations = PipelineOperations()
