"""
Neo4j 图数据库连接管理

提供 Neo4j 数据库连接管理功能
"""

from typing import Dict, Any

from neo4j import (
    AsyncGraphDatabase,
)

# 全局连接管理器实例
from src.config import config
from src.rag.tools.logger import logger


class Neo4jConnectionManager:
    """Neo4j 连接管理器"""

    def __init__(self):
        # 配置参数
        self.uri = config.database.neo4j_uri
        self.username = config.database.neo4j_user
        self.password = config.database.neo4j_password
        self.database = None  # 使用默认数据库
        self.max_connection_lifetime = None
        self.max_connection_pool_size = None
        self.connection_acquisition_timeout = None

        self.driver = None
        self._initialized = False

    async def initialize(self):
        """初始化连接"""
        if self._initialized:
            return

        self.driver = AsyncGraphDatabase.driver(
            self.uri,
            auth=(self.username, self.password),
            max_connection_lifetime=self.max_connection_lifetime,
            max_connection_pool_size=self.max_connection_pool_size,
            connection_acquisition_timeout=self.connection_acquisition_timeout
        )

        # 测试连接
        await self.verify_connectivity()

        self._initialized = True
        logger.info(f"Neo4j 连接初始化成功: {self.uri}")

    async def finalize(self):
        """关闭连接"""
        if self.driver:
            await self.driver.close()
            self.driver = None
            self._initialized = False
            logger.info("Neo4j 连接已关闭")

    async def verify_connectivity(self):
        """验证连接"""
        await self.driver.verify_connectivity()
        logger.info("Neo4j 连接验证成功")

    async def execute_query(
            self,
            query: str,
            parameters: Dict[str, Any] = None,
            database: str = None
    ) -> list:
        """
        执行查询

        Args:
            query: Cypher 查询语句
            parameters: 查询参数
            database: 数据库名称

        Returns:
            查询结果列表
        """
        if not self._initialized:
            await self.initialize()

        db_name = database or self.database
        params = parameters or {}

        async with self.driver.session(database=db_name) as session:
            result = await session.run(query, params)
            records = await result.data()
            return records

    async def execute_write_query(
            self,
            query: str,
            parameters: Dict[str, Any] = None,
            database: str = None
    ) -> dict:
        """
        执行写入查询

        Args:
            query: Cypher 写入语句
            parameters: 查询参数
            database: 数据库名称

        Returns:
            查询结果摘要
        """
        if not self._initialized:
            await self.initialize()

        db_name = database or self.database
        params = parameters or {}

        async with self.driver.session(database=db_name) as session:
            result = await session.run(query, params)
            summary = await result.consume()
            return {
                "nodes_created": summary.counters.nodes_created,
                "nodes_deleted": summary.counters.nodes_deleted,
                "relationships_created": summary.counters.relationships_created,
                "relationships_deleted": summary.counters.relationships_deleted,
                "properties_set": summary.counters.properties_set,
                "labels_added": summary.counters.labels_added,
                "labels_removed": summary.counters.labels_removed,
                "indexes_added": summary.counters.indexes_added,
                "indexes_removed": summary.counters.indexes_removed,
                "constraints_added": summary.counters.constraints_added,
                "constraints_removed": summary.counters.constraints_removed
            }

    async def execute_transaction(self, queries: list, database: str = None) -> list:
        """
        执行事务

        Args:
            queries: 查询列表，每个元素为 (query, parameters) 元组
            database: 数据库名称

        Returns:
            所有查询的结果列表
        """
        if not self._initialized:
            await self.initialize()

        db_name = database or self.database

        async def _execute_transaction(tx):
            """执行事务内的操作"""
            results = []
            for query, parameters in queries:
                params = parameters or {}
                result = await tx.run(query, params)
                records = await result.data()
                results.append(records)
            return results

        async with self.driver.session(database=db_name) as session:
            results = await session.execute_write(_execute_transaction)

        return results

    async def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        query = """
        CALL dbms.components() YIELD name, versions, edition
        RETURN name, versions, edition
        """
        result = await self.execute_query(query)

        # 获取节点和关系统计
        stats_query = """
        MATCH (n)
        RETURN count(n) as node_count
        UNION ALL
        MATCH ()-[r]->()
        RETURN count(r) as relationship_count
        """
        stats_result = await self.execute_query(stats_query)

        return {
            "components": result,
            "statistics": stats_result,
            "database": self.database,
        }

    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "uri": self.uri,
            "database": self.database,
            "initialized": self._initialized,
            "max_connection_lifetime": self.max_connection_lifetime,
            "max_connection_pool_size": self.max_connection_pool_size,
            "connection_acquisition_timeout": self.connection_acquisition_timeout
        }


# 初始化全局连接管理器
neo4j_connection_manager = Neo4jConnectionManager()
