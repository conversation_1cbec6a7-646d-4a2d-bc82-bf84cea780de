"""
Neo4j 图操作管理器

提供图数据的基础 CRUD 操作
"""
from typing import Dict, List, Optional, Any

from src.rag.tools.logger import logger


class Neo4jGraphOperations:
    """Neo4j 图操作管理器"""

    def set_connection_manager(self, connection_manager):
        """
        初始化图操作管理器
        
        Args:
            connection_manager: 连接管理器实例
        """
        self.connection_manager = connection_manager

    def _get_node_label(self, node_type: str = "Entity") -> str:
        """获取节点标签"""
        return f"{node_type}"

    def _get_relationship_type(self, rel_type: str) -> str:
        """获取关系类型"""
        return f"{rel_type}"

    def _clean_label_name(self, label: str) -> str:
        """
        清理标签名称，确保符合Neo4j标签命名规范

        Args:
            label: 原始标签名称

        Returns:
            清理后的标签名称
        """
        import re
        if not label:
            return ""

        # 移除特殊字符，只保留字母、数字、下划线和中文字符
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '_', label.strip())

        # 确保不以数字开头
        if cleaned and cleaned[0].isdigit():
            cleaned = f"Label_{cleaned}"

        # 限制长度
        if len(cleaned) > 50:
            cleaned = cleaned[:50]

        return cleaned

    async def has_node(self, node_id: str) -> bool:
        """检查节点是否存在"""
        query = f"""
        MATCH (n:{self._get_node_label()})
        WHERE n.id = $node_id
        RETURN count(n) > 0 as exists
        """
        result = await self.connection_manager.execute_query(query, {"node_id": node_id})
        return result[0]["exists"] if result else False

    async def has_edge(self, source_node_id: str, target_node_id: str) -> bool:
        """检查边是否存在"""
        query = f"""
        MATCH (s:{self._get_node_label()})-[r]-(t:{self._get_node_label()})
        WHERE s.id = $source_id AND t.id = $target_id
        RETURN count(r) > 0 as exists
        """
        result = await self.connection_manager.execute_query(query, {
            "source_id": source_node_id,
            "target_id": target_node_id
        })
        return result[0]["exists"] if result else False

    async def get_node(self, node_id: str) -> Optional[Dict[str, Any]]:
        """获取节点"""
        query = f"""
        MATCH (n:{self._get_node_label()})
        WHERE n.id = $node_id
        RETURN n
        """
        result = await self.connection_manager.execute_query(query, {"node_id": node_id})

        if result:
            node_data = dict(result[0]["n"])
            return node_data
        return None

    async def node_degree(self, node_id: str) -> int:
        """获取节点度数"""
        query = f"""
        MATCH (n:{self._get_node_label()})-[r]-()
        WHERE n.id = $node_id
        RETURN count(r) as degree
        """
        result = await self.connection_manager.execute_query(query, {"node_id": node_id})
        return result[0]["degree"] if result else 0

    async def edge_degree(self, src_id: str, tgt_id: str) -> int:
        """获取边度数（两个节点之间的连接数）"""
        query = f"""
        MATCH (s:{self._get_node_label()})-[r]-(t:{self._get_node_label()})
        WHERE s.id = $src_id AND t.id = $tgt_id
        RETURN count(r) as degree
        """
        result = await self.connection_manager.execute_query(query, {
            "src_id": src_id,
            "tgt_id": tgt_id
        })
        return result[0]["degree"] if result else 0

    async def get_edge(self, source_node_id: str, target_node_id: str) -> Optional[Dict[str, Any]]:
        """获取边"""
        query = f"""
        MATCH (s:{self._get_node_label()})-[r]-(t:{self._get_node_label()})
        WHERE s.id = $source_id AND t.id = $target_id
        RETURN r, type(r) as relationship_type
        LIMIT 1
        """
        result = await self.connection_manager.execute_query(query, {
            "source_id": source_node_id,
            "target_id": target_node_id
        })

        if result:
            edge_data = dict(result[0]["r"])
            edge_data["relationship_type"] = result[0]["relationship_type"]
            return edge_data
        return None

    async def get_node_edges(self, source_node_id: str) -> List[Dict[str, Any]]:
        """获取节点的所有边"""
        query = f"""
        MATCH (s:{self._get_node_label()})-[r]-(t:{self._get_node_label()})
        WHERE s.id = $source_id
        RETURN r, type(r) as relationship_type, t.id as target_id
        """
        result = await self.connection_manager.execute_query(query, {"source_id": source_node_id})

        edges = []
        for record in result:
            edge_data = dict(record["r"])
            edge_data.update({
                "relationship_type": record["relationship_type"],
                "target_id": record["target_id"]
            })
            edges.append(edge_data)

        return edges

    def _flatten_properties(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """展平嵌套的属性字典，将复杂对象转换为Neo4j支持的原始类型"""
        flattened = {}
        
        for key, value in data.items():
            if value is None:
                continue  # 跳过None值
            elif isinstance(value, dict):
                # 如果是字典，展平其内容，使用prefix_key的形式
                for sub_key, sub_value in value.items():
                    if sub_value is not None:
                        flattened_key = f"{key}_{sub_key}" if key != "properties" else sub_key
                        if isinstance(sub_value, (str, int, float, bool)):
                            flattened[flattened_key] = sub_value
                        elif isinstance(sub_value, list):
                            # 对于列表，转换为字符串
                            flattened[flattened_key] = str(sub_value)
                        else:
                            # 其他复杂类型转换为字符串
                            flattened[flattened_key] = str(sub_value)
            elif isinstance(value, list):
                # Neo4j支持数组，但只能是原始类型的数组
                if all(isinstance(item, (str, int, float, bool)) for item in value):
                    flattened[key] = value
                else:
                    # 包含复杂对象的列表转换为字符串
                    flattened[key] = str(value)
            elif isinstance(value, (str, int, float, bool)):
                # 原始类型直接保留
                flattened[key] = value
            else:
                # 其他类型转换为字符串
                flattened[key] = str(value)
        
        return flattened

    async def upsert_node(self, node_id: str, node_data: Dict[str, Any]) -> None:
        """插入或更新节点"""
        # 展平属性，处理嵌套字典
        flattened_data = self._flatten_properties(node_data)

        # 准备节点属性
        properties = {
            "id": node_id,
            **flattened_data
        }

        # 移除空值
        properties = {k: v for k, v in properties.items() if v is not None}

        # 构建属性设置语句
        set_clauses = []
        for key in properties.keys():
            set_clauses.append(f"n.{key} = $props.{key}")

        query = f"""
        MERGE (n:{self._get_node_label()} {{id: $node_id}})
        SET {', '.join(set_clauses)}
        RETURN n
        """

        await self.connection_manager.execute_write_query(query, {
            "node_id": node_id,
            "props": properties
        })

        logger.debug(f"节点 {node_id} 更新成功")

    async def upsert_edge(
        self,
        source_node_id: str,
        target_node_id: str,
        edge_data: Dict[str, Any]
    ) -> None:
        """插入或更新边"""
        # 获取关系类型
        relationship_type = edge_data.get("relationship_type", "RELATED")
        rel_type = self._get_relationship_type(relationship_type)

        # 展平属性，处理嵌套字典
        edge_data_copy = {k: v for k, v in edge_data.items() if k != "relationship_type"}
        flattened_properties = self._flatten_properties(edge_data_copy)

        # 移除空值
        properties = {k: v for k, v in flattened_properties.items() if v is not None}

        # 构建属性设置语句
        if properties:
            set_clauses = []
            for key in properties.keys():
                set_clauses.append(f"r.{key} = $props.{key}")
            set_clause = f"SET {', '.join(set_clauses)}"
        else:
            set_clause = ""

        query = f"""
        MATCH (s:{self._get_node_label()} {{id: $source_id}})
        MATCH (t:{self._get_node_label()} {{id: $target_id}})
        MERGE (s)-[r:{rel_type}]->(t)
        {set_clause}
        RETURN r
        """

        await self.connection_manager.execute_write_query(query, {
            "source_id": source_node_id,
            "target_id": target_node_id,
            "props": properties
        })

        logger.debug(f"边 {source_node_id}->{target_node_id} 更新成功")

    async def upsert_document_graph(
        self,
        doc_id: str,
        nodes: List[Dict[str, Any]],
        edges: List[Dict[str, Any]],
    ) -> None:
        """
        在事务中插入或更新与文档关联的节点和边。
        1. 创建或更新 Document 节点。
        2. 为每个节点创建或更新，并附加 doc_id 属性。
        3. 创建 Document 节点与子节点之间的 :CONTAINS 关系。
        4. 创建或更新边。
        """
        logger.info(f"开始为文档 {doc_id} 更新图数据...")

        async def _execute_transaction(tx):
            """执行事务内的操作"""
            # 1. 创建 Document 节点
            await tx.run("MERGE (d:Document {doc_id: $doc_id})", {"doc_id": doc_id})

            # 2. Upsert nodes, add doc_id, and link to document
            for node_data in nodes:
                node_id = node_data.get("entity_id") or node_data.get("id")
                if not node_id:
                    continue

                # 获取节点类型和业务标签
                node_type = node_data.get("entity_type", "Entity")
                business_label = node_data.get("label", "")

                # 构建标签列表：基础标签 + 业务标签
                labels = [self._get_node_label(node_type)]
                if business_label and business_label.strip():
                    # 清理标签名称，确保符合Neo4j标签命名规范
                    clean_label = self._clean_label_name(business_label)
                    if clean_label:
                        labels.append(clean_label)

                # Flatten properties and add doc_id
                properties = self._flatten_properties(node_data)
                properties["doc_id"] = doc_id
                properties["id"] = node_id

                # 构建标签字符串
                labels_str = ":".join(labels)

                # Upsert node with multiple labels
                set_clauses = [f"n.{key} = $props.{key}" for key in properties.keys()]
                query = f"""
                MERGE (n:{labels_str} {{id: $node_id}})
                SET {', '.join(set_clauses)}
                """
                await tx.run(query, {"node_id": node_id, "props": properties})

                # Link to document
                link_query = """
                MATCH (d:Document {doc_id: $doc_id})
                MATCH (n {id: $node_id})
                MERGE (d)-[:CONTAINS]->(n)
                """
                await tx.run(link_query, {"doc_id": doc_id, "node_id": node_id})

            # 3. Upsert edges
            for edge_data in edges:
                source_id = edge_data.get("source_id")
                target_id = edge_data.get("target_id")
                if not source_id or not target_id:
                    logger.warning(f"跳过边：缺少source_id或target_id - {edge_data}")
                    continue

                rel_type_str = edge_data.get("relationship_type", "RELATED")
                rel_type = self._get_relationship_type(rel_type_str)

                edge_props = {k: v for k, v in edge_data.items() if
                              k not in ["source_id", "target_id", "relationship_type"]}
                properties = self._flatten_properties(edge_props)

                set_clause = ""
                if properties:
                    set_clauses = [f"r.{key} = $props.{key}" for key in properties.keys()]
                    set_clause = f"SET {', '.join(set_clauses)}"

                query = f"""
                MATCH (s {{id: $source_id}})
                MATCH (t {{id: $target_id}})
                MERGE (s)-[r:{rel_type}]->(t)
                {set_clause}
                """
                await tx.run(query, {"source_id": source_id, "target_id": target_id, "props": properties})
                logger.debug(f"成功创建边: {source_id} -> {target_id} ({rel_type})")

            return len(nodes), len(edges)

        # 使用 session.execute_write() 方法来处理写入事务
        async with self.connection_manager.driver.session(database=self.connection_manager.database) as session:
            node_count, edge_count = await session.execute_write(_execute_transaction)
            logger.info(f"成功为文档 {doc_id} 更新了 {node_count} 个节点和 {edge_count} 条边。")

    async def delete_node(self, node_id: str) -> None:
        """删除节点"""
        query = f"""
        MATCH (n:{self._get_node_label()} {{id: $node_id}})
        DETACH DELETE n
        """

        result = await self.connection_manager.execute_write_query(query, {"node_id": node_id})

        if result["nodes_deleted"] > 0:
            logger.debug(f"节点 {node_id} 删除成功")
        else:
            logger.warning(f"节点 {node_id} 不存在")

    async def delete_edge(self, source_node_id: str, target_node_id: str) -> None:
        """删除边"""
        query = f"""
        MATCH (s:{self._get_node_label()} {{id: $source_id}})-[r]-(t:{self._get_node_label()} {{id: $target_id}})
        DELETE r
        """

        result = await self.connection_manager.execute_write_query(query, {
            "source_id": source_node_id,
            "target_id": target_node_id
        })

        if result["relationships_deleted"] > 0:
            logger.debug(f"边 {source_node_id}-{target_node_id} 删除成功")
        else:
            logger.warning(f"边 {source_node_id}-{target_node_id} 不存在")

    # 高级查询方法
    async def get_all_nodes(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取所有节点"""
        query = f"""
        MATCH (n:{self._get_node_label()})
        RETURN n
        LIMIT {limit}
        """
        result = await self.connection_manager.execute_query(query)

        nodes = []
        for record in result:
            node_data = dict(record["n"])
            nodes.append(node_data)

        return nodes

    async def get_all_edges(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取所有边"""
        query = f"""
        MATCH (s:{self._get_node_label()})-[r]-(t:{self._get_node_label()})
        RETURN s.id as source_id, t.id as target_id, r, type(r) as relationship_type
        LIMIT {limit}
        """
        result = await self.connection_manager.execute_query(query)

        edges = []
        for record in result:
            # 安全地处理关系数据
            edge_data = {}
            if record["r"]:
                try:
                    edge_data = dict(record["r"])
                except:
                    edge_data = {}

            edge_data.update({
                "source_id": record["source_id"],
                "target_id": record["target_id"],
                "relationship_type": record["relationship_type"]
            })
            edges.append(edge_data)

        return edges

    async def search_nodes_by_property(
            self,
            property_name: str,
            property_value: Any,
            limit: int = 100
    ) -> List[Dict[str, Any]]:
        """根据属性搜索节点"""
        query = f"""
        MATCH (n:{self._get_node_label()})
        WHERE n.{property_name} = $value
        RETURN n
        LIMIT {limit}
        """
        result = await self.connection_manager.execute_query(query, {"value": property_value})

        nodes = []
        for record in result:
            node_data = dict(record["n"])
            nodes.append(node_data)

        return nodes

    async def get_neighbors(self, node_id: str, depth: int = 1) -> List[Dict[str, Any]]:
        """获取节点的邻居"""
        query = f"""
        MATCH (n:{self._get_node_label()} {{id: $node_id}})-[*1..{depth}]-(neighbor:{self._get_node_label()})
        RETURN DISTINCT neighbor
        """
        result = await self.connection_manager.execute_query(query, {"node_id": node_id})

        neighbors = []
        for record in result:
            neighbor_data = dict(record["neighbor"])
            neighbors.append(neighbor_data)

        return neighbors

    async def get_graph_stats(self) -> Dict[str, Any]:
        """获取图统计信息"""
        # 分别查询节点和关系数量
        node_query = f"""
        MATCH (n:{self._get_node_label()})
        RETURN count(n) as count
        """
        node_result = await self.connection_manager.execute_query(node_query)
        node_count = node_result[0]["count"] if node_result else 0

        relationship_query = f"""
        MATCH (:{self._get_node_label()})-[r]->(:{self._get_node_label()})
        RETURN count(r) as count
        """
        rel_result = await self.connection_manager.execute_query(relationship_query)
        relationship_count = rel_result[0]["count"] if rel_result else 0

        return {
            "node_count": node_count,
            "relationship_count": relationship_count
        }

    async def get_nodes_paginated(
            self,
            page: int = 1,
            page_size: int = 20,
            node_type: Optional[str] = None,
            property_filters: Optional[Dict[str, Any]] = None,
            search_text: Optional[str] = None,
            sort_by: str = "id",
            sort_order: str = "asc"
    ) -> Dict[str, Any]:
        """分页查询节点"""
        # 构建WHERE条件
        where_conditions = []
        params = {}

        # 节点类型筛选
        if node_type:
            where_conditions.append("n.type = $node_type")
            params["node_type"] = node_type

        # 属性筛选
        if property_filters:
            for key, value in property_filters.items():
                param_name = f"prop_{key}"
                where_conditions.append(f"n.{key} = ${param_name}")
                params[param_name] = value

        # 文本搜索
        if search_text:
            where_conditions.append("(n.name CONTAINS $search_text OR n.id CONTAINS $search_text)")
            params["search_text"] = search_text

        where_clause = " AND ".join(where_conditions) if where_conditions else "true"

        # 排序方向验证
        sort_order = sort_order.upper() if sort_order.upper() in ["ASC", "DESC"] else "ASC"

        # 计算总数
        count_query = f"""
        MATCH (n:{self._get_node_label()})
        WHERE {where_clause}
        RETURN count(n) as total
        """
        count_result = await self.connection_manager.execute_query(count_query, params)
        total = count_result[0]["total"] if count_result else 0

        # 计算分页参数
        offset = (page - 1) * page_size
        total_pages = (total + page_size - 1) // page_size

        # 查询数据
        data_query = f"""
        MATCH (n:{self._get_node_label()})
        WHERE {where_clause}
        RETURN n
        ORDER BY n.{sort_by} {sort_order}
        SKIP {offset}
        LIMIT {page_size}
        """
        data_result = await self.connection_manager.execute_query(data_query, params)

        nodes = []
        for record in data_result:
            node_data = dict(record["n"])
            nodes.append(node_data)

        return {
            "data": nodes,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        }

    async def get_edges_paginated(
            self,
            page: int = 1,
            page_size: int = 20,
            edge_type: Optional[str] = None,
            source_id: Optional[str] = None,
            target_id: Optional[str] = None,
            property_filters: Optional[Dict[str, Any]] = None,
            search_text: Optional[str] = None,
            sort_by: str = "source_id",
            sort_order: str = "asc"
    ) -> Dict[str, Any]:
        """分页查询边"""
        # 构建WHERE条件
        where_conditions = []
        params = {}

        # 边类型筛选
        if edge_type:
            where_conditions.append("type(r) = $edge_type")
            params["edge_type"] = edge_type

        # 源节点筛选
        if source_id:
            where_conditions.append("s.id = $source_id")
            params["source_id"] = source_id

        # 目标节点筛选
        if target_id:
            where_conditions.append("t.id = $target_id")
            params["target_id"] = target_id

        # 属性筛选
        if property_filters:
            for key, value in property_filters.items():
                param_name = f"prop_{key}"
                where_conditions.append(f"r.{key} = ${param_name}")
                params[param_name] = value

        # 文本搜索
        if search_text:
            where_conditions.append("(s.id CONTAINS $search_text OR t.id CONTAINS $search_text OR type(r) CONTAINS $search_text)")
            params["search_text"] = search_text

        where_clause = " AND ".join(where_conditions) if where_conditions else "true"

        # 排序方向验证
        sort_order = sort_order.upper() if sort_order.upper() in ["ASC", "DESC"] else "ASC"

        # 计算总数
        count_query = f"""
        MATCH (s:{self._get_node_label()})-[r]-(t:{self._get_node_label()})
        WHERE {where_clause}
        RETURN count(r) as total
        """
        count_result = await self.connection_manager.execute_query(count_query, params)
        total = count_result[0]["total"] if count_result else 0

        # 计算分页参数
        offset = (page - 1) * page_size
        total_pages = (total + page_size - 1) // page_size

        # 查询数据
        sort_field = "s.id" if sort_by == "source_id" else "t.id" if sort_by == "target_id" else f"r.{sort_by}"
        data_query = f"""
        MATCH (s:{self._get_node_label()})-[r]-(t:{self._get_node_label()})
        WHERE {where_clause}
        RETURN s.id as source_id, t.id as target_id, r, type(r) as relationship_type
        ORDER BY {sort_field} {sort_order}
        SKIP {offset}
        LIMIT {page_size}
        """
        data_result = await self.connection_manager.execute_query(data_query, params)

        edges = []
        for record in data_result:
            # 安全地处理关系数据
            edge_data = {}
            if record["r"]:
                try:
                    edge_data = dict(record["r"])
                except:
                    edge_data = {}

            edge_data.update({
                "source_id": record["source_id"],
                "target_id": record["target_id"],
                "relationship_type": record["relationship_type"]
            })
            edges.append(edge_data)

        return {
            "data": edges,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        }

    async def query_graph_with_traversal(
            self,
            label: Optional[str] = None,
            max_depth: int = 2,
            max_nodes: int = 100
    ) -> Dict[str, Any]:
        """
        图遍历查询，同时返回节点和边

        Args:
            label: 标签过滤条件，None或"*"表示不过滤
            max_depth: 最大遍历深度
            max_nodes: 最大节点数量

        Returns:
            包含nodes和edges的字典
        """
        logger.info(f"开始图遍历查询: label={label}, max_depth={max_depth}, max_nodes={max_nodes}")

        # 构建标签过滤条件
        if label and label != "*":
            # 如果指定了标签，查找具有该业务标签的节点
            start_query = """
            MATCH (start)
            WHERE start.label = $label
            RETURN COALESCE(start.entity_id, start.id) as start_id
            LIMIT 1
            """
            start_result = await self.connection_manager.execute_query(start_query, {"label": label})
            if not start_result:
                logger.warning(f"没有找到业务标签为 {label} 的节点")
                return {"nodes": [], "edges": [], "is_truncated": False}

            start_node_id = start_result[0]["start_id"]
            logger.info(f"找到起始节点: {start_node_id}")

            # 从指定业务标签的节点开始遍历
            traversal_query = f"""
            MATCH path = (start)-[*0..{max_depth}]-(connected)
            WHERE start.label = $label AND COALESCE(start.entity_id, start.id) = $start_id
            WITH DISTINCT connected as node
            LIMIT {max_nodes}
            RETURN node, labels(node) as node_labels
            """
            params = {"label": label, "start_id": start_node_id}
        else:
            # 不过滤标签，获取所有节点
            # 优先查询有entity_id或id的节点
            traversal_query = f"""
            MATCH (node)
            WHERE node.entity_id IS NOT NULL OR node.id IS NOT NULL
            RETURN node, labels(node) as node_labels
            LIMIT {max_nodes}
            """
            params = {}

        logger.info(f"执行节点查询: {traversal_query}")
        # 执行节点查询
        node_result = await self.connection_manager.execute_query(traversal_query, params)
        logger.info(f"节点查询返回 {len(node_result)} 个结果")

        nodes = []
        node_ids = set()

        for record in node_result:
            node_data = dict(record["node"])
            neo4j_labels = record["node_labels"]
            # 优先使用entity_id，如果没有则使用id
            node_id = node_data.get("entity_id") or node_data.get("id", "")

            if node_id:
                node_ids.add(node_id)

                # 构建标签数组：优先使用业务标签，如果没有则使用Neo4j标签
                business_label = node_data.get("label", "")
                if business_label and business_label.strip():
                    labels = [business_label]
                else:
                    # 过滤掉系统标签，只保留业务相关的标签
                    labels = [label for label in neo4j_labels if label not in ["Entity", "Document"]]
                    if not labels:
                        labels = neo4j_labels  # 如果没有业务标签，保留所有标签

                nodes.append({
                    "id": node_id,
                    "labels": labels,
                    "properties": node_data
                })
                logger.debug(f"添加节点: {node_id}, 业务标签: {labels}, Neo4j标签: {neo4j_labels}")
            else:
                logger.warning(f"节点缺少ID: {node_data}")

        logger.info(f"处理完成，共找到 {len(nodes)} 个节点")

        # 查询这些节点之间的边
        if node_ids:
            node_ids_list = list(node_ids)
            logger.info(f"查询 {len(node_ids_list)} 个节点之间的边")

            # 使用COALESCE来处理entity_id和id
            edge_query = """
            MATCH (s)-[r]->(t)
            WHERE (COALESCE(s.entity_id, s.id) IN $node_ids)
              AND (COALESCE(t.entity_id, t.id) IN $node_ids)
            RETURN COALESCE(s.entity_id, s.id) as source_id,
                   COALESCE(t.entity_id, t.id) as target_id,
                   r, type(r) as edge_type
            """
            edge_result = await self.connection_manager.execute_query(
                edge_query,
                {"node_ids": node_ids_list}
            )

            logger.info(f"边查询返回 {len(edge_result)} 个结果")

            edges = []
            for record in edge_result:
                source_id = record["source_id"]
                target_id = record["target_id"]

                # 安全地处理边数据
                try:
                    edge_data = dict(record["r"]) if record["r"] else {}
                except (TypeError, ValueError) as e:
                    logger.warning(f"处理边数据时出错: {e}, 使用空字典")
                    edge_data = {}

                edges.append({
                    "id": f"{source_id}-{target_id}",
                    "type": "DIRECTED",
                    "source": source_id,
                    "target": target_id,
                    "properties": edge_data
                })
                logger.debug(f"添加边: {source_id} -> {target_id}")
        else:
            logger.warning("没有找到节点，跳过边查询")
            edges = []

        # 判断是否被截断
        is_truncated = len(nodes) >= max_nodes

        logger.info(f"查询完成: 节点数={len(nodes)}, 边数={len(edges)}, 是否截断={is_truncated}")

        return {
            "nodes": nodes,
            "edges": edges,
            "is_truncated": is_truncated
        }

    async def get_all_labels(self) -> List[str]:
        """
        获取数据库中所有的业务标签列表（从节点的label属性中获取）

        Returns:
            业务标签列表
        """
        logger.info("开始获取所有业务标签")

        query = """
        MATCH (n)
        WHERE n.label IS NOT NULL AND n.label <> ""
        RETURN DISTINCT n.label as label
        ORDER BY label
        """

        try:
            result = await self.connection_manager.execute_query(query)
            labels = [record["label"] for record in result if record["label"]]
            logger.info(f"获取到 {len(labels)} 个业务标签: {labels}")
            return labels
        except Exception as e:
            logger.error(f"获取业务标签失败: {e}")
            return []


# 全局图操作实例
neo4j_graph_operations = Neo4jGraphOperations()


# 创建全局图操作实例
def initialize_graph_operations():
    """获取Neo4j图操作实例"""
    from .client import neo4j_connection_manager
    neo4j_graph_operations.set_connection_manager(neo4j_connection_manager)
