"""
MINIO 对象存储客户端

提供基于 MinIO 连接池管理
"""

import asyncio
import traceback

from minio import Minio

from src.config import config
from src.rag.tools.logger import logger


class MinIOClient:
    """MINIO 客户端"""

    def __init__(self):
        """
        初始化 MINIO 客户端

        从统一配置中获取MinIO连接参数
        """
        storage_config = config.storage

        self.endpoint = storage_config.minio_endpoint
        self.access_key = storage_config.minio_access_key
        self.secret_key = storage_config.minio_secret_key
        self.secure = storage_config.minio_secure
        self.bucket_name = storage_config.minio_bucket

        self.client = Minio(
            endpoint=self.endpoint,
            access_key=self.access_key,
            secret_key=self.secret_key,
            secure=self.secure
        )

        logger.info(f"MinIO client initialized: {self.endpoint}, bucket: {self.bucket_name}")

    async def initialize(self):
        """初始化，创建存储桶（如果不存在）"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None, self._create_bucket_if_not_exists
            )
            logger.info(f"MinIO bucket '{self.bucket_name}' is ready")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to initialize MinIO: {e}")
            raise

    def _create_bucket_if_not_exists(self):
        """创建存储桶（如果不存在）"""
        if not self.client.bucket_exists(self.bucket_name):
            self.client.make_bucket(self.bucket_name)
            logger.info(f"Created MinIO bucket: {self.bucket_name}")
        else:
            logger.info(f"MinIO bucket already exists: {self.bucket_name}")


min_io_client = MinIOClient()