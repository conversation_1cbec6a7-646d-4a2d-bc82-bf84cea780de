"""
MINIO 对象存储 CRUD 操作
"""

import asyncio
import os
import traceback
from io import BytesIO
from typing import Optional, Dict, Any

from minio.error import S3Error

from .client import min_io_client
from src.rag.tools.logger import logger


class MinIOObjectOperations:
    """MinIO 对象存储操作管理器 - 单例模式"""

    def __init__(self):
        logger.info("MinIO object operations initialized")

    def _get_client(self):
        return min_io_client.client

    def _get_bucket_name(self):
        return min_io_client.bucket_name

    def _get_endpoint(self):
        return min_io_client.endpoint

    def _is_secure(self):
        return min_io_client.secure

    async def upload_file(
            self,
            object_key: str,
            file_path: str,
            content_type: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        上传文件

        Args:
            object_key: 对象键（文件在MINIO中的路径）
            file_path: 本地文件路径
            content_type: 内容类型
        Returns:
            上传结果信息
        """
        try:
            file_size = os.path.getsize(file_path)

            await asyncio.get_event_loop().run_in_executor(
                None,
                self._upload_file_sync,
                object_key,
                file_path,
                content_type,
            )

            logger.info(f"Successfully uploaded file: {object_key} ({file_size} bytes)")

            return {
                "object_key": object_key,
                "bucket": self._get_bucket_name(),
                "size": file_size,
                "content_type": content_type,
                "url": f"{'https' if self._is_secure() else 'http'}://{self._get_endpoint()}/{self._get_bucket_name()}/{object_key}"
            }

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to upload file {object_key}: {e}")
            raise

    def _upload_file_sync(
            self,
            object_key: str,
            file_path: str,
            content_type: Optional[str] = None,
    ):
        """同步上传文件"""
        return self._get_client().fput_object(
            bucket_name=self._get_bucket_name(),
            object_name=object_key,
            file_path=file_path,
            content_type=content_type,
        )


    async def upload_bytes(
            self,
            object_key: str,
            data: bytes,
            content_type: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        上传字节数据

        Args:
            object_key: 对象键
            data: 字节数据
            content_type: 内容类型
        Returns:
            上传结果信息
        """
        try:
            data_stream = BytesIO(data)
            data_size = len(data)

            await asyncio.get_event_loop().run_in_executor(
                None,
                self._upload_bytes_sync,
                object_key,
                data_stream,
                data_size,
                content_type,
            )

            logger.info(f"Successfully uploaded bytes: {object_key} ({data_size} bytes)")

            return {
                "object_key": object_key,
                "bucket": self._get_bucket_name(),
                "size": data_size,
                "content_type": content_type,
                "url": f"{'https' if self._is_secure() else 'http'}://{self._get_endpoint()}/{self._get_bucket_name()}/{object_key}"
            }

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to upload bytes {object_key}: {e}")
            raise

    def _upload_bytes_sync(
            self,
            object_key: str,
            data_stream: BytesIO,
            data_size: int,
            content_type: Optional[str] = None,
    ):
        """同步上传字节数据"""
        return self._get_client().put_object(
            bucket_name=self._get_bucket_name(),
            object_name=object_key,
            data=data_stream,
            length=data_size,
            content_type=content_type,
        )


    async def download_file(self, object_key: str, file_path: str) -> None:
        """
        下载文件到本地

        Args:
            object_key: 对象键
            file_path: 本地保存路径
        """
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._get_client().fget_object,
                self._get_bucket_name(),
                object_key,
                file_path
            )
            logger.info(f"Successfully downloaded file: {object_key} -> {file_path}")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to download file {object_key}: {e}")
            raise

    async def download_bytes(self, object_key: str) -> bytes:
        """
        下载文件为字节数据

        Args:
            object_key: 对象键

        Returns:
            文件字节数据
        """
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self._get_client().get_object,
                self._get_bucket_name(),
                object_key
            )

            data = response.read()
            response.close()
            response.release_conn()

            logger.info(f"Successfully downloaded bytes: {object_key} ({len(data)} bytes)")
            return data

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to download bytes {object_key}: {e}")
            raise

    async def delete_file(self, object_key: str) -> None:
        """
        删除文件

        Args:
            object_key: 对象键
        """
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._get_client().remove_object,
                self._get_bucket_name(),
                object_key
            )
            logger.info(f"Successfully deleted file: {object_key}")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to delete file {object_key}: {e}")
            raise


    async def file_exists(self, object_key: str) -> bool:
        """
        检查文件是否存在

        Args:
            object_key: 对象键

        Returns:
            文件是否存在
        """
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._get_client().stat_object,
                self._get_bucket_name(),
                object_key
            )
            return True
        except S3Error as e:
            if e.code == 'NoSuchKey':
                return False
            traceback.print_exc()
            raise
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error checking file existence {object_key}: {e}")
            raise

    async def get_file_info(self, object_key: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息

        Args:
            object_key: 对象键

        Returns:
            文件信息字典
        """
        try:
            stat = await asyncio.get_event_loop().run_in_executor(
                None,
                self._get_client().stat_object,
                self._get_bucket_name(),
                object_key
            )

            return {
                "object_key": object_key,
                "bucket": self._get_bucket_name(),
                "size": stat.size,
                "etag": stat.etag,
                "content_type": stat.content_type,
                "last_modified": stat.last_modified,
            }

        except S3Error as e:
            if e.code == 'NoSuchKey':
                return None
            traceback.print_exc()
            logger.error(f"Error getting file info {object_key}: {e}")
            raise
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error getting file info {object_key}: {e}")
            raise


    async def list_files(self, prefix: str = "") -> list:
        """
        列出文件

        Args:
            prefix: 文件前缀过滤

        Returns:
            文件列表
        """
        try:
            objects = await asyncio.get_event_loop().run_in_executor(
                None,
                self._list_objects_sync,
                prefix
            )

            return [
                {
                    "object_key": obj.object_name,
                    "bucket": self._get_bucket_name(),
                    "size": obj.size,
                    "etag": obj.etag,
                    "last_modified": obj.last_modified,
                    "content_type": getattr(obj, 'content_type', None)
                }
                for obj in objects
            ]

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error listing files with prefix {prefix}: {e}")
            raise

    def _list_objects_sync(self, prefix: str = ""):
        """同步列出对象"""
        return list(self._get_client().list_objects(
            bucket_name=self._get_bucket_name(),
            prefix=prefix,
            recursive=True
        ))


    async def get_object_bytes(self, object_key: str) -> bytes:
        """下载文件为字节数据（别名方法）"""
        return await self.download_bytes(object_key)

    async def get_object_stream(self, object_key: str):
        """获取文件流（用于下载）"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self._get_client().get_object,
                self._get_bucket_name(),
                object_key
            )

            # 创建异步生成器来流式读取数据
            async def stream_generator():
                try:
                    # 4KB chunks
                    chunk_size = 4096
                    while True:
                        chunk = response.read(chunk_size)
                        if not chunk:
                            break
                        yield chunk
                finally:
                    response.close()
                    response.release_conn()

            return stream_generator()

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to get object stream {object_key}: {e}")
            raise


# 创建单例实例
object_operations = MinIOObjectOperations()
