"""
Milvus 连接池管理器

提供基于 Milvus 的连接池管理功能
"""

import asyncio
import threading
import traceback
from contextlib import asynccontextmanager
from queue import Queue, Empty
from typing import Set

from pymilvus import (
    Collection,
    CollectionSchema,
    DataType,
    FieldSchema,
    connections,
    utility,
)

from src.config import config
from src.rag.tools.logger import logger


class MilvusConnectionPool:
    """Milvus 连接池管理器"""

    def __init__(self, max_connections: int = 10, min_connections: int = 2):
        """
        初始化连接池

        Args:
            max_connections: 最大连接数
            min_connections: 最小连接数
        """
        self.max_connections = max_connections
        self.min_connections = min_connections
        self._pool = Queue(maxsize=max_connections)
        self._active_connections: Set[str] = set()
        self._lock = threading.Lock()
        self._initialized = False

        # 从配置获取连接参数
        storage_config = config.storage
        self.host = storage_config.milvus_host
        self.port = storage_config.milvus_port
        self.user = ''
        self.password = ''
        self.db_name = ''

        logger.info(f"Milvus connection pool initialized: max={max_connections}, min={min_connections}")

    async def initialize(self):
        """初始化连接池"""
        if self._initialized:
            return

        try:
            # 创建最小数量的连接
            for i in range(self.min_connections):
                connection_alias = await self._create_connection()
                self._pool.put(connection_alias)

            self._initialized = True
            logger.info(f"Connection pool initialized with {self.min_connections} connections")

        except Exception as e:
            logger.error(f"Failed to initialize connection pool: {e}")
            raise

    async def _create_connection(self) -> str:
        """创建新的连接"""
        connection_alias = f"mixrag_milvus_{len(self._active_connections)}"

        try:
            # 连接参数
            connect_params = {
                "alias": connection_alias,
                "host": self.host,
                "port": self.port,
            }

            if self.user:
                connect_params["user"] = self.user
            if self.password:
                connect_params["password"] = self.password

            # 在线程池中执行连接
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: connections.connect(**connect_params)
            )

            # 使用指定数据库（如果支持）
            if self.db_name:
                try:
                    if hasattr(connections, 'get_connection'):
                        conn = connections.get_connection(connection_alias)
                        if hasattr(conn, 'use_database'):
                            conn.use_database(self.db_name)
                except Exception as db_error:
                    logger.warning(f"Failed to switch database to {self.db_name}: {db_error}")

            with self._lock:
                self._active_connections.add(connection_alias)

            logger.debug(f"Created new connection: {connection_alias}")
            return connection_alias

        except Exception as e:
            logger.error(f"Failed to create connection {connection_alias}: {e}")
            raise

    @asynccontextmanager
    async def get_connection(self):
        """获取连接的上下文管理器"""
        connection_alias = None
        try:
            # 尝试从池中获取连接
            try:
                connection_alias = self._pool.get_nowait()
            except Empty:
                # 池中没有可用连接，创建新连接
                if len(self._active_connections) < self.max_connections:
                    connection_alias = await self._create_connection()
                else:
                    # 等待连接可用
                    connection_alias = await asyncio.get_event_loop().run_in_executor(
                        None, self._pool.get
                    )

            # 检查连接是否有效
            if not connections.has_connection(connection_alias):
                # 连接无效，重新创建
                logger.warning(f"Connection {connection_alias} is invalid, recreating...")
                with self._lock:
                    self._active_connections.discard(connection_alias)
                connection_alias = await self._create_connection()

            yield connection_alias

        finally:
            # 归还连接到池中
            if connection_alias and connections.has_connection(connection_alias):
                self._pool.put(connection_alias)

    async def close_all(self):
        """关闭所有连接"""
        try:
            # 清空连接池
            while not self._pool.empty():
                try:
                    connection_alias = self._pool.get_nowait()
                    if connections.has_connection(connection_alias):
                        connections.disconnect(connection_alias)
                except Empty:
                    break
                except Exception as e:
                    logger.warning(f"Failed to disconnect {connection_alias}: {e}")

            # 关闭所有活跃连接
            with self._lock:
                for connection_alias in list(self._active_connections):
                    try:
                        if connections.has_connection(connection_alias):
                            connections.disconnect(connection_alias)
                    except Exception as e:
                        logger.warning(f"Failed to disconnect {connection_alias}: {e}")
                self._active_connections.clear()

            self._initialized = False
            logger.info("All connections closed")

        except Exception as e:
            logger.error(f"Failed to close connections: {e}")


class MilvusClient:
    """Milvus 集合管理器"""

    def __init__(self, connection_pool: MilvusConnectionPool):
        """
        初始化集合管理器

        Args:
            connection_pool: 连接池实例
        """
        self.connection_pool = connection_pool

        # 从统一配置获取Milvus配置
        storage_config = config.storage

        # 嵌入维度配置
        self.embedding_dim = storage_config.milvus_dimension

        # 索引配置
        self.index_type = "HNSW"
        self.metric_type = "COSINE"
        self.nlist = 1024
        self.m = 8
        self.ef_construction = 200

        # 确保meta_fields是有序的，避免字段顺序错位
        self.meta_fields = sorted(list(set(['full_doc_id', 'chunk_order_index', 'tokens', 'content', 'description', 'keywords'])))

        # 集合名称配置
        self.collection_name = storage_config.milvus_collection

        # 字段配置
        self.id_field = "id"
        self.vector_field = "vector"
        # 移除text_field，只使用content字段（在meta_fields中定义）

        logger.info(f"Milvus collection manager initialized: collection={self.collection_name}")
        logger.info(f"Meta fields configured: {self.meta_fields}")

    async def _setup_collection(self):
        """设置集合"""
        try:
            await asyncio.get_event_loop().run_in_executor(None, self._setup_collection_sync)

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to setup collection: {e}")
            raise

    def _setup_collection_sync(self):
        """同步设置集合"""
        try:
            # 获取一个连接来检查集合
            connection_alias = None
            try:
                connection_alias = self.connection_pool._pool.get_nowait()
            except:
                # 如果池中没有连接，创建一个临时连接
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                connection_alias = loop.run_until_complete(self.connection_pool._create_connection())
                loop.close()

            # 检查集合是否存在
            has_collection = utility.has_collection(
                self.collection_name,
                using=connection_alias
            )

            logger.info(f"Collection {self.collection_name} exists: {has_collection}")
            logger.info(f"Will create with meta_fields: {self.meta_fields}")

            if has_collection:
                # 检查现有集合的schema是否匹配
                existing_collection = Collection(
                    name=self.collection_name,
                    using=connection_alias
                )

                expected_field_count = 3 + len(self.meta_fields)  # id, vector, text + meta_fields
                actual_field_count = len(existing_collection.schema.fields)

                logger.info(f"Schema check - Expected fields: {expected_field_count}, Actual fields: {actual_field_count}")

                if actual_field_count != expected_field_count:
                    logger.warning(f"Schema mismatch detected. Dropping and recreating collection {self.collection_name}")
                    # 删除现有集合
                    utility.drop_collection(self.collection_name, using=connection_alias)
                    logger.info(f"Dropped existing collection: {self.collection_name}")
                    has_collection = False

            if not has_collection:
                # 创建集合
                self._create_collection(connection_alias)
                logger.info(f"Created Milvus collection: {self.collection_name}")
            else:
                logger.info(f"Milvus collection already exists with correct schema: {self.collection_name}")

            # 归还连接
            if connection_alias:
                self.connection_pool._pool.put(connection_alias)

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Sync setup collection failed: {e}")
            raise

    def _create_collection(self, connection_alias: str):
        """创建集合"""
        try:
            # 定义字段
            fields = [
                FieldSchema(
                    name=self.id_field,
                    dtype=DataType.VARCHAR,
                    is_primary=True,
                    max_length=255
                ),
                FieldSchema(
                    name=self.vector_field,
                    dtype=DataType.FLOAT_VECTOR,
                    dim=self.embedding_dim
                )
                # 移除text字段，只使用content字段（在meta_fields中定义）
            ]

            # 添加元数据字段 - 根据字段名称选择合适的数据类型和长度
            for field_name in self.meta_fields:
                if field_name in ['chunk_order_index', 'tokens', 'weight']:
                    # 数值字段使用INT64类型
                    fields.append(FieldSchema(
                        name=field_name,
                        dtype=DataType.INT64
                    ))
                elif field_name == 'full_doc_id':
                    fields.append(FieldSchema(
                        name=field_name,
                        dtype=DataType.VARCHAR,
                        max_length=255
                    ))
                elif field_name in ['content']:
                    # content字段需要更大的长度限制，支持长文本
                    fields.append(FieldSchema(
                        name=field_name,
                        dtype=DataType.VARCHAR,
                        max_length=16384  # 32K字符，足够存储大部分chunk内容
                    ))
                elif field_name in ['description', 'keywords']:
                    # 描述和关键词字段使用中等长度
                    fields.append(FieldSchema(
                        name=field_name,
                        dtype=DataType.VARCHAR,
                        max_length=4096  # 4K字符，足够存储描述信息
                    ))
                else:
                    # 其他文本字段使用默认长度
                    fields.append(FieldSchema(
                        name=field_name,
                        dtype=DataType.VARCHAR,
                        max_length=1024
                    ))

            logger.info(f"Creating collection with fields: {[f.name + ':' + str(f.dtype) for f in fields]}")

            # 创建集合模式
            schema = CollectionSchema(fields=fields, description="Mix-RAG vector storage")

            # 创建集合
            collection = Collection(
                name=self.collection_name,
                schema=schema,
                using=connection_alias
            )

            # 创建索引
            self._create_index(collection)

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to create collection: {e}")
            raise

    def _create_index(self, collection: Collection):
        """创建向量索引"""
        try:
            # 构建索引参数
            index_params = {
                "metric_type": self.metric_type,
                "index_type": self.index_type,
            }

            # 根据索引类型设置参数
            if self.index_type == "IVF_FLAT":
                index_params["params"] = {"nlist": self.nlist}
            elif self.index_type == "HNSW":
                index_params["params"] = {"M": self.m, "efConstruction": self.ef_construction}

            # 创建索引
            collection.create_index(
                field_name=self.vector_field,
                index_params=index_params
            )

            logger.info(f"Created index for collection: {self.collection_name}")

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to create index: {e}")
            raise

    async def _load_collection(self):
        """加载集合到内存"""
        try:
            await asyncio.get_event_loop().run_in_executor(None, self._load_collection_sync)
            logger.info(f"Collection loaded: {self.collection_name}")

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to load collection: {e}")
            raise

    def _load_collection_sync(self):
        """同步加载集合"""
        try:
            # 获取一个连接来加载集合
            connection_alias = None
            try:
                connection_alias = self.connection_pool._pool.get_nowait()
            except:
                # 如果池中没有连接，创建一个临时连接
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                connection_alias = loop.run_until_complete(self.connection_pool._create_connection())
                loop.close()

            import time
            from pymilvus import utility

            # 检查集合是否已经加载
            try:
                load_state = utility.load_state(
                    self.collection_name,
                    using=connection_alias
                )
                logger.info(f"Collection load state: {load_state}")

                # 如果已经加载完成，直接返回
                if load_state == "Loaded":
                    logger.info(f"Collection {self.collection_name} is already loaded")
                    # 归还连接
                    if connection_alias:
                        self.connection_pool._pool.put(connection_alias)
                    return
            except Exception as check_error:
                logger.debug(f"Failed to check load state: {check_error}")

            # 创建集合对象并加载
            collection = Collection(name=self.collection_name, using=connection_alias)

            # 尝试释放集合（如果已经被部分加载）
            try:
                collection.release()
                logger.info(f"Released collection before loading: {self.collection_name}")
                time.sleep(1)
            except Exception as release_error:
                logger.debug(f"Failed to release collection (might be normal): {release_error}")

            # 重新加载集合
            collection.load()
            logger.info(f"Collection load command issued: {self.collection_name}")

            # 使用 wait_for_loading_complete 等待加载完成
            try:
                utility.wait_for_loading_complete(
                    collection_name=self.collection_name,
                    timeout=30,
                    using=connection_alias
                )
                logger.info(f"Collection {self.collection_name} loaded successfully")
            except Exception as wait_error:
                logger.warning(f"wait_for_loading_complete failed: {wait_error}")

            # 归还连接
            if connection_alias:
                self.connection_pool._pool.put(connection_alias)

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Sync load collection failed: {e}")
            raise

    async def initialize(self):
        """初始化集合"""
        # 初始化连接池
        await self.connection_pool.initialize()

        # 创建或获取集合
        await self._setup_collection()

        # 加载集合
        await self._load_collection()

        logger.info(f"Milvus collection initialized successfully: {self.collection_name}")

    async def finalize(self):
        """清理连接池"""
        try:
            await self.connection_pool.close_all()
            logger.info(f"Milvus collection manager finalized: {self.collection_name}")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to finalize Milvus collection manager: {e}")


# 创建连接池和集合管理器实例
milvus_connection_pool = MilvusConnectionPool()
milvus_client = MilvusClient(milvus_connection_pool)
