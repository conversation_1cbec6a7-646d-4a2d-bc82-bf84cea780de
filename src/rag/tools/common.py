"""
通用工具函数模块

包含 MixRAG 核心工作流所需的基础工具函数：
- 日志配置和管理
- 哈希计算和ID生成
- 文本编码和token处理
- 异步函数装饰器
- 数据格式转换工具
"""

import asyncio
import csv
import hashlib
import json
import re
import traceback
from dataclasses import dataclass
from functools import wraps
from io import StringIO
from typing import Any, Callable, List, Union

import tiktoken

from src.rag.tools.logger import logger

tokenizer = tiktoken.get_encoding("cl100k_base")


@dataclass
class EmbeddingFunc:
    """嵌入函数的包装类"""
    embedding_dim: int
    max_token_size: int
    func: Callable

    async def __call__(self, texts: List[str]) -> List[List[float]]:
        """调用嵌入函数"""
        return await self.func(texts)


def compute_mdhash_id(content: Union[str, bytes], prefix: str = "") -> str:
    """计算 MD5 哈希 ID"""
    if isinstance(content, str):
        content_bytes = content.encode()
    else:
        content_bytes = content
    return prefix + hashlib.md5(content_bytes).hexdigest()


def compute_args_hash(*args) -> str:
    """计算参数的哈希值"""
    content = json.dumps(args, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(content.encode()).hexdigest()


def encode_string_by_tiktoken(content: str, model_name: str = "gpt-4o") -> List[int]:
    """
    使用 tiktoken 编码字符串

    Args:
        content: 要编码的文本内容
        model_name: 模型名称（用于兼容性，当前未使用）

    Returns:
        编码后的token列表
    """
    if tokenizer is None:
        # 简单的分词替代
        return content.split()

    try:
        return tokenizer.encode(content)
    except Exception as e:
        traceback.print_exc()
        logger.warning(f"Tiktoken encoding failed: {e}, using simple split")
        return content.split()


def decode_tokens_by_tiktoken(tokens: List[int], model_name: str = "gpt-4o") -> str:
    """
    使用 tiktoken 解码 tokens

    Args:
        tokens: 要解码的token列表
        model_name: 模型名称（用于兼容性，当前未使用）

    Returns:
        解码后的文本
    """
    if tokenizer is None:
        # 简单的连接替代
        return " ".join(str(t) for t in tokens)

    try:
        return tokenizer.decode(tokens)
    except Exception as e:
        traceback.print_exc()
        logger.warning(f"Tiktoken decoding failed: {e}")
        return " ".join(str(t) for t in tokens)


def truncate_list_by_token_size(
        list_data: List[str],
        key: Callable = None,
        max_token_size: int = 16384,
        model_name: str = "gpt-4o"
) -> List[str]:
    """根据 token 大小截断列表"""
    if key is None:
        key = lambda x: x

    result = []
    total_tokens = 0

    for item in list_data:
        content = key(item)
        tokens = encode_string_by_tiktoken(content, model_name)
        item_tokens = len(tokens)

        if total_tokens + item_tokens <= max_token_size:
            result.append(item)
            total_tokens += item_tokens
        else:
            break

    return result


def list_of_list_to_csv(data: List[List[str]]) -> str:
    """将列表的列表转换为 CSV 字符串"""
    output = StringIO()
    writer = csv.writer(output)
    writer.writerows(data)
    return output.getvalue()


def csv_string_to_list(csv_str: str) -> List[List[str]]:
    """将 CSV 字符串转换为列表的列表"""
    input_io = StringIO(csv_str)
    reader = csv.reader(input_io)
    return [row for row in reader]


def split_string_by_multi_markers(
        content: str,
        markers: List[str]
) -> List[str]:
    """
    使用多个标记分割字符串

    Args:
        content: 要分割的字符串
        markers: 分割标记列表

    Returns:
        分割后的字符串列表
    """
    if not markers:
        return [content]

    pattern = '|'.join(re.escape(marker) for marker in markers)
    parts = re.split(pattern, content)
    return [part.strip() for part in parts if part.strip()]


def safe_init_async_func(func):
    """安全初始化异步函数的装饰器"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error in {func.__name__}: {e}")
            raise

    return wrapper


def limit_async_func_call(max_size: int = 16, waitting_time: float = 0.0001):
    """限制异步函数并发调用数量的装饰器"""
    semaphore = asyncio.Semaphore(max_size)

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            async with semaphore:
                if waitting_time > 0:
                    await asyncio.sleep(waitting_time)
                return await func(*args, **kwargs)

        return wrapper

    return decorator


async def run_async_func_call(
        func: Callable,
        data_list: List[Any],
        batch_size: int = 16
) -> List[Any]:
    """批量异步调用函数"""
    results = []

    for i in range(0, len(data_list), batch_size):
        batch = data_list[i:i + batch_size]
        batch_results = await asyncio.gather(
            *[func(item) for item in batch],
            return_exceptions=True
        )

        for result in batch_results:
            if isinstance(result, Exception):
                logger.error(f"Async function call failed: {result}")
                results.append(None)
            else:
                results.append(result)

    return results
