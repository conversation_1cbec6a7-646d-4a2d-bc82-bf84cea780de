"""
RAG 工具 - 日志模块

提供统一的日志记录功能
"""

import datetime
import logging


class TimestampFormatter(logging.Formatter):
    """自定义日志格式化器，支持时间戳和彩色输出"""
    
    # ANSI 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置颜色
    }
    
    def format(self, record):
        # 添加时间戳
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # 获取颜色
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化消息
        log_message = f"{color}[{timestamp}] {record.levelname:8} | {record.name:20} | {record.getMessage()}{reset}"
        
        # 如果有异常信息，添加到消息中
        if record.exc_info:
            log_message += f"\n{self.formatException(record.exc_info)}"
        
        return log_message


def setup_logging(level=logging.INFO):
    """设置日志配置"""
    # 创建根记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    
    # 设置自定义格式化器
    formatter = TimestampFormatter()
    console_handler.setFormatter(formatter)
    
    # 添加处理器到根记录器
    root_logger.addHandler(console_handler)
    
    return root_logger


# 初始化日志配置
setup_logging(level=logging.INFO)
logger = logging.getLogger(__name__)
