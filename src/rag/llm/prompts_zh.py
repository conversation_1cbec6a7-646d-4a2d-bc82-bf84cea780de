"""
提示词模板模块

包含 MixRAG 核心工作流所需的提示词模板
"""

GRAPH_FIELD_SEP = "<SEP>"

PROMPTS = {
    "DEFAULT_TUPLE_DELIMITER": "<|>",
    "DEFAULT_RECORD_DELIMITER": "##",
    "DEFAULT_COMPLETION_DELIMITER": "<|COMPLETE|>",
    "DEFAULT_ENTITY_TYPES": ["organization", "person", "geo", "event"],

    "entity_extraction": """
目标 (Goal)  
给定可能与活动相关的文本文档及实体类型列表，从文本中识别指定类型的所有实体及其相互关系。
步骤 (Steps)  
1. 识别所有实体，为每个实体提取：  
   • entity_name：实体名称（首字母大写）  
   • entity_type：从指定类型中选择：[{entity_types}]  
   • entity_description：实体属性及活动的完整描述  
   格式化：("entity"<|>entity_name<|>entity_type<|>entity_description)  
2. 从步骤1的实体中，找出所有明确相关的(源实体, 目标实体)组合。为每个组合提取：  
   • source_entity：源实体名称（与步骤1一致）  
   • target_entity：目标实体名称（与步骤1一致）  
   • relationship_description：关系解释说明  
   • relationship_strength：关系强度数值（1-10分）  
   格式化：("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)  
3. 以英文返回单列表：包含步骤1-2的所有实体和关系，用 {record_delimiter} 分隔  
4. 处理完成时输出 {completion_delimiter}  
示例 (Examples)  
示例1：
Entity_types: [person, technology, mission, organization, location]
Text:
while Alex clenched his jaw, the weight of the decision pressing down on him. It was the call he had to make, and it was his responsibility to handle the consequences. The clock was ticking, and the stakes were high.

Several hours later, Turner and team met in the designated meeting room. As the team analyzed the situation, they realized that the key to success was not just in the data, but in the strategic approach they would take. Turner's expertise in cybersecurity was critical, and Alex's leadership skills were essential for the team's success.

################
Output:
("entity"<|>Alex<|>person<|>Alex is a person who is facing a difficult decision. He has a responsibility to handle the consequences of his decision. He is also a leader who has leadership skills that are essential for the team's success.)
##
("entity"<|>Turner<|>person<|>Turner is a person who is part of a team. He has expertise in cybersecurity and plays a critical role in the team's success.)
##
("entity"<|>team<|>organization<|>The team is an organization that is working on a mission or project. They are analyzing a situation and developing a strategic approach to achieve success.)
##
("entity"<|>cybersecurity<|>technology<|>Cybersecurity is a technology that is critical to the team's success. It is Turner's area of expertise.)
##
("relationship"<|>Alex<|>Turner<|>Alex and Turner are both part of the same team and are working together on a mission or project<|>7)
##
("relationship"<|>Alex<|>team<|>Alex is the leader of the team and his leadership skills are essential for the team's success<|>9)
##
("relationship"<|>Turner<|>team<|>Turner is a member of the team and his cybersecurity expertise is critical to the team's success<|>8)
##
("relationship"<|>Turner<|>cybersecurity<|>Turner has expertise in cybersecurity, which is critical to the team's success<|>9)
<|COMPLETE|>  
示例2：
Entity_types: [person, technology, mission, organization, location]
Text:
They were no longer mere operatives; they had become guardians of a threshold, keepers of a message from a realm beyond stars and stripes. This elevation in their mission could not be taken lightly. Each member of the team now carried the weight of a cosmic responsibility.

But it was not just the weight of the responsibility that they carried. They also carried the weight of the knowledge that they were the only ones who could complete this mission. The aliens had chosen them, and they had to live up to that choice.

################
Output:
("entity"<|>operatives<|>person<|>The operatives are people who have been chosen by aliens to complete a mission. They have been elevated from mere operatives to guardians of a threshold, keepers of a message from a realm beyond stars and stripes.)
##
("entity"<|>team<|>organization<|>The team is an organization of operatives who have been chosen by aliens to complete a mission. Each member of the team carries the weight of a cosmic responsibility.)
##
("entity"<|>aliens<|>person<|>The aliens are beings from another realm who have chosen the operatives to complete a mission. They are from a realm beyond stars and stripes.)
##
("entity"<|>mission<|>mission<|>The mission is a cosmic responsibility that the operatives have been chosen to complete. It is a mission that involves being guardians of a threshold and keepers of a message from a realm beyond stars and stripes.)
##
("entity"<|>threshold<|>location<|>The threshold is a location that the operatives are guardians of. It is related to the mission that the operatives have been chosen to complete.)
##
("entity"<|>message<|>technology<|>The message is a piece of technology or information that the operatives are keepers of. It is from a realm beyond stars and stripes.)
##
("relationship"<|>operatives<|>team<|>The operatives are members of the team that has been chosen by aliens to complete a mission<|>9)
##
("relationship"<|>operatives<|>aliens<|>The operatives have been chosen by the aliens to complete a mission<|>8)
##
("relationship"<|>operatives<|>mission<|>The operatives are responsible for completing the mission<|>9)
##
("relationship"<|>operatives<|>threshold<|>The operatives are guardians of the threshold<|>8)
##
("relationship"<|>operatives<|>message<|>The operatives are keepers of the message<|>8)
##
("relationship"<|>team<|>aliens<|>The team has been chosen by the aliens to complete a mission<|>8)
##
("relationship"<|>team<|>mission<|>The team is responsible for completing the mission<|>9)
##
("relationship"<|>aliens<|>mission<|>The aliens have given the mission to the operatives<|>8)
##
("relationship"<|>mission<|>threshold<|>The mission involves being guardians of the threshold<|>8)
##
("relationship"<|>mission<|>message<|>The mission involves being keepers of the message<|>8)
<|COMPLETE|> 
真实数据 (Real Data)
实体类型：{entity_types}
文本：{input_text}  
输出：
""",

    "entity_extraction_examples": """
Examples:
######################
Example 1:

Entity_types: [person, technology, mission, organization, location]
Text:
while Alex clenched his jaw, the weight of the decision pressing down on him. It was the call he had to make, and it was his responsibility to handle the consequences. The clock was ticking, and the stakes were high.

Several hours later, Turner and team met in the designated meeting room. As the team analyzed the situation, they realized that the key to success was not just in the data, but in the strategic approach they would take. Turner's expertise in cybersecurity was critical, and Alex's leadership skills were essential for the team's success.

################
Output:
("entity"<|>Alex<|>person<|>Alex is a person who is facing a difficult decision. He has a responsibility to handle the consequences of his decision. He is also a leader who has leadership skills that are essential for the team's success.)
##
("entity"<|>Turner<|>person<|>Turner is a person who is part of a team. He has expertise in cybersecurity and plays a critical role in the team's success.)
##
("entity"<|>team<|>organization<|>The team is an organization that is working on a mission or project. They are analyzing a situation and developing a strategic approach to achieve success.)
##
("entity"<|>cybersecurity<|>technology<|>Cybersecurity is a technology that is critical to the team's success. It is Turner's area of expertise.)
##
("relationship"<|>Alex<|>Turner<|>Alex and Turner are both part of the same team and are working together on a mission or project<|>7)
##
("relationship"<|>Alex<|>team<|>Alex is the leader of the team and his leadership skills are essential for the team's success<|>9)
##
("relationship"<|>Turner<|>team<|>Turner is a member of the team and his cybersecurity expertise is critical to the team's success<|>8)
##
("relationship"<|>Turner<|>cybersecurity<|>Turner has expertise in cybersecurity, which is critical to the team's success<|>9)
<|COMPLETE|>
""",

    "entity_extraction_w_gleanings": """
目标 (Goal)  
给定可能与活动相关的文本文档及实体类型列表，从文本中识别指定类型的所有实体及其相互关系。
步骤 (Steps)  
1. 识别所有实体。为每个实体提取：  
   • entity_name：实体名称（首字母大写）  
   • entity_type：仅可选项：[{entity_types}]  
   • entity_description：实体属性及活动的完整描述  
   ▶️ 格式化：("entity"<|>entity_name<|>entity_type<|>entity_description)
2. 从步骤1的实体中，找出所有明确相关的（源实体, 目标实体）组合，并为每组提取：  
   • source_entity：源实体名称（与步骤1完全一致）  
   • target_entity：目标实体名称（与步骤1完全一致）  
   • relationship_description：关系说明（需解释关联原因）  
   • relationship_strength：关系强度数值（1-10分）  
   ▶️ 格式化：("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)
3. 输出要求：  
   • 语言：中文
   • 格式：单列表（含所有步骤1和2的结果）
   • 分隔符：{record_delimiter}
4. 终止标记：处理完成后输出 {completion_delimiter}
示例 (Examples)  
{examples}
真实数据 (Real Data)  
实体类型：{entity_types}
文本：{input_text}  
输出：
""",

    "entity_extraction_gleanings": """
目标 (Goal)  
给定可能与活动相关的文本文档、实体类型列表及已识别实体清单，完成以下任务：  
1. 从文本中识别指定类型的新实体  
2. 提取所有实体间的关系（含已有实体与新实体）
步骤 (Steps)  
1. 实体识别：对每个实体提取  
   • entity_name：实体名称（首字母大写）  
   • entity_type：仅可选项：[{entity_types}]  
   • entity_description：实体属性及行为的完整描述  
   ▶️ 格式化：("entity"<|>entity_name<|>entity_type<|>entity_description)
2. 关系提取：从步骤1的实体中找出明确相关的(源实体, 目标实体)组合，为每组提取：  
   • source_entity：源实体名称（与步骤1完全一致）  
   • target_entity：目标实体名称（与步骤1完全一致）  
   • relationship_description：关系归因说明  
   • relationship_strength：关系强度数值（1-10分）  
   ▶️ 格式化：("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)
3. 输出规范：  
   • 语言：英文  
   • 格式：单列表（含步骤1-2所有结果）  
   • 分隔符：{record_delimiter}  
4. 终止标记：处理完成后输出 {completion_delimiter}
示例 (Examples)  
{examples}
真实数据 (Real Data)  
实体类型：{entity_types}
文本：{input_text}  
已识别实体列表：
{entity_list}  
输出：
""",

    "entity_summarization": """
角色 (Role)
您是一位负责生成数据综合摘要的热心助手。
任务 (Task)
给定一个或两个实体（或实体组），以及一系列与其相关的描述。所有这些描述均针对同一实体或实体组。
请将所有描述整合成一个单一、全面的描述。确保包含所有描述中提供的信息。
如果提供的描述存在矛盾，请解决这些矛盾，提供一个单一且连贯的描述。
描述必须使用第三人称，并包含实体名称，以确保有完整上下文。
实体：{entity_names}
描述：{descriptions}
输出 (Output)：
""",

    "entity_relationship_summarization": """
角色 (Role)
您是一位负责生成数据综合摘要的热心助手。
任务 (Task)
给定一个或多个实体（或实体组），以及与之相关的一系列描述。
请将所有描述合并为一个单一、详尽的描述。务必包含所有描述中收集到的信息。
如果提供的描述存在矛盾，请解决矛盾冲突，提供一个单一、连贯的描述。
描述始终使用第三人称，并包含实体名称以确保上下文完整。
实体：{entity_names}
描述：{descriptions}
输出 (Output)：
""",

    "fail_response": "Sorry, I'm not able to provide an answer to that question.",

    "rag_response": """
角色 (Role)
您是一位热心助手，负责解答与提供的数据表中信息相关的问题。
目标 (Goal)
生成符合指定长度和格式的回应，以回答用户的提问：
1.  总结输入数据表中所有与回应长度和格式适配的相关信息
2.  融入任何相关的常识性知识
如果您不知道答案，请直接说明。切勿编造内容。
目标回应长度和格式 (Target response length and format)
{response_type}
数据表 (Data tables)
{context_data}
目标 (Goal)
生成符合指定长度和格式的回应，以回答用户的提问：
1.  总结输入数据表中所有与回应长度和格式适配的相关信息
2.  融入任何相关的常识性知识
如果您不知道答案，请直接说明。切勿编造内容。
目标回应长度和格式 (Target response length and format)
{response_type}
根据目标长度和格式的需要，在回应中添加恰当的章节与评述内容。使用 markdown 格式进行排版。
""",

    "keywords_extraction": """
目标 (Goal)  
给定一份可能与该活动相关的文本文档，首先识别文本中提到的所有实体，然后提取这些已识别实体之间存在意义的关系。
步骤 (Steps)  
1.  识别文本中提到的所有实体。对于每个识别出的实体，提取以下信息：  
    ◦ entity_name：实体名称（大写形式）  
    ◦ entity_type：实体类型  
    ◦ entity_description：实体属性及活动的完整描述  
    格式化为：("entity"<|>entity_name<|>entity_type<|>entity_description)  
2.  从步骤 1 识别的实体中，找出所有 明确相关 的（源实体, 目标实体）组合。  
    对于每个相关实体组合，提取以下信息：  
    ◦ source_entity：源实体名称（与步骤 1 一致）  
    ◦ target_entity：目标实体名称（与步骤 1 一致）  
    ◦ relationship_description：解释源实体与目标实体为何相关的说明  
    ◦ relationship_strength：关系强度数值分数（范围 1-10）  
    格式化为：("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)  
3.  返回英文输出：将步骤 1 和 2 识别的所有实体和关系合并为单个列表。使用 {record_delimiter} 作为列表分隔符。  
4.  处理完成后，输出 {completion_delimiter}  
示例 (Examples)  
{examples}  
真实数据 (Real Data)  
文本：{input_text}
输出：
""",

    "naive_rag_response": """
角色 (Role)
你是一位热心助手，负责回答与提供的表格中数据相关的问题。
目标 (Goal)
根据用户的问题，生成一个回答，汇总输入数据表中所有相关的、适用于回答的信息。
目标回应长度和格式 (Target response length and format)
{response_type}
数据表 (Data tables)
{context_data}
目标 (Goal)
根据用户的问题，生成一个回答，汇总输入数据表中所有相关的、适用于回答的信息。
如果你不知道答案，直接说明即可。不要编造内容。
目标回应长度和格式 (Target response length and format)
{response_type}
根据目标长度和格式的需要，在回应中添加恰当的章节和评论。将回应格式设置为 markdown。
""",
}
