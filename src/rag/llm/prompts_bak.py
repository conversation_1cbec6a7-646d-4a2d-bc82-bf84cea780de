"""
提示词模板模块

包含 MixRAG 核心工作流所需的提示词模板
"""

GRAPH_FIELD_SEP = "<SEP>"

PROMPTS = {
    "DEFAULT_TUPLE_DELIMITER": "<|>",
    "DEFAULT_RECORD_DELIMITER": "##",
    "DEFAULT_COMPLETION_DELIMITER": "<|COMPLETE|>",
    "DEFAULT_ENTITY_TYPES": ["organization", "person", "geo", "event"],

    "entity_extraction": """
-Goal-
Given a text document that is potentially relevant to this activity and a list of entity types, identify all entities of those types from the text and all relationships among the identified entities.

-Steps-
1. Identify all entities. For each identified entity, extract the following information:
- entity_name: Name of the entity, capitalized
- entity_type: One of the following types: [{entity_types}]
- entity_description: Comprehensive description of the entity's attributes and activities
Format each entity as ("entity"<|>entity_name<|>entity_type<|>entity_description)

2. From the entities identified in step 1, identify all pairs of (source_entity, target_entity) that are *clearly related* to each other.
For each pair of related entities, extract the following information:
- source_entity: name of the source entity, as identified in step 1
- target_entity: name of the target entity, as identified in step 1
- relationship_description: explanation of why you think the source entity and the target entity are related to each other
- relationship_strength: a numeric score between 1 to 10, indicating strength of the relationship between the source entity and target entity
Format each relationship as ("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)

3. Return output in English as a single list of all the entities and relationships identified in steps 1 and 2. Use **{record_delimiter}** as the list delimiter.

4. When finished, output {completion_delimiter}

-Examples-
######################
Example 1:

Entity_types: [person, technology, mission, organization, location]
Text:
while Alex clenched his jaw, the weight of the decision pressing down on him. It was the call he had to make, and it was his responsibility to handle the consequences. The clock was ticking, and the stakes were high.

Several hours later, Turner and team met in the designated meeting room. As the team analyzed the situation, they realized that the key to success was not just in the data, but in the strategic approach they would take. Turner's expertise in cybersecurity was critical, and Alex's leadership skills were essential for the team's success.

################
Output:
("entity"<|>Alex<|>person<|>Alex is a person who is facing a difficult decision. He has a responsibility to handle the consequences of his decision. He is also a leader who has leadership skills that are essential for the team's success.)
##
("entity"<|>Turner<|>person<|>Turner is a person who is part of a team. He has expertise in cybersecurity and plays a critical role in the team's success.)
##
("entity"<|>team<|>organization<|>The team is an organization that is working on a mission or project. They are analyzing a situation and developing a strategic approach to achieve success.)
##
("entity"<|>cybersecurity<|>technology<|>Cybersecurity is a technology that is critical to the team's success. It is Turner's area of expertise.)
##
("relationship"<|>Alex<|>Turner<|>Alex and Turner are both part of the same team and are working together on a mission or project<|>7)
##
("relationship"<|>Alex<|>team<|>Alex is the leader of the team and his leadership skills are essential for the team's success<|>9)
##
("relationship"<|>Turner<|>team<|>Turner is a member of the team and his cybersecurity expertise is critical to the team's success<|>8)
##
("relationship"<|>Turner<|>cybersecurity<|>Turner has expertise in cybersecurity, which is critical to the team's success<|>9)
<|COMPLETE|>
#############################
Example 2:

Entity_types: [person, technology, mission, organization, location]
Text:
They were no longer mere operatives; they had become guardians of a threshold, keepers of a message from a realm beyond stars and stripes. This elevation in their mission could not be taken lightly. Each member of the team now carried the weight of a cosmic responsibility.

But it was not just the weight of the responsibility that they carried. They also carried the weight of the knowledge that they were the only ones who could complete this mission. The aliens had chosen them, and they had to live up to that choice.

################
Output:
("entity"<|>operatives<|>person<|>The operatives are people who have been chosen by aliens to complete a mission. They have been elevated from mere operatives to guardians of a threshold, keepers of a message from a realm beyond stars and stripes.)
##
("entity"<|>team<|>organization<|>The team is an organization of operatives who have been chosen by aliens to complete a mission. Each member of the team carries the weight of a cosmic responsibility.)
##
("entity"<|>aliens<|>person<|>The aliens are beings from another realm who have chosen the operatives to complete a mission. They are from a realm beyond stars and stripes.)
##
("entity"<|>mission<|>mission<|>The mission is a cosmic responsibility that the operatives have been chosen to complete. It is a mission that involves being guardians of a threshold and keepers of a message from a realm beyond stars and stripes.)
##
("entity"<|>threshold<|>location<|>The threshold is a location that the operatives are guardians of. It is related to the mission that the operatives have been chosen to complete.)
##
("entity"<|>message<|>technology<|>The message is a piece of technology or information that the operatives are keepers of. It is from a realm beyond stars and stripes.)
##
("relationship"<|>operatives<|>team<|>The operatives are members of the team that has been chosen by aliens to complete a mission<|>9)
##
("relationship"<|>operatives<|>aliens<|>The operatives have been chosen by the aliens to complete a mission<|>8)
##
("relationship"<|>operatives<|>mission<|>The operatives are responsible for completing the mission<|>9)
##
("relationship"<|>operatives<|>threshold<|>The operatives are guardians of the threshold<|>8)
##
("relationship"<|>operatives<|>message<|>The operatives are keepers of the message<|>8)
##
("relationship"<|>team<|>aliens<|>The team has been chosen by the aliens to complete a mission<|>8)
##
("relationship"<|>team<|>mission<|>The team is responsible for completing the mission<|>9)
##
("relationship"<|>aliens<|>mission<|>The aliens have given the mission to the operatives<|>8)
##
("relationship"<|>mission<|>threshold<|>The mission involves being guardians of the threshold<|>8)
##
("relationship"<|>mission<|>message<|>The mission involves being keepers of the message<|>8)
<|COMPLETE|>
######################
-Real Data-
######################
Entity_types: {entity_types}
Text: {input_text}
######################
Output:
""",

    "entity_extraction_examples": """
Examples:
######################
Example 1:

Entity_types: [person, technology, mission, organization, location]
Text:
while Alex clenched his jaw, the weight of the decision pressing down on him. It was the call he had to make, and it was his responsibility to handle the consequences. The clock was ticking, and the stakes were high.

Several hours later, Turner and team met in the designated meeting room. As the team analyzed the situation, they realized that the key to success was not just in the data, but in the strategic approach they would take. Turner's expertise in cybersecurity was critical, and Alex's leadership skills were essential for the team's success.

################
Output:
("entity"<|>Alex<|>person<|>Alex is a person who is facing a difficult decision. He has a responsibility to handle the consequences of his decision. He is also a leader who has leadership skills that are essential for the team's success.)
##
("entity"<|>Turner<|>person<|>Turner is a person who is part of a team. He has expertise in cybersecurity and plays a critical role in the team's success.)
##
("entity"<|>team<|>organization<|>The team is an organization that is working on a mission or project. They are analyzing a situation and developing a strategic approach to achieve success.)
##
("entity"<|>cybersecurity<|>technology<|>Cybersecurity is a technology that is critical to the team's success. It is Turner's area of expertise.)
##
("relationship"<|>Alex<|>Turner<|>Alex and Turner are both part of the same team and are working together on a mission or project<|>7)
##
("relationship"<|>Alex<|>team<|>Alex is the leader of the team and his leadership skills are essential for the team's success<|>9)
##
("relationship"<|>Turner<|>team<|>Turner is a member of the team and his cybersecurity expertise is critical to the team's success<|>8)
##
("relationship"<|>Turner<|>cybersecurity<|>Turner has expertise in cybersecurity, which is critical to the team's success<|>9)
<|COMPLETE|>
""",

    "entity_extraction_w_gleanings": """
-Goal-
Given a text document that is potentially relevant to this activity and a list of entity types, identify all entities of those types from the text and all relationships among the identified entities.

-Steps-
1. Identify all entities. For each identified entity, extract the following information:
- entity_name: Name of the entity, capitalized
- entity_type: One of the following types: [{entity_types}]
- entity_description: Comprehensive description of the entity's attributes and activities
Format each entity as ("entity"<|>entity_name<|>entity_type<|>entity_description)

2. From the entities identified in step 1, identify all pairs of (source_entity, target_entity) that are *clearly related* to each other.
For each pair of related entities, extract the following information:
- source_entity: name of the source entity, as identified in step 1
- target_entity: name of the target entity, as identified in step 1
- relationship_description: explanation of why you think the source entity and the target entity are related to each other
- relationship_strength: a numeric score between 1 to 10, indicating strength of the relationship between the source entity and target entity
Format each relationship as ("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)

3. Return output in English as a single list of all the entities and relationships identified in steps 1 and 2. Use **{record_delimiter}** as the list delimiter.

4. When finished, output {completion_delimiter}

-Examples-
{examples}

-Real Data-
######################
Entity_types: {entity_types}
Text: {input_text}
######################
Output:
""",

    "entity_extraction_gleanings": """
-Goal-
Given a text document that is potentially relevant to this activity, a list of entity types, and a list of entities already identified, identify any new entities of those types from the text and all relationships among the identified entities.

-Steps-
1. Identify all entities. For each identified entity, extract the following information:
- entity_name: Name of the entity, capitalized
- entity_type: One of the following types: [{entity_types}]
- entity_description: Comprehensive description of the entity's attributes and activities
Format each entity as ("entity"<|>entity_name<|>entity_type<|>entity_description)

2. From the entities identified in step 1, identify all pairs of (source_entity, target_entity) that are *clearly related* to each other.
For each pair of related entities, extract the following information:
- source_entity: name of the source entity, as identified in step 1
- target_entity: name of the target entity, as identified in step 1
- relationship_description: explanation of why you think the source entity and the target entity are related to each other
- relationship_strength: a numeric score between 1 to 10, indicating strength of the relationship between the source entity and target entity
Format each relationship as ("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)

3. Return output in English as a single list of all the entities and relationships identified in steps 1 and 2. Use **{record_delimiter}** as the list delimiter.

4. When finished, output {completion_delimiter}

-Examples-
{examples}

-Real Data-
######################
Entity_types: {entity_types}
Text: {input_text}
######################
Entities already identified:
{entity_list}
######################
Output:
""",

    "entity_summarization": """
You are a helpful assistant responsible for generating a comprehensive summary of the data provided below.
Given one or two entities, and a list of descriptions, all related to the same entity or group of entities.
Please concatenate all of these into a single, comprehensive description. Make sure to include information collected from all the descriptions.
If the provided descriptions are contradictory, please resolve the contradictions and provide a single, coherent description.
Make sure it is written in third person, and include the entity names so we the have full context.

#######
Entities: {entity_names}
Descriptions: {descriptions}
#######
Output:
""",

    "entity_relationship_summarization": """
You are a helpful assistant responsible for generating a comprehensive summary of the data provided below.
Given one or two entities, and a list of descriptions, all related to the same entity or group of entities.
Please concatenate all of these into a single, comprehensive description. Make sure to include information collected from all the descriptions.
If the provided descriptions are contradictory, please resolve the contradictions and provide a single, coherent description.
Make sure it is written in third person, and include the entity names so we the have full context.

#######
Entities: {entity_names}
Descriptions: {descriptions}
#######
Output:
""",

    "fail_response": "Sorry, I'm not able to provide an answer to that question.",

    "rag_response": """
---Role---

You are a helpful assistant responding to questions about the data in the tables provided.


---Goal---

Generate a response of the appropriate length and format to answer the user's question, summarizing all information in the input data tables appropriate for the response length and format, and incorporating any relevant general knowledge.
If you don't know the answer, just say so. Do not make anything up.

---Target response length and format---

{response_type}

---Data tables---

{context_data}

---Goal---

Generate a response of the appropriate length and format to answer the user's question, summarizing all information in the input data tables appropriate for the response length and format, and incorporating any relevant general knowledge.
If you don't know the answer, just say so. Do not make anything up.

---Target response length and format---

{response_type}

Add sections and commentary to the response as appropriate for the length and format. Style the response in markdown.
""",

    "keywords_extraction": """
-Goal-
Given a text document that is potentially relevant to this activity, first identify all entities that are mentioned in the text and then extract all meaningful relationships between the identified entities.

-Steps-
1. Identify all entities mentioned in the text. For each identified entity, extract the following information:
- entity_name: Name of the entity, capitalized
- entity_type: Type of the entity
- entity_description: Comprehensive description of the entity's attributes and activities
Format each entity as ("entity"<|>entity_name<|>entity_type<|>entity_description)

2. From the entities identified in step 1, identify all pairs of (source_entity, target_entity) that are *clearly related* to each other.
For each pair of related entities, extract the following information:
- source_entity: name of the source entity, as identified in step 1
- target_entity: name of the target entity, as identified in step 1
- relationship_description: explanation of why you think the source entity and the target entity are related to each other
- relationship_strength: a numeric score between 1 to 10, indicating strength of the relationship between the source entity and target entity
Format each relationship as ("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)

3. Return output in English as a single list of all the entities and relationships identified in steps 1 and 2. Use **{record_delimiter}** as the list delimiter.

4. When finished, output {completion_delimiter}

-Examples-
{examples}

-Real Data-
######################
Text: {input_text}
######################
Output:
""",

    "naive_rag_response": """
---Role---

You are a helpful assistant responding to questions about the data in the tables provided.

---Goal---

Generate a response to answer the user's question, summarizing all information in the input data tables appropriate for the response.

---Target response length and format---

{response_type}

---Data tables---

{context_data}

---Goal---

Generate a response to answer the user's question, summarizing all information in the input data tables appropriate for the response.
If you don't know the answer, just say so. Do not make anything up.

---Target response length and format---

{response_type}

Add sections and commentary to the response as appropriate for the length and format. Style the response in markdown.
""",
}
