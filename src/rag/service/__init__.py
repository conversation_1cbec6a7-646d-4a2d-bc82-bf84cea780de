"""
RAG 核心服务模块

提供 RAG 系统的核心业务逻辑：
- 文本分块服务
- 实体抽取服务  
- 知识图谱构建服务
- 检索服务
"""

# 导入核心服务组件
from .text_chunking import DocumentChunker, DocumentChunk, chunking_by_token_size
from .entity_extraction import EntityExtractor, Entity, Relationship, extract_entities_from_chunks

# 注意：knowledge_graph.py 和 retrieval.py 需要更新导入路径后才能导入
# from .knowledge_graph import KnowledgeGraphBuilder, MixRAGKnowledgeGraph
# from .retrieval import KnowledgeGraphRetriever

__all__ = [
    # 文本分块
    "DocumentChunker",
    "DocumentChunk", 
    "chunking_by_token_size",
    
    # 实体抽取
    "EntityExtractor",
    "Entity",
    "Relationship",
    "extract_entities_from_chunks",
    
    # 知识图谱构建（待更新导入路径）
    # "KnowledgeGraphBuilder",
    # "MixRAGKnowledgeGraph",
    
    # 检索服务（待更新导入路径）
    # "KnowledgeGraphRetriever"
]
