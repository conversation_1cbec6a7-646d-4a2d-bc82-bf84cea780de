"""
知识图谱检索模块

实现基于知识图谱的智能检索功能，支持 local、global、mix 三种检索模式
"""

import asyncio
import traceback
import time
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Set, Tuple

from src.rag.infrastructure.schemas.base import QueryParam
from src.rag.infrastructure.milvus import milvus_vector_operations
from src.rag.infrastructure import neo4j_graph_operations
from src.rag.llm import PROMPTS
from src.rag.llm import call_llm
from src.rag.service.knowledge_graph import MixRAGKnowledgeGraph
from src.rag.tools import (
    logger,
    truncate_list_by_token_size,
    safe_init_async_func,
    limit_async_func_call
)


@dataclass
class RetrievalContext:
    """检索上下文"""
    entities: List[Dict[str, Any]] = field(default_factory=list)
    relationships: List[Dict[str, Any]] = field(default_factory=list)
    chunks: List[Dict[str, Any]] = field(default_factory=list)
    context_text: str = ""
    sources: Set[str] = field(default_factory=set)


class KnowledgeGraphRetriever:
    """知识图谱检索器
    
    支持多种检索模式的智能检索
    """

    def __init__(self, knowledge_graph: MixRAGKnowledgeGraph, llm_func=None):
        """
        初始化知识图谱检索器
        
        Args:
            knowledge_graph: 知识图谱实例
            vector_storage: 向量存储（用于语义检索）
            graph_storage: 图存储（备用）
            llm_func: LLM 模型函数
        """
        self.knowledge_graph = knowledge_graph
        self.llm_func = llm_func or call_llm
        self.query_decomposer = QueryDecomposition()
        logger.info(f"KnowledgeGraphRetriever initialized: nodes={self.knowledge_graph.get_node_count()}, edges={self.knowledge_graph.get_edge_count()}")

    async def _search_entities_by_keywords(
            self,
            query: str,
            top_k: int = 20
    ) -> List[Tuple[str, float]]:
        """根据关键词搜索实体"""
        try:
            query_lower = query.lower()
            query_words = set(query_lower.split())

            # 直接从Neo4j查询实体
            neo4j_query = """
            MATCH (n)
            WHERE n.label IS NOT NULL
              AND (toLower(n.label) CONTAINS $query
                   OR toLower(n.description) CONTAINS $query)
            RETURN n.label as entity_name,
                   COALESCE(n.description, '') as description
            LIMIT $top_k
            """

            result = await neo4j_graph_operations.connection_manager.execute_query(
                neo4j_query,
                {"query": query_lower, "top_k": top_k}
            )

            entity_scores = []
            for record in result:
                entity_name = record["entity_name"]
                description = record["description"]

                # 计算相似度分数
                score = 0.0

                # 计算实体名称相似度
                entity_words = set(entity_name.lower().split())
                name_overlap = len(query_words & entity_words)
                if name_overlap > 0:
                    score += name_overlap / len(query_words) * 2.0

                # 计算描述相似度
                desc_words = set(description.lower().split())
                desc_overlap = len(query_words & desc_words)
                if desc_overlap > 0:
                    score += desc_overlap / len(query_words) * 1.0

                # 包含查询字符串的实体优先
                if query_lower in entity_name.lower():
                    score += 1.5
                if query_lower in description.lower():
                    score += 0.5

                if score > 0:
                    entity_scores.append((entity_name, score))

            # 按分数排序
            entity_scores.sort(key=lambda x: x[1], reverse=True)
            logger.debug(f"Keyword search found {len(entity_scores)} entities for query: {query}")
            return entity_scores[:top_k]

        except Exception as e:
            logger.error(f"关键词搜索失败: {e}")
            return []

    async def _search_entities_by_vector(
            self,
            query: str,
            top_k: int = 20,
            ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """使用向量存储搜索实体"""
        if not milvus_vector_operations:
            return []

        try:
            results = await milvus_vector_operations.query(query, top_k, ids)
            return results
        except Exception as e:
            traceback.print_exc()
            logger.warning(f"Vector search failed: {e}")
            return []

    async def local_retrieval(
            self,
            query: str,
            param: QueryParam
    ) -> RetrievalContext:
        """
        本地检索：基于实体的上下文检索
        
        Args:
            query: 查询字符串
            param: 查询参数
            
        Returns:
            检索上下文
        """
        logger.info(f"Starting local retrieval for query: {query}")

        context = RetrievalContext()

        # 1. 搜索相关实体
        relevant_entities = []

        if milvus_vector_operations:
            # 首先尝试向量搜索
            vector_results = await self._search_entities_by_vector(query, param.top_k)
            relevant_entities = [r.get("entity_name", "") for r in vector_results if r.get("entity_name")]
            logger.debug(f"Vector search found {len(relevant_entities)} entities")

        # 如果向量搜索没有结果，回退到关键词搜索
        if not relevant_entities:
            keyword_results = await self._search_entities_by_keywords(query, param.top_k)
            relevant_entities = [entity for entity, score in keyword_results]
            logger.debug(f"Keyword search found {len(relevant_entities)} entities")

        logger.debug(f"Found {len(relevant_entities)} relevant entities")

        # 2. 获取实体详细信息和邻居
        entity_context = []
        relationship_context = []

        try:
            for entity_name in relevant_entities[:param.top_k]:
                # 从Neo4j获取实体信息
                entity_query = """
                MATCH (n)
                WHERE n.label = $entity_name
                RETURN n.label as entity_name,
                       COALESCE(n.type, 'unknown') as entity_type,
                       COALESCE(n.description, '') as description,
                       COALESCE(n.source_chunk, '') as source_chunk
                LIMIT 1
                """

                entity_result = await neo4j_graph_operations.connection_manager.execute_query(
                    entity_query,
                    {"entity_name": entity_name}
                )

                if not entity_result:
                    continue

                record = entity_result[0]
                entity_info = {
                    "entity_name": record["entity_name"],
                    "entity_type": record["entity_type"],
                    "description": record["description"],
                    "source_chunks": [record["source_chunk"]] if record["source_chunk"] else []
                }
                entity_context.append(entity_info)
                if record["source_chunk"]:
                    context.sources.add(record["source_chunk"])

                # 获取相关关系
                relationship_query = """
                MATCH (s)-[r]->(t)
                WHERE s.label = $entity_name OR t.label = $entity_name
                RETURN s.label as src_id,
                       t.label as tgt_id,
                       type(r) as relationship_type,
                       COALESCE(r.description, type(r)) as description
                LIMIT 5
                """

                rel_result = await neo4j_graph_operations.connection_manager.execute_query(
                    relationship_query,
                    {"entity_name": entity_name}
                )

                for rel_record in rel_result:
                    relationship_info = {
                        "src_id": rel_record["src_id"],
                        "tgt_id": rel_record["tgt_id"],
                        "description": rel_record["description"],
                        "weight": 1.0,
                        "source_chunks": []
                    }
                    relationship_context.append(relationship_info)

        except Exception as e:
            logger.error(f"获取实体上下文失败: {e}")
            # 如果Neo4j查询失败，继续使用空的上下文

        # 3. 按 token 限制截断
        context.entities = truncate_list_by_token_size(
            entity_context,
            key=lambda x: f"{x['entity_name']}: {x['description']}",
            max_token_size=param.max_token_for_local_context
        )

        context.relationships = truncate_list_by_token_size(
            relationship_context,
            key=lambda x: f"{x['src_id']} -> {x['tgt_id']}: {x['description']}",
            max_token_size=param.max_token_for_local_context // 2
        )

        # 4. 生成上下文文本
        context_parts = []

        if context.entities:
            entities_text = "## 相关实体\n"
            for entity in context.entities:
                entities_text += f"- **{entity['entity_name']}** ({entity['entity_type']}): {entity['description']}\n"
            context_parts.append(entities_text)

        if context.relationships:
            relationships_text = "## 相关关系\n"
            for rel in context.relationships:
                relationships_text += f"- {rel['src_id']} -> {rel['tgt_id']}: {rel['description']}\n"
            context_parts.append(relationships_text)

        context.context_text = "\n\n".join(context_parts)

        logger.info(f"Local retrieval completed: {len(context.entities)} entities, {len(context.relationships)} relationships")
        return context

    async def global_retrieval(
            self,
            query: str,
            param: QueryParam
    ) -> RetrievalContext:
        """
        全局检索：基于关系的全局模式检索
        
        Args:
            query: 查询字符串
            param: 查询参数
            
        Returns:
            检索上下文
        """
        logger.info(f"Starting global retrieval for query: {query}")

        context = RetrievalContext()

        # 1. 搜索相关关系
        if milvus_vector_operations:
            # 使用向量搜索关系
            vector_results = await self._search_entities_by_vector(query, param.top_k * 2)
            # 过滤出关系类型的结果
            relationship_results = [r for r in vector_results if "src_id" in r and "tgt_id" in r]
        else:
            # 使用关键词搜索关系
            relationship_results = []
            query_lower = query.lower()

            for (src_id, tgt_id), edge in self.knowledge_graph.edges.items():
                score = 0.0

                # 计算关系描述相似度
                if query_lower in edge.description.lower():
                    score += 1.0

                # 计算实体名称相似度
                if query_lower in src_id.lower() or query_lower in tgt_id.lower():
                    score += 0.5

                if score > 0:
                    rel_info = {
                        "src_id": edge.src_id,
                        "tgt_id": edge.tgt_id,
                        "description": edge.description,
                        "weight": edge.weight,
                        "score": score,
                        "source_chunks": list(edge.source_chunks)
                    }
                    relationship_results.append(rel_info)

            # 按分数排序
            relationship_results.sort(key=lambda x: x.get("score", 0), reverse=True)

        logger.debug(f"Found {len(relationship_results)} relevant relationships")

        # 2. 收集相关实体
        entity_names = set()
        relationship_context = []

        for rel in relationship_results[:param.top_k]:
            relationship_context.append(rel)
            entity_names.add(rel["src_id"])
            entity_names.add(rel["tgt_id"])
            context.sources.update(rel.get("source_chunks", []))

        # 3. 获取实体详细信息
        entity_context = []
        for entity_name in entity_names:
            node = self.knowledge_graph.get_node(entity_name)
            if node:
                entity_info = {
                    "entity_name": node.entity_name,
                    "entity_type": node.entity_type,
                    "description": node.description,
                    "source_chunks": list(node.source_chunks)
                }
                entity_context.append(entity_info)
                context.sources.update(node.source_chunks)

        # 4. 按 token 限制截断
        context.relationships = truncate_list_by_token_size(
            relationship_context,
            key=lambda x: f"{x['src_id']} -> {x['tgt_id']}: {x['description']}",
            max_token_size=param.max_token_for_global_context
        )

        context.entities = truncate_list_by_token_size(
            entity_context,
            key=lambda x: f"{x['entity_name']}: {x['description']}",
            max_token_size=param.max_token_for_global_context // 2
        )

        # 5. 生成上下文文本
        context_parts = []

        if context.relationships:
            relationships_text = "## 关键关系模式\n"
            for rel in context.relationships:
                relationships_text += f"- **{rel['src_id']}** -> **{rel['tgt_id']}**: {rel['description']}\n"
            context_parts.append(relationships_text)

        if context.entities:
            entities_text = "## 相关实体\n"
            for entity in context.entities:
                entities_text += f"- **{entity['entity_name']}** ({entity['entity_type']}): {entity['description']}\n"
            context_parts.append(entities_text)

        context.context_text = "\n\n".join(context_parts)

        logger.info(f"Global retrieval completed: {len(context.entities)} entities, {len(context.relationships)} relationships")
        return context

    async def mix_retrieval(
            self,
            query: str,
            param: QueryParam
    ) -> RetrievalContext:
        """
        混合检索：结合 local 和 global 检索
        
        Args:
            query: 查询字符串
            param: 查询参数
            
        Returns:
            检索上下文
        """
        logger.info(f"Starting mix retrieval for query: {query}")

        # 调整参数以平衡两种检索模式
        local_param = QueryParam(**param.__dict__)
        local_param.top_k = param.top_k // 2
        local_param.max_token_for_local_context = param.max_token_for_local_context // 2

        global_param = QueryParam(**param.__dict__)
        global_param.top_k = param.top_k // 2
        global_param.max_token_for_global_context = param.max_token_for_global_context // 2

        # 并行执行两种检索
        local_context, global_context = await asyncio.gather(
            self.local_retrieval(query, local_param),
            self.global_retrieval(query, global_param)
        )

        # 合并结果
        combined_context = RetrievalContext()
        combined_context.entities = local_context.entities + global_context.entities
        combined_context.relationships = local_context.relationships + global_context.relationships
        combined_context.sources = local_context.sources | global_context.sources

        # 去重实体（基于实体名称）
        seen_entities = set()
        unique_entities = []
        for entity in combined_context.entities:
            if entity["entity_name"] not in seen_entities:
                unique_entities.append(entity)
                seen_entities.add(entity["entity_name"])
        combined_context.entities = unique_entities

        # 去重关系（基于 src_id, tgt_id 组合）
        seen_relationships = set()
        unique_relationships = []
        for rel in combined_context.relationships:
            rel_key = (rel["src_id"], rel["tgt_id"])
            reverse_key = (rel["tgt_id"], rel["src_id"])
            if rel_key not in seen_relationships and reverse_key not in seen_relationships:
                unique_relationships.append(rel)
                seen_relationships.add(rel_key)
        combined_context.relationships = unique_relationships

        # 生成合并的上下文文本
        context_parts = []

        if combined_context.entities:
            entities_text = "## 相关实体\n"
            for entity in combined_context.entities:
                entities_text += f"- **{entity['entity_name']}** ({entity['entity_type']}): {entity['description']}\n"
            context_parts.append(entities_text)

        if combined_context.relationships:
            relationships_text = "## 相关关系\n"
            for rel in combined_context.relationships:
                relationships_text += f"- {rel['src_id']} -> {rel['tgt_id']}: {rel['description']}\n"
            context_parts.append(relationships_text)

        combined_context.context_text = "\n\n".join(context_parts)

        logger.info(f"mix retrieval completed: {len(combined_context.entities)} entities, {len(combined_context.relationships)} relationships")
        return combined_context

    @limit_async_func_call(max_size=4)
    @safe_init_async_func
    async def generate_response(
            self,
            query: str,
            context: RetrievalContext,
            param: QueryParam
    ) -> str:
        """
        基于检索上下文生成回答
        
        Args:
            query: 用户查询
            context: 检索上下文
            param: 查询参数
            
        Returns:
            生成的回答
        """
        if not self.llm_func:
            return context.context_text

        if param.only_need_context:
            return context.context_text

        try:
            # 构建提示词
            prompt_template = PROMPTS.get("rag_response", PROMPTS.get("naive_rag_response", ""))

            if not prompt_template:
                return context.context_text

            prompt = prompt_template.format(
                context_data=context.context_text,
                response_type=param.response_type
            )

            if param.only_need_prompt:
                return prompt

            # 生成回答
            response = await self.llm_func(prompt)

            if response and isinstance(response, str):
                return response.strip()
            else:
                logger.warning("LLM response generation failed")
                return context.context_text

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error generating response: {e}")
            return context.context_text

    async def query(
            self,
            query: str,
            param: Optional[QueryParam] = None
    ) -> str:
        """
        执行查询
        
        Args:
            query: 查询字符串
            param: 查询参数
            
        Returns:
            查询结果
        """
        if param is None:
            param = QueryParam()

        logger.info(f"Starting query with mode: {param.mode}")

        # 根据模式选择检索方法
        if param.mode == "local":
            context = await self.local_retrieval(query, param)
        elif param.mode == "global":
            context = await self.global_retrieval(query, param)
        elif param.mode == "mix":
            context = await self.mix_retrieval(query, param)
        else:
            logger.warning(f"Unknown retrieval mode: {param.mode}, using mix")
            context = await self.mix_retrieval(query, param)

        # 生成回答
        response = await self.generate_response(query, context, param)

        logger.info(f"Query completed successfully")
        return response


@dataclass
class QueryDecomposition:
    """查询分解"""

    async def decompose(self, query: str) -> List[str]:
        """
        将复杂查询分解为多个子查询
        
        Args:
            query: 原始查询
            
        Returns:
            子查询列表
        """
        # 这里可以实现查询分解逻辑
        # 暂时返回原查询
        sub_queries = [query]
        return sub_queries


# 全局检索器实例
knowledge_graph_retriever = KnowledgeGraphRetriever(
    knowledge_graph=MixRAGKnowledgeGraph(),
    llm_func=call_llm,
)
