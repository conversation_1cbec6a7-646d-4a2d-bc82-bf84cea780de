"""
实体关系抽取模块

🧠 核心功能：
- 从文本分块中识别和提取实体
- 发现实体间的关系和连接
- 支持多轮精炼抽取策略
- 异步并发处理优化

🎯 抽取策略：
- 基于LLM的智能实体识别
- 多类型实体支持（人物、组织、地点、事件等）
- 关系强度量化评估
- 上下文感知的实体消歧

📊 输出格式：
- 结构化实体对象
- 带权重的关系图谱
- 可追溯的数据来源
"""

import asyncio
import re
import traceback
from dataclasses import dataclass
from typing import List, Dict, Any, Callable, Tuple

from src.rag.tools import (
    logger,
    compute_mdhash_id,
    split_string_by_multi_markers,
    limit_async_func_call,
    safe_init_async_func
)
from src.rag.llm import PROMPTS

# 添加缺失的工具函数
def is_float_regex(value: str) -> bool:
    """使用正则表达式检查是否为浮点数"""
    pattern = r'^[-+]?(?:\d+\.?\d*|\.\d+)(?:[eE][-+]?\d+)?$'
    return bool(re.match(pattern, value.strip()))


@dataclass
class Entity:
    """
    实体数据结构

    表示从文本中抽取的命名实体

    Attributes:
        entity_name: 实体名称（标准化后）
        entity_type: 实体类型（如：person, organization, location等）
        description: 实体描述信息
        source_id: 来源文本块ID
    """
    entity_name: str
    entity_type: str
    description: str
    source_id: str


@dataclass
class Relationship:
    """
    关系数据结构

    表示实体间的语义关系

    Attributes:
        src_id: 源实体ID
        tgt_id: 目标实体ID
        description: 关系描述
        keywords: 关系关键词
        weight: 关系强度权重（0.0-10.0）
        source_id: 来源文本块ID
    """
    src_id: str
    tgt_id: str
    description: str
    keywords: str
    weight: float
    source_id: str


class EntityExtractor:
    """
    🧠 实体关系抽取器

    基于大语言模型的智能实体关系抽取系统，支持：
    - 多类型实体识别（人物、组织、地点、事件等）
    - 实体间关系发现和量化
    - 多轮精炼抽取策略
    - 异步并发处理优化

    抽取流程：
    1. 初始抽取：使用基础提示词进行第一轮抽取
    2. 精炼抽取：基于已有结果进行补充抽取
    3. 结果合并：去重并整合所有抽取结果
    4. 质量验证：验证实体和关系的有效性
    """

    def __init__(
            self,
            llm_model_func: Callable,
            entity_types: List[str] = None,
            max_gleaning_rounds: int = 1,
            extract_max_retry: int = 3
    ):
        """
        初始化实体抽取器

        Args:
            llm_model_func: LLM模型调用函数
            entity_types: 目标实体类型列表，默认包含组织、人物、地理、事件
            max_gleaning_rounds: 最大精炼轮数，用于补充抽取
            extract_max_retry: 单次抽取的最大重试次数
        """
        self.llm_model_func = llm_model_func
        self.entity_types = entity_types or PROMPTS.get("DEFAULT_ENTITY_TYPES", [
            "organization", "person", "geo", "event"
        ])
        self.max_gleaning_rounds = max_gleaning_rounds
        self.extract_max_retry = extract_max_retry

        # 分隔符配置
        self.tuple_delimiter = PROMPTS.get("DEFAULT_TUPLE_DELIMITER", "<|>")
        self.record_delimiter = PROMPTS.get("DEFAULT_RECORD_DELIMITER", "##")
        self.completion_delimiter = PROMPTS.get("DEFAULT_COMPLETION_DELIMITER", "<|COMPLETE|>")

        logger.info(
            f"EntityExtractor initialized: entity_types={self.entity_types}, "
            f"max_gleaning_rounds={max_gleaning_rounds}"
        )

    def _clean_str(self, text: str) -> str:
        """清理字符串"""
        if not text:
            return ""
        return re.sub(r'[^\w\s\-_\u4e00-\u9fff]', '', text).strip()

    def _parse_extraction_results(self, raw_result: str) -> Tuple[List[Entity], List[Relationship]]:
        """解析抽取结果"""
        entities = []
        relationships = []

        logger.debug(f"开始解析抽取结果，原始内容长度: {len(raw_result)}")
        logger.debug(f"原始内容前200字符: {raw_result[:200]}")

        # 移除完成标记并按记录分割
        content = raw_result.replace(self.completion_delimiter, "").strip()

        if not content:
            logger.debug("内容为空，返回空结果")
            return entities, relationships

        logger.debug(f"处理后内容长度: {len(content)}")

        # 按记录分隔符分割
        records = split_string_by_multi_markers(content, [self.record_delimiter])
        logger.debug(f"分割后记录数: {len(records)}")

        for i, record in enumerate(records):
            record = record.strip()
            if not record:
                continue

            logger.debug(f"处理记录 {i}: {record[:100]}")

            # 按元组分隔符分割
            parts = split_string_by_multi_markers(record, [self.tuple_delimiter])
            logger.debug(f"记录 {i} 分割后部分数: {len(parts)}")

            if len(parts) < 2:
                logger.debug(f"记录 {i} 部分数不足，跳过")
                continue

            record_type = parts[0].strip().lower()
            logger.debug(f"记录 {i} 类型: {record_type}")

            if "entity" in record_type:
                logger.debug(f"解析实体记录 {i}")
                # 解析实体: ("entity"<|>entity_name<|>entity_type<|>entity_description)
                if len(parts) >= 4:
                    entity_name = self._clean_str(parts[1])
                    entity_type = self._clean_str(parts[2])
                    description = parts[3].strip()

                    logger.debug(f"实体: {entity_name}, 类型: {entity_type}")

                    if entity_name and entity_type:
                        entity = Entity(
                            entity_name=entity_name,
                            entity_type=entity_type,
                            description=description,
                            source_id=""  # 稍后填充
                        )
                        entities.append(entity)
                        logger.debug(f"成功添加实体: {entity_name}")

            elif "relationship" in record_type:
                logger.debug(f"解析关系记录 {i}")
                # 解析关系: ("relationship"<|>source_entity<|>target_entity<|>relationship_description<|>relationship_strength)
                if len(parts) >= 5:
                    src_id = self._clean_str(parts[1])
                    tgt_id = self._clean_str(parts[2])
                    description = parts[3].strip()
                    weight_str = parts[4].strip()

                    logger.debug(f"关系: {src_id} -> {tgt_id}, 权重: {weight_str}")

                    # 解析权重
                    try:
                        if is_float_regex(weight_str):
                            weight = float(weight_str)
                        else:
                            weight = 1.0
                    except (ValueError, TypeError):
                        weight = 1.0

                    if src_id and tgt_id:
                        relationship = Relationship(
                            src_id=src_id,
                            tgt_id=tgt_id,
                            description=description,
                            keywords="",  # 可以从描述中提取
                            weight=weight,
                            source_id=""  # 稍后填充
                        )
                        relationships.append(relationship)
                        logger.debug(f"成功添加关系: {src_id} -> {tgt_id}")

        return entities, relationships

    @limit_async_func_call(max_size=16)
    @safe_init_async_func
    async def _extract_entities_from_text(
            self,
            text: str,
            source_id: str,
            gleaning_round: int = 0,
            history_entities: List[Entity] = None
    ) -> Tuple[List[Entity], List[Relationship]]:
        """从文本中抽取实体和关系"""

        # 选择合适的提示词
        if gleaning_round == 0:
            # 第一轮抽取
            prompt_template = PROMPTS["entity_extraction"]
            examples = ""
        else:
            # 精炼轮次
            prompt_template = PROMPTS["entity_extraction_gleanings"]
            examples = PROMPTS.get("entity_extraction_examples", "")

        # 构建提示词
        entity_types_str = ", ".join(self.entity_types)

        # 构建提示词参数
        format_kwargs = {
            "entity_types": entity_types_str,
            "input_text": text,
            "examples": examples,
            "record_delimiter": self.record_delimiter,
            "completion_delimiter": self.completion_delimiter,
        }

        # 检查是否需要 entity_list 参数
        if gleaning_round > 0 and history_entities:
            # 构建已知实体列表
            entity_list = "\n".join([
                f"- {e.entity_name} ({e.entity_type}): {e.description}"
                for e in history_entities
            ])
            format_kwargs["entity_list"] = entity_list

        prompt = prompt_template.format(**format_kwargs)

        # 调用 LLM
        for retry in range(self.extract_max_retry):
            try:
                result = await self.llm_model_func(prompt)

                if not result or not isinstance(result, str):
                    logger.warning(f"Invalid LLM response on retry {retry + 1}")
                    continue

                # 解析结果
                entities, relationships = self._parse_extraction_results(result)

                # 设置 source_id
                for entity in entities:
                    entity.source_id = source_id
                for relationship in relationships:
                    relationship.source_id = source_id

                logger.debug(
                    f"Extracted {len(entities)} entities and {len(relationships)} "
                    f"relationships from source {source_id} (round {gleaning_round})"
                )

                return entities, relationships

            except Exception as e:
                traceback.print_exc()
                logger.warning(f"Error in entity extraction (retry {retry + 1}): {e}")
                if retry == self.extract_max_retry - 1:
                    logger.error(f"Failed to extract entities after {self.extract_max_retry} retries")
                    return [], []

        return [], []

    async def extract_from_chunk(
            self,
            chunk_content: str,
            chunk_id: str
    ) -> Tuple[List[Entity], List[Relationship]]:
        """
        从单个文本块抽取实体和关系

        Args:
            chunk_content: 文本块内容
            chunk_id: 文本块 ID

        Returns:
            (实体列表, 关系列表)
        """
        if not chunk_content or not chunk_content.strip():
            return [], []

        logger.info(f"Extracting entities from chunk {chunk_id}")

        all_entities = []
        all_relationships = []

        # 第一轮抽取
        entities, relationships = await self._extract_entities_from_text(
            chunk_content, chunk_id, gleaning_round=0
        )
        all_entities.extend(entities)
        all_relationships.extend(relationships)

        # 精炼轮次
        for round_num in range(1, self.max_gleaning_rounds + 1):
            logger.debug(f"Starting gleaning round {round_num} for chunk {chunk_id}")

            gleaned_entities, gleaned_relationships = await self._extract_entities_from_text(
                chunk_content, chunk_id, gleaning_round=round_num, history_entities=all_entities
            )

            # 合并新发现的实体和关系
            existing_entity_names = {e.entity_name for e in all_entities}
            new_entities = [e for e in gleaned_entities if e.entity_name not in existing_entity_names]
            all_entities.extend(new_entities)

            # 关系去重（基于 src_id, tgt_id 组合）
            existing_relationships = {(r.src_id, r.tgt_id) for r in all_relationships}
            new_relationships = [
                r for r in gleaned_relationships
                if (r.src_id, r.tgt_id) not in existing_relationships
            ]
            all_relationships.extend(new_relationships)

            if not new_entities and not new_relationships:
                logger.debug(f"No new entities/relationships found in round {round_num}")
                break

        logger.info(
            f"Completed extraction for chunk {chunk_id}: "
            f"{len(all_entities)} entities, {len(all_relationships)} relationships"
        )

        return all_entities, all_relationships

    async def extract_from_chunks_batch(
            self,
            chunks: List[Dict[str, Any]]
    ) -> Tuple[List[Entity], List[Relationship]]:
        """
        批量从多个文本块抽取实体和关系
        
        Args:
            chunks: 文本块列表，每个包含 'content' 和 'chunk_id'
            
        Returns:
            (所有实体列表, 所有关系列表)
        """
        logger.info(f"Starting batch entity extraction for {len(chunks)} chunks")

        # 并行处理所有块
        tasks = []
        for chunk in chunks:
            content = chunk.get('content', '')
            chunk_id = chunk.get('chunk_id', compute_mdhash_id(content, prefix="chunk-"))
            tasks.append(self.extract_from_chunk(content, chunk_id))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 合并结果
        all_entities = []
        all_relationships = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error processing chunk {i}: {result}")
                continue

            entities, relationships = result
            all_entities.extend(entities)
            all_relationships.extend(relationships)

        logger.info(
            f"Batch extraction completed: {len(all_entities)} total entities, "
            f"{len(all_relationships)} total relationships"
        )

        return all_entities, all_relationships


# 兼容性函数
async def extract_entities_from_chunks(
        chunks: List[Dict[str, Any]],
        llm_model_func: Callable,
        entity_types: List[str] = None,
        max_gleaning_rounds: int = 1
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    从文本块中抽取实体和关系（兼容性函数）
    
    Args:
        chunks: 文本块列表
        llm_model_func: LLM 模型函数
        entity_types: 实体类型列表
        max_gleaning_rounds: 最大精炼轮数
        
    Returns:
        (实体字典列表, 关系字典列表)
    """
    extractor = EntityExtractor(
        llm_model_func=llm_model_func,
        entity_types=entity_types,
        max_gleaning_rounds=max_gleaning_rounds
    )

    entities, relationships = await extractor.extract_from_chunks_batch(chunks)

    # 转换为字典格式
    entity_dicts = []
    for entity in entities:
        entity_dicts.append({
            "entity_name": entity.entity_name,
            "entity_type": entity.entity_type,
            "description": entity.description,
            "source_id": entity.source_id
        })

    relationship_dicts = []
    for relationship in relationships:
        relationship_dicts.append({
            "src_id": relationship.src_id,
            "tgt_id": relationship.tgt_id,
            "description": relationship.description,
            "keywords": relationship.keywords,
            "weight": relationship.weight,
            "source_id": relationship.source_id
        })

    return entity_dicts, relationship_dicts
