"""
知识图谱构建模块

🕸️ 核心功能：
- 将抽取的实体和关系构建成知识图谱
- 支持图谱的增量更新和合并
- 提供图谱的查询和遍历接口
- 实现图谱的持久化存储

🎯 构建策略：
- 实体去重和合并
- 关系权重聚合
- 描述信息整合
- 来源追踪管理

📊 图谱特性：
- 内存高效存储
- 快速查询访问
- 支持复杂图算法
- 可扩展的数据结构
"""

import asyncio
import traceback
from collections import defaultdict
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Callable, Set, Tuple

from src.rag.llm import PROMPTS
from src.rag.service.entity_extraction import Entity, Relationship
from src.rag.tools import (
    logger,
    safe_init_async_func,
    limit_async_func_call
)


@dataclass
class KnowledgeGraphNode:
    """
    知识图谱节点

    表示图谱中的实体节点，支持多源信息合并

    Attributes:
        entity_name: 实体名称（唯一标识）
        entity_type: 实体类型
        description: 主要描述信息
        source_chunks: 来源文本块ID集合
        merged_descriptions: 合并的所有描述信息
    """
    entity_name: str
    entity_type: str
    description: str
    source_chunks: Set[str] = field(default_factory=set)
    merged_descriptions: List[str] = field(default_factory=list)


@dataclass
class KnowledgeGraphEdge:
    """
    知识图谱边

    表示实体间的关系连接，支持权重和多源合并

    Attributes:
        src_id: 源实体ID
        tgt_id: 目标实体ID
        description: 关系描述
        keywords: 关系关键词
        weight: 关系权重
        source_chunks: 来源文本块ID集合
        merged_descriptions: 合并的所有关系描述
    """
    src_id: str
    tgt_id: str
    description: str
    keywords: str
    weight: float
    source_chunks: Set[str] = field(default_factory=set)
    merged_descriptions: List[str] = field(default_factory=list)


class MixRAGKnowledgeGraph:
    """
    🕸️ MixRAG内存知识图谱

    高效的内存知识图谱实现，支持：
    - 快速节点和边的增删改查
    - 邻接表优化的图遍历
    - 实体和关系的智能合并
    - 多源信息的统一管理

    数据结构：
    - nodes: 实体节点字典 {entity_name: KnowledgeGraphNode}
    - edges: 关系边字典 {(src, tgt): KnowledgeGraphEdge}
    - adjacency: 邻接表 {node: {neighbors}}
    """

    def __init__(self):
        """初始化知识图谱"""
        self.nodes: Dict[str, KnowledgeGraphNode] = {}
        self.edges: Dict[Tuple[str, str], KnowledgeGraphEdge] = {}
        self.adjacency: Dict[str, Set[str]] = defaultdict(set)

        logger.info("🕸️ MixRAG知识图谱已初始化")

    def add_node(self, entity: Entity) -> None:
        """
        添加实体节点到知识图谱

        如果实体已存在，则合并描述信息和来源；
        如果是新实体，则创建新节点。

        Args:
            entity: 要添加的实体对象
        """
        if entity.entity_name in self.nodes:
            # 合并已存在实体的信息
            existing_node = self.nodes[entity.entity_name]
            existing_node.merged_descriptions.append(entity.description)
            existing_node.source_chunks.add(entity.source_id)
        else:
            # 创建新实体节点
            node = KnowledgeGraphNode(
                entity_name=entity.entity_name,
                entity_type=entity.entity_type,
                description=entity.description,
                source_chunks={entity.source_id},
                merged_descriptions=[entity.description]
            )
            self.nodes[entity.entity_name] = node

    def add_edge(self, relationship: Relationship) -> None:
        """添加边到知识图谱"""
        # 确保节点存在
        if relationship.src_id not in self.nodes:
            # 创建虚拟节点
            self.add_node(Entity(
                entity_name=relationship.src_id,
                entity_type="unknown",
                description=f"Entity {relationship.src_id}",
                source_id=relationship.source_id
            ))

        if relationship.tgt_id not in self.nodes:
            # 创建虚拟节点
            self.add_node(Entity(
                entity_name=relationship.tgt_id,
                entity_type="unknown",
                description=f"Entity {relationship.tgt_id}",
                source_id=relationship.source_id
            ))

        # 标准化边的键（保证一致性）
        edge_key = (relationship.src_id, relationship.tgt_id)
        reverse_key = (relationship.tgt_id, relationship.src_id)

        # 检查是否已存在边（任一方向）
        existing_key = None
        if edge_key in self.edges:
            existing_key = edge_key
        elif reverse_key in self.edges:
            existing_key = reverse_key

        if existing_key:
            # 合并边信息
            existing_edge = self.edges[existing_key]
            existing_edge.merged_descriptions.append(relationship.description)
            existing_edge.source_chunks.add(relationship.source_id)
            existing_edge.weight += relationship.weight  # 累加权重
        else:
            # 创建新边
            edge = KnowledgeGraphEdge(
                src_id=relationship.src_id,
                tgt_id=relationship.tgt_id,
                description=relationship.description,
                keywords=relationship.keywords,
                weight=relationship.weight,
                source_chunks={relationship.source_id},
                merged_descriptions=[relationship.description]
            )
            self.edges[edge_key] = edge

            # 更新邻接表
            self.adjacency[relationship.src_id].add(relationship.tgt_id)
            self.adjacency[relationship.tgt_id].add(relationship.src_id)

    def get_node(self, entity_name: str) -> Optional[KnowledgeGraphNode]:
        """获取节点"""
        return self.nodes.get(entity_name)

    def get_edge(self, src_id: str, tgt_id: str) -> Optional[KnowledgeGraphEdge]:
        """获取边"""
        edge = self.edges.get((src_id, tgt_id))
        if edge is None:
            edge = self.edges.get((tgt_id, src_id))
        return edge

    def get_neighbors(self, entity_name: str) -> Set[str]:
        """获取邻居节点"""
        return self.adjacency.get(entity_name, set())

    def get_node_count(self) -> int:
        """获取节点数量"""
        return len(self.nodes)

    def get_edge_count(self) -> int:
        """获取边数量"""
        return len(self.edges)

    def get_subgraph(self, entity_names: Set[str]) -> 'MixRAGKnowledgeGraph':
        """获取子图"""
        subgraph = MixRAGKnowledgeGraph()

        # 添加指定的节点
        for entity_name in entity_names:
            if entity_name in self.nodes:
                node = self.nodes[entity_name]
                # 重建 Entity 对象来添加节点
                entity = Entity(
                    entity_name=node.entity_name,
                    entity_type=node.entity_type,
                    description=node.description,
                    source_id=",".join(node.source_chunks)
                )
                subgraph.add_node(entity)

        # 添加这些节点之间的边
        for (src_id, tgt_id), edge in self.edges.items():
            if src_id in entity_names and tgt_id in entity_names:
                # 重建 Relationship 对象来添加边
                relationship = Relationship(
                    src_id=edge.src_id,
                    tgt_id=edge.tgt_id,
                    description=edge.description,
                    keywords=edge.keywords,
                    weight=edge.weight,
                    source_id=",".join(edge.source_chunks)
                )
                subgraph.add_edge(relationship)

        return subgraph

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        nodes_dict = {}
        for name, node in self.nodes.items():
            nodes_dict[name] = {
                "entity_name": node.entity_name,
                "entity_type": node.entity_type,
                "description": node.description,
                "source_chunks": list(node.source_chunks),
                "merged_descriptions": node.merged_descriptions
            }

        edges_dict = {}
        for (src, tgt), edge in self.edges.items():
            edge_key = f"{src}-{tgt}"
            edges_dict[edge_key] = {
                "src_id": edge.src_id,
                "tgt_id": edge.tgt_id,
                "description": edge.description,
                "keywords": edge.keywords,
                "weight": edge.weight,
                "source_chunks": list(edge.source_chunks),
                "merged_descriptions": edge.merged_descriptions
            }

        return {
            "nodes": nodes_dict,
            "edges": edges_dict,
            "node_count": len(self.nodes),
            "edge_count": len(self.edges)
        }


class KnowledgeGraphBuilder:
    """知识图谱构建器
    
    负责从实体和关系构建知识图谱
    """

    def __init__(
            self,
            llm_model_func: Optional[Callable] = None,
            enable_llm_summarization: bool = True
    ):
        """
        初始化知识图谱构建器
        
        Args:
            llm_model_func: LLM 模型函数，用于摘要生成
            enable_llm_summarization: 是否启用 LLM 辅助摘要
        """
        self.llm_model_func = llm_model_func
        self.enable_llm_summarization = enable_llm_summarization

        logger.info(
            f"KnowledgeGraphBuilder initialized: "
            f"llm_summarization={enable_llm_summarization}"
        )

    @limit_async_func_call(max_size=8)
    @safe_init_async_func
    async def _summarize_descriptions(
            self,
            entity_names: List[str],
            descriptions: List[str]
    ) -> str:
        """使用 LLM 摘要描述"""
        if not self.llm_model_func or not self.enable_llm_summarization:
            # 简单连接描述
            return " ".join(descriptions)

        if len(descriptions) == 1:
            return descriptions[0]

        try:
            entity_names_str = ", ".join(entity_names)
            descriptions_str = "\n".join(descriptions)

            prompt = PROMPTS["entity_summarization"].format(
                entity_names=entity_names_str,
                descriptions=descriptions_str
            )

            summary = await self.llm_model_func(prompt)

            if summary and isinstance(summary, str):
                return summary.strip()
            else:
                logger.warning("LLM summarization failed, using simple concatenation")
                return " ".join(descriptions)

        except Exception as e:
            traceback.print_exc()
            logger.warning(f"Error in LLM summarization: {e}")
            return " ".join(descriptions)

    async def _merge_entity_descriptions(self, node: KnowledgeGraphNode) -> None:
        """合并实体描述"""
        if len(node.merged_descriptions) <= 1:
            return

        # 使用 LLM 摘要或简单合并
        summarized = await self._summarize_descriptions(
            [node.entity_name],
            node.merged_descriptions
        )

        node.description = summarized

    async def _merge_relationship_descriptions(self, edge: KnowledgeGraphEdge) -> None:
        """合并关系描述"""
        if len(edge.merged_descriptions) <= 1:
            return

        # 使用 LLM 摘要或简单合并
        summarized = await self._summarize_descriptions(
            [edge.src_id, edge.tgt_id],
            edge.merged_descriptions
        )

        edge.description = summarized

    async def build_graph(
            self,
            entities: List[Entity],
            relationships: List[Relationship]
    ) -> MixRAGKnowledgeGraph:
        """
        构建知识图谱
        
        Args:
            entities: 实体列表
            relationships: 关系列表
            
        Returns:
            构建的知识图谱
        """
        logger.info(f"Building knowledge graph from {len(entities)} entities and {len(relationships)} relationships")

        # 创建知识图谱
        kg = MixRAGKnowledgeGraph()

        # 添加所有实体
        for entity in entities:
            kg.add_node(entity)

        # 添加所有关系
        for relationship in relationships:
            kg.add_edge(relationship)

        logger.info(f"Initial graph built: {kg.get_node_count()} nodes, {kg.get_edge_count()} edges")

        # 合并描述
        if self.enable_llm_summarization and self.llm_model_func:
            logger.info("Starting LLM-assisted summarization")

            # 并行处理节点摘要
            node_tasks = []
            for node in kg.nodes.values():
                if len(node.merged_descriptions) > 1:
                    node_tasks.append(self._merge_entity_descriptions(node))

            # 并行处理边摘要
            edge_tasks = []
            for edge in kg.edges.values():
                if len(edge.merged_descriptions) > 1:
                    edge_tasks.append(self._merge_relationship_descriptions(edge))

            # 等待所有摘要完成
            if node_tasks:
                await asyncio.gather(*node_tasks, return_exceptions=True)
            if edge_tasks:
                await asyncio.gather(*edge_tasks, return_exceptions=True)

            logger.info("LLM summarization completed")

        logger.info(f"Knowledge graph built successfully: {kg.get_node_count()} nodes, {kg.get_edge_count()} edges")
        return kg

    async def build_graph_from_chunks_results(
            self,
            extraction_results: List[Tuple[List[Entity], List[Relationship]]]
    ) -> MixRAGKnowledgeGraph:
        """
        从多个块的抽取结果构建知识图谱
        
        Args:
            extraction_results: 每个块的抽取结果列表
            
        Returns:
            构建的知识图谱
        """
        # 合并所有实体和关系
        all_entities = []
        all_relationships = []

        for entities, relationships in extraction_results:
            all_entities.extend(entities)
            all_relationships.extend(relationships)

        return await self.build_graph(all_entities, all_relationships)

    def filter_graph_by_entity_types(
            self,
            kg: MixRAGKnowledgeGraph,
            allowed_types: Set[str]
    ) -> MixRAGKnowledgeGraph:
        """
        根据实体类型过滤知识图谱
        
        Args:
            kg: 原始知识图谱
            allowed_types: 允许的实体类型集合
            
        Returns:
            过滤后的知识图谱
        """
        # 找到符合类型的实体
        filtered_entities = set()
        for name, node in kg.nodes.items():
            if node.entity_type in allowed_types:
                filtered_entities.add(name)

        # 构建子图
        return kg.get_subgraph(filtered_entities)

    def get_entity_statistics(self, kg: MixRAGKnowledgeGraph) -> Dict[str, Any]:
        """
        获取实体统计信息
        
        Args:
            kg: 知识图谱
            
        Returns:
            统计信息
        """
        entity_types = defaultdict(int)
        degree_distribution = defaultdict(int)

        for name, node in kg.nodes.items():
            entity_types[node.entity_type] += 1
            degree = len(kg.get_neighbors(name))
            degree_distribution[degree] += 1

        return {
            "total_nodes": kg.get_node_count(),
            "total_edges": kg.get_edge_count(),
            "entity_types": dict(entity_types),
            "degree_distribution": dict(degree_distribution),
            "average_degree": sum(len(neighbors) for neighbors in kg.adjacency.values()) / max(1, len(kg.nodes))
        }
