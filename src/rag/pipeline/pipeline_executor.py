"""
流水线执行器

负责定时从数据库中获取待执行的流水线并执行
支持并发执行和线程池管理
"""

import asyncio
import os
import traceback
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Dict, Optional, Any, Set

# 精确导入 - 避免循环依赖
from src.rag.infrastructure.db.pipeline_operations import pipeline_operations
from src.rag.infrastructure.db.client import postgre_sql_client
from src.rag.tools import logger
from .models import PipelineStatus, TaskStatus, Pipeline, TaskType, PipelineTask, TaskResult
from .pipeline_monitor import PipelineMonitor


@dataclass
class ExecutorStats:
    """执行器统计信息"""
    running_pipelines: int = 0
    completed_pipelines: int = 0
    failed_pipelines: int = 0
    total_tasks_processed: int = 0
    last_check_time: Optional[datetime] = None
    uptime_seconds: float = 0.0


def _initialize_processors() -> Dict[TaskType, Any]:
    """
    初始化所有处理器
    
    Returns:
        处理器字典，键为TaskType，值为处理器实例
    """
    from .processors import ProcessorFactory
    
    # 获取所有注册的处理器类型
    task_types = ProcessorFactory.get_supported_task_types()
    
    # 为每个任务类型创建处理器实例
    processors = {}
    for task_type in task_types:
        processor_instance = ProcessorFactory.create_processor(task_type)
        processors[task_type] = processor_instance
    
    logger.info(f"🔧 [流水线执行器] 已初始化 {len(processors)} 个处理器")
    
    return processors


class PipelineExecutor:
    """流水线执行器"""

    def __init__(self):
        # 从配置文件加载配置
        from dotenv import load_dotenv
        config_path = os.path.join(os.path.dirname(__file__), "..", "..", "..", "config.env")
        load_dotenv(config_path)

        self.enabled = os.getenv("PIPELINE_EXECUTOR_ENABLED", "true").lower() == "true"
        self.check_interval = int(os.getenv("PIPELINE_EXECUTOR_INTERVAL", "5"))  # 秒
        self.max_threads = int(os.getenv("PIPELINE_EXECUTOR_THREADS", "4"))
        self.max_concurrent_pipelines = int(os.getenv("PIPELINE_EXECUTOR_MAX_CONCURRENT_PIPELINES", "10"))
        self.batch_size = int(os.getenv("PIPELINE_EXECUTOR_BATCH_SIZE", "5"))

        # 运行时状态
        self.is_running = False
        self.executor_task = None
        self.thread_pool = None
        self.running_pipelines: Dict[str, asyncio.Task] = {}  # pipeline_id -> monitoring_task
        self.executing_tasks: Set[str] = set()  # 正在执行的任务ID集合，防止重复执行
        self.stats = ExecutorStats()
        self.start_time = None
        
        # 初始化处理器
        self.processors = _initialize_processors()
        # 初始化监控器
        self.monitor = PipelineMonitor(self)

        logger.info(f"🚀 [流水线执行器] 初始化完成")
        logger.info(f"   ⚙️ 启用状态: {self.enabled}")
        logger.info(f"   ⏱️ 检查间隔: {self.check_interval} 秒")
        logger.info(f"   🧵 最大线程数: {self.max_threads}")
        logger.info(f"   📊 最大并发流水线: {self.max_concurrent_pipelines}")
        logger.info(f"   📦 批处理大小: {self.batch_size}")

    async def start(self):
        """启动执行器"""
        if self.is_running:
            logger.warning("流水线执行器已在运行中")
            return

        if not self.enabled:
            logger.warning("流水线执行器已禁用，跳过启动")
            return

        self.is_running = True
        self.start_time = datetime.utcnow()

        # 创建线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_threads)

        # 启动执行循环
        self.executor_task = asyncio.create_task(self._execution_loop())

        logger.info(f"🚀 [流水线执行器] 已启动，检查间隔: {self.check_interval} 秒")

    async def stop(self):
        """停止执行器"""
        if not self.is_running:
            return

        logger.info("🛑 [流水线执行器] 正在停止...")

        self.is_running = False

        # 取消执行任务
        if self.executor_task:
            self.executor_task.cancel()
            try:
                await self.executor_task
            except asyncio.CancelledError:
                pass

        # 等待所有运行中的流水线监控任务完成
        if self.running_pipelines:
            logger.info(f"等待 {len(self.running_pipelines)} 个流水线监控任务完成...")
            await asyncio.gather(*self.running_pipelines.values(), return_exceptions=True)

        # 关闭线程池
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)

        logger.info("✅ [流水线执行器] 已停止")

    async def _execution_loop(self):
        """主执行循环"""
        logger.info("🔄 [流水线执行器] 开始执行循环")

        while self.is_running:
            try:
                # 检查并启动新的流水线
                await self._check_and_start_pipelines()

                # 检查并启动可执行的任务
                await self._check_and_start_tasks()

                # 清理已完成的监控任务
                await self._cleanup_completed_pipeline_tasks()

                # 更新统计信息
                self._update_stats()

            except Exception as e:
                logger.error(f"💥 [流水线执行器] 执行循环异常: {e}")
                traceback.print_exc()

            # 等待下次检查
            await asyncio.sleep(self.check_interval)

        logger.info("🔄 [流水线执行器] 执行循环已结束")

    async def _check_and_start_pipelines(self):
        """检查并启动待处理的流水线"""
        # 检查是否可以启动新的流水线
        if len(self.running_pipelines) >= self.max_concurrent_pipelines:
            return

        # 获取待处理的流水线
        pending_pipelines = await pipeline_operations.get_pending_pipelines(self.batch_size)

        if pending_pipelines:
            logger.info(f"  发现 {len(pending_pipelines)} 个待处理流水线")

            # 启动待执行的流水线（按创建时间排序，先创建的先执行）
            pipelines_to_start = pending_pipelines[:len(pending_pipelines)]
            for pipeline in pipelines_to_start:
                await self._start_pipeline_execution(pipeline)

            # 然后检查正在运行的流水线，为新的运行中流水线创建监控任务
            running_pipelines = await pipeline_operations.get_pipelines(
                status=PipelineStatus.RUNNING,
                page=1,
                page_size=self.max_concurrent_pipelines
            )

            for pipeline in running_pipelines:
                pipeline_id = pipeline.id
                if pipeline_id not in self.running_pipelines:
                    # 为已经在运行但没有监控任务的流水线创建监控任务
                    logger.info(f"🔍 [流水线执行器] 为运行中流水线创建监控任务: {pipeline_id}")
                    monitor_task = asyncio.create_task(self.monitor.monitor_pipeline(pipeline_id))
                    self.running_pipelines[pipeline_id] = monitor_task

    async def _start_pipeline_execution(self, pipeline: Pipeline):
        """启动单个流水线的执行"""
        pipeline_id = pipeline.id

        try:
            logger.info(f"🚀 [流水线执行器] 启动流水线执行: {pipeline_id}")
            logger.info(f"   📝 名称: {pipeline.name}")
            logger.info(f"   👤 创建者: {pipeline.created_by or 'unknown'}")

            # 更新流水线状态为运行中，并设置开始时间
            started_at = datetime.now(timezone.utc).replace(tzinfo=None)
            success = await pipeline_operations.update_pipeline_status(
                pipeline_id,
                PipelineStatus.RUNNING,
                started_at=started_at
            )

            if not success:
                logger.error(f"   ❌ 更新流水线状态失败: {pipeline_id}")
                return

            # 创建监控任务
            monitor_task = asyncio.create_task(self.monitor.monitor_pipeline(pipeline_id))

            self.running_pipelines[pipeline_id] = monitor_task

            logger.info(f"   ✅ 流水线监控任务已启动: {pipeline_id}")

        except Exception as e:
            logger.error(f"💥 [流水线执行器] 启动流水线失败: {pipeline_id}, 错误: {e}")
            traceback.print_exc()

            # 标记流水线为失败
            try:
                await pipeline_operations.update_pipeline_status(
                    pipeline_id,
                    PipelineStatus.FAILED,
                    completed_at=datetime.now(timezone.utc).replace(tzinfo=None),
                    error_message=f"启动失败: {str(e)}"
                )
            except Exception as update_error:
                logger.error(f"更新流水线状态失败: {update_error}")

    async def _check_and_start_tasks(self):
        """检查并启动可执行的任务"""
        for pipeline_id in list(self.running_pipelines.keys()):
            try:
                await self._start_executable_tasks(pipeline_id)
            except Exception as e:
                logger.error(f"💥 [流水线执行器] 检查任务失败: {pipeline_id}, 错误: {e}")

    async def _start_executable_tasks(self, pipeline_id: str):
        """基于依赖关系执行流水线任务"""
        try:
            pipeline_tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)
            if not pipeline_tasks:
                return

            # 找到可以执行的任务（依赖已满足且状态为pending）
            executable_tasks = []
            all_tasks_map = {task.id: task for task in pipeline_tasks}

            for task in pipeline_tasks:
                if task.status == TaskStatus.PENDING and task.id not in self.executing_tasks:
                    if self._are_dependencies_satisfied(task, all_tasks_map):
                        executable_tasks.append(task)
                        logger.info(f"🔄 [任务调度] 任务可执行: {task.id} ({task.task_type})")

            # 串行启动所有可执行的任务
            if executable_tasks:
                logger.info(f"🚀 [流水线执行] 启动 {len(executable_tasks)} 个可执行任务")

                # 为每个可执行任务收集输入数据并执行
                for task in executable_tasks:
                    await self._execute_single_task(task)

        except Exception as e:
            logger.error(f"💥 [流水线执行] 流水线执行失败: {pipeline_id}, 错误: {e}")
            traceback.print_exc()
            await pipeline_operations.update_pipeline_status(pipeline_id, PipelineStatus.FAILED, error_message=str(e))

    def _are_dependencies_satisfied(self, task: PipelineTask, all_tasks_map: Dict[str, PipelineTask]) -> bool:
        # 检查所有依赖任务是否都已完成
        for dep_task_id in task.dependencies or []:
            dep_task = all_tasks_map.get(dep_task_id)
            if not dep_task:
                return False
            # 正确比较枚举对象
            if dep_task.status != TaskStatus.COMPLETED:
                return False
        return True

    async def _update_task_status_with_retry(self, task_id: str, status: TaskStatus, **kwargs) -> None:
        """带重试机制的任务状态更新"""
        await pipeline_operations.update_task_status(task_id, status, **kwargs)

    async def _execute_single_task(self, task: PipelineTask):
        """执行单个任务"""
        task_id = task.id
        task_type = task.task_type

        # 检查任务是否已经在执行中
        if task_id in self.executing_tasks:
            logger.warning(f"⚠️ [任务执行] 任务已在执行中，跳过: {task_id}")
            return

        # 标记任务为执行中
        self.executing_tasks.add(task_id)

        try:
            logger.info(f"🚀 [任务执行] 开始执行任务: {task_id} (类型: {task_type})")

            # 更新任务状态为运行中
            await self._update_task_status_with_retry(task_id, TaskStatus.RUNNING)

            try:
                # 收集依赖项的输出作为当前任务的输入
                previous_task_result = await self._collect_dependency_outputs(task)

                # 直接在异步环境中执行任务
                task_result = await self._run_task_async(task, previous_task_result)

                # 根据任务执行结果更新状态
                if task_result.success:
                    # 保存任务输出数据
                    if task_result.result_data:
                        await pipeline_operations.update_task_output_data(task_id, task_result.result_data)
                        logger.info(f"   💾 任务输出数据已保存: {task_id}, 数据键: {list(task_result.result_data.keys())}")

                    # 更新任务状态为完成
                    await self._update_task_status_with_retry(
                        task_id,
                        TaskStatus.COMPLETED,
                    )
                    logger.info(f"   ✅ 任务已完成: {task_id}")
                else:
                    # 任务执行失败
                    await self._update_task_status_with_retry(
                        task_id,
                        TaskStatus.FAILED,
                        error_message=task_result.error_message or "任务执行失败"
                    )
                    logger.error(f"   ❌ 任务执行失败: {task_id}, 错误: {task_result.error_message}")

            except Exception as task_error:
                logger.error(f"   ❌ 任务执行失败: {task_id}, 错误: {task_error}")
                traceback.print_exc()
                await self._update_task_status_with_retry(
                    task_id,
                    TaskStatus.FAILED,
                    error_message=str(task_error)
                )
        finally:
            # 无论成功还是失败，都要清理执行状态
            self.executing_tasks.discard(task_id)
            logger.debug(f"🧹 [任务执行] 清理任务执行状态: {task_id}")

    async def _collect_dependency_outputs(self, task: PipelineTask) -> TaskResult:
        """收集所有依赖任务的输出，并合并它们"""
        merged_output_data = {}

        # 继承流水线最开始的输入
        pipeline = await pipeline_operations.get_pipeline(task.pipeline_id)
        if pipeline and hasattr(pipeline, 'input_params') and pipeline.input_params:
            merged_output_data.update(pipeline.input_params)

        # 获取流水线中的所有任务，按step_order排序
        all_tasks = await pipeline_operations.get_pipeline_tasks(task.pipeline_id)

        # 找到当前任务的step_order
        current_step_order = task.step_order

        # 收集所有在当前任务之前完成的任务的输出数据
        for prev_task in all_tasks:
            if prev_task.step_order < current_step_order and prev_task.output_data:
                # 合并前置任务的输出数据
                merged_output_data.update(prev_task.output_data)
                logger.debug(f"  📥 收集前置任务输出: {prev_task.id} (step {prev_task.step_order}) -> {list(prev_task.output_data.keys())}")

        return TaskResult(task_id=task.id, success=True, result_data=merged_output_data)

    async def _run_task_async(self, task: PipelineTask, pre_task_result: TaskResult) -> TaskResult:
        """在异步环境中运行任务"""
        task_type = task.task_type
        logger.info(f"  [异步执行] 开始执行任务: {task.id} (类型: {task_type})")

        try:
            task_type_enum = TaskType(task_type)
            processor = self.processors.get(task_type_enum)
            if not processor:
                raise ValueError(f"未找到类型为 '{task_type}' 的处理器")

            # 传递合并后的输入，异步执行任务
            result = await processor.execute_task(task, pre_task_result)
            logger.info(f"  [异步执行] 执行完成: {task.id}")
            return result

        except Exception as e:
            logger.error(f"  [异步执行] 执行任务失败: {task.id}, 错误: {e}")
            traceback.print_exc()
            # 返回失败的TaskResult
            return TaskResult(task_id=task.id, success=False, error_message=str(e))

    async def _cleanup_completed_pipeline_tasks(self):
        """清理已完成的流水线监控任务"""
        completed_pipeline_ids = []

        for pipeline_id, task in self.running_pipelines.items():
            if task.done():
                completed_pipeline_ids.append(pipeline_id)

        for pipeline_id in completed_pipeline_ids:
            task = self.running_pipelines.pop(pipeline_id)
            try:
                # 获取任务结果（如果有异常会抛出）
                await task
            except Exception as e:
                logger.error(f"💥 [清理任务] 流水线监控任务异常: {pipeline_id}, 错误: {e}")

            logger.debug(f"🧹 [清理任务] 已清理流水线监控任务: {pipeline_id}")

    def _update_stats(self):
        """更新统计信息"""
        self.stats.running_pipelines = len(self.running_pipelines)
        self.stats.last_check_time = datetime.utcnow()
        if self.start_time:
            self.stats.uptime_seconds = (datetime.utcnow() - self.start_time).total_seconds()

    async def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return {
            "is_running": self.is_running,
            "running_pipelines": self.stats.running_pipelines,
            "completed_pipelines": self.stats.completed_pipelines,
            "failed_pipelines": self.stats.failed_pipelines,
            "total_tasks_processed": self.stats.total_tasks_processed,
            "uptime_seconds": self.stats.uptime_seconds,
            "last_check_time": self.stats.last_check_time.isoformat() if self.stats.last_check_time else None,
            "max_concurrent_pipelines": self.max_concurrent_pipelines,
            "check_interval": self.check_interval,
        }

    async def _update_document_status_on_completion(self, pipeline_id: str, status: str, error_message: Optional[str] = None) -> None:
        """流水线完成时更新关联文档的状态"""
        try:
            # 获取流水线信息
            pipeline = await pipeline_operations.get_pipeline(pipeline_id)
            if not pipeline or not pipeline.doc_id:
                logger.debug(f"流水线 {pipeline_id} 没有关联文档，跳过文档状态更新")
                return

            doc_id = pipeline.doc_id
            logger.info(f"🔄 更新文档状态: {doc_id} -> {status}")

            # 更新文档状态
            success = await postgre_sql_client.update_document_status(
                doc_id=doc_id, process_status=status, error_message=error_message)

            if success:
                logger.info(f"✅ 文档状态更新成功: {doc_id} -> {status}")
            else:
                logger.error(f"❌ 文档状态更新失败: {doc_id}")

        except Exception as e:
            logger.error(f"更新文档状态失败: {e}")
            traceback.print_exc()


# 全局执行器实例
pipeline_executor = PipelineExecutor()
