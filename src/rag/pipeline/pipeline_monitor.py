"""
流水线监控器

负责监控流水线的执行情况和任务状态
并更新数据库中的相关记录

"""
import asyncio
import traceback
from datetime import datetime, timezone
from typing import Optional

from src.rag.infrastructure.db import document_operations, pipeline_operations
from src.rag.pipeline.models import PipelineStatus, TaskStatus
from src.rag.tools import logger


class PipelineMonitor:
    """流水线监控器"""

    def __init__(self, executor):
        self.executor = executor
        self.stats = self.executor.stats

    async def monitor_pipeline(self, pipeline_id: str):
        """监控单个流水线的执行"""
        logger.info(f"👁️ [流水线监控] 开始监控流水线: {pipeline_id}")

        last_status_report = None
        check_count = 0

        try:
            while True:
                check_count += 1

                pipeline = await pipeline_operations.get_pipeline(pipeline_id)
                if not pipeline:
                    logger.error(f"   ❌ 流水线不存在: {pipeline_id}")
                    break

                if pipeline.status == PipelineStatus.CANCELLED:
                    logger.info(f"   ⏹️ 流水线已被取消: {pipeline_id}")
                    await self._cancel_pipeline_tasks(pipeline_id)
                    break

                pipeline_tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)
                if not pipeline_tasks:
                    logger.warning(f"   ⚠️ 流水线中没有任务: {pipeline_id}")
                    await pipeline_operations.update_pipeline_status(
                        pipeline_id,
                        PipelineStatus.FAILED,
                        error_message="流水线中没有任务"
                    )
                    break

                # 使用TaskStatus枚举的所有值初始化计数器
                task_status_count = {status.value: 0 for status in TaskStatus}

                for task in pipeline_tasks:
                    status = task.status
                    # 将枚举对象转换为字符串值
                    status_value = status.value if hasattr(status, 'value') else str(status)
                    if status_value in task_status_count:
                        task_status_count[status_value] += 1
                    else:
                        logger.warning(f"⚠️ [流水线监控] 发现未知任务状态: {status}, 任务ID: {task.id}")
                        task_status_count[status_value] = task_status_count.get(status_value, 0) + 1
                
                await self.executor._start_executable_tasks(pipeline_id)

                if task_status_count != last_status_report or check_count % 12 == 1:
                    last_status_report = task_status_count.copy()

                total_tasks = len(pipeline_tasks)
                completed_tasks = task_status_count['completed']
                failed_tasks = task_status_count['failed'] + task_status_count['dependency_failed']
                cancelled_tasks = task_status_count['cancelled']
                processing_tasks = task_status_count['running'] + task_status_count['pending'] + task_status_count['retry']

                if failed_tasks > 0:
                    logger.error(f"💥 [流水线监控] 流水线执行失败: {pipeline_id}")
                    failed_task_messages = []
                    for task in pipeline_tasks:
                        task_status = task.status
                        task_error = task.error_message
                        task_id = task.id
                        # 将枚举对象转换为字符串值进行比较
                        status_value = task_status.value if hasattr(task_status, 'value') else str(task_status)
                        if status_value in ["failed", "dependency_failed"] and task_error:
                            failed_task_messages.append(f"{task_id}: {task_error}")
                    
                    error_message = "; ".join(failed_task_messages[:5])
                    if len(failed_task_messages) > 5:
                        error_message += f"; ... 还有 {len(failed_task_messages) - 5} 个错误"

                    await pipeline_operations.update_pipeline_status(
                        pipeline_id, PipelineStatus.FAILED,
                        completed_at=datetime.now(timezone.utc).replace(tzinfo=None),
                        error_message=error_message
                    )
                    await self._update_document_status_on_completion(pipeline_id, "failed", error_message)
                    self.stats.failed_pipelines += 1
                    break

                elif completed_tasks == total_tasks and processing_tasks == 0:
                    logger.info(f"🎉 [流水线监控] 流水线执行完成: {pipeline_id}")
                    completed_at = datetime.now(timezone.utc).replace(tzinfo=None)
                    duration = None
                    if pipeline.started_at:
                        start_time = pipeline.started_at
                        duration = (completed_at - start_time).total_seconds()
                        logger.info(f"   ⏱️ 执行时间: {duration:.1f} 秒")

                    await pipeline_operations.update_pipeline_status(
                        pipeline_id, PipelineStatus.COMPLETED,
                        completed_at=completed_at,
                    )
                    await self._update_document_status_on_completion(pipeline_id, "completed")
                    self.stats.completed_pipelines += 1
                    self.stats.total_tasks_processed += completed_tasks
                    break

                elif cancelled_tasks == total_tasks:
                    logger.info(f"⏹️ [流水线监控] 流水线已取消: {pipeline_id}")
                    break

                await asyncio.sleep(5)

        except asyncio.CancelledError:
            logger.info(f"⏹️ [流水线监控] 监控任务被取消: {pipeline_id}")
        except Exception as e:
            logger.error(f"💥 [流水线监控] 监控异常: {pipeline_id}, 错误: {e}")
            traceback.print_exc()
            try:
                await pipeline_operations.update_pipeline_status(
                    pipeline_id, PipelineStatus.FAILED,
                    completed_at=datetime.now(timezone.utc).replace(tzinfo=None),
                    error_message=f"监控异常: {str(e)}"
                )
                self.stats.failed_pipelines += 1
            except Exception as update_error:
                logger.error(f"更新流水线状态失败: {update_error}")

        logger.info(f"🏁 [流水线监控] 监控结束: {pipeline_id}")

    async def _update_task_status_with_retry(self, task_id: str, status: TaskStatus, **kwargs) -> None:
        """带重试机制的任务状态更新"""
        await pipeline_operations.update_task_status(task_id, status, **kwargs)

    async def _cancel_pipeline_tasks(self, pipeline_id: str):
        """取消流水线中的所有任务"""
        try:
            pipeline_tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)
            cancelled_count = 0
            for task in pipeline_tasks:
                task_status = task.status
                # 将枚举对象转换为字符串值进行比较
                status_value = task_status.value if hasattr(task_status, 'value') else str(task_status)
                if status_value in ["pending", "running"]:
                    success = await self._update_task_status_with_retry(
                        task.id,
                        TaskStatus.CANCELLED,
                        error_message="流水线已取消"
                    )
                    if success:
                        cancelled_count += 1
            logger.info(f"⏹️ [流水线取消] 流水线 {pipeline_id} 已取消 {cancelled_count} 个任务")
        except Exception as e:
            logger.error(f"💥 [流水线取消] 取消任务失败: {pipeline_id}, 错误: {e}")

    async def _update_document_status_on_completion(self, pipeline_id: str, status: str, error_message: Optional[str] = None) -> None:
        """流水线完成时更新关联文档的状态"""
        try:
            pipeline = await pipeline_operations.get_pipeline(pipeline_id)
            if not pipeline or not pipeline.doc_id:
                logger.debug(f"流水线 {pipeline_id} 没有关联文档，跳过文档状态更新")
                return

            doc_id = pipeline.doc_id
            logger.info(f"🔄 更新文档状态: {doc_id} -> {status}")
            success = await document_operations.update_document_status(
                doc_id=doc_id, process_status=status, error_message=error_message)
            if success:
                logger.info(f"✅ 文档状态更新成功: {doc_id} -> {status}")
            else:
                logger.error(f"❌ 文档状态更新失败: {doc_id}")
        except Exception as e:
            logger.error(f"更新文档状态失败: {e}")
            traceback.print_exc()