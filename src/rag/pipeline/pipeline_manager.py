"""
流水线管理器

负责创建、管理 取消 重试 等操作
"""

import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from src.rag.infrastructure.db.pipeline_operations import pipeline_operations
from src.rag.tools import logger
from .models import Pipeline, PipelineTask, PipelineStatus, TaskStatus, TaskType


class PipelineManager:
    """流水线管理器"""

    async def submit_document_processing_chain(
        self,
        doc_id: str,
        created_by: str = "system"
    ) -> str:
        """
        提交文档处理链

        Args:
            doc_id: 文档ID
            created_by: 创建者

        Returns:
            str: 流水线ID
        """

        # 生成流水线ID
        pipeline_id = f"pipeline_{doc_id}_{uuid.uuid4().hex[:8]}"

        # 创建流水线
        pipeline = Pipeline(
            id=pipeline_id,
            name=f"文档处理流水线 - {doc_id}",
            status=PipelineStatus.PENDING,
            description=f"处理文档 {doc_id} 的完整流水线",
            created_at=datetime.now(timezone.utc).replace(tzinfo=None),
            created_by=created_by,
            doc_id=doc_id
        )

        # 保存流水线
        success = await pipeline_operations.create_pipeline(pipeline)
        if not success:
            raise RuntimeError(f"创建流水线失败: {pipeline_id}")

        # 创建任务链
        await self._create_document_processing_tasks(pipeline_id, doc_id)

        logger.info(f"文档处理流水线创建成功: {pipeline_id} (doc_id: {doc_id})")
        return pipeline_id

    async def _create_document_processing_tasks(
        self,
        pipeline_id: str,
        doc_id: str,
    ):
        """
        创建文档处理任务链

        Args:
            pipeline_id: 流水线ID
            doc_id: 文档ID
        """
        # 定义任务链（串行执行设计）
        task_chain = [
            {
                "task_type": TaskType.DOCUMENT_CHUNKING,
                "name": "文档分块",
                "parameters": {
                    "doc_id": doc_id,
                    "chunk_size": 512,
                    "overlap_size": 50
                },
                "dependencies": []  # 无依赖
            },
            {
                "task_type": TaskType.VECTOR_EMBEDDING,
                "name": "向量嵌入",
                "parameters": {
                    "doc_id": doc_id
                },
                "dependencies": [0]  # 依赖任务0（文档分块）
            },
            {
                "task_type": TaskType.VECTOR_STORAGE,
                "name": "向量存储",
                "parameters": {
                    "doc_id": doc_id
                },
                "dependencies": [1]  # 依赖任务1（向量嵌入）
            },
            {
                "task_type": TaskType.ENTITY_EXTRACTION,
                "name": "实体抽取",
                "parameters": {
                    "doc_id": doc_id
                },
                "dependencies": [2]  # 依赖任务2（向量存储）
            },
            {
                "task_type": TaskType.GRAPH_BUILDING,
                "name": "图构建",
                "parameters": {
                    "doc_id": doc_id
                },
                "dependencies": [3]  # 依赖任务3（实体抽取）
            },
            {
                "task_type": TaskType.GRAPH_STORAGE,
                "name": "图谱存储",
                "parameters": {
                    "doc_id": doc_id
                },
                "dependencies": [4]  # 依赖任务4（图构建）
            }
        ]

        # 创建任务
        task_ids = []
        for i, task_config in enumerate(task_chain):
            # 生成较短的任务ID
            task_id = f"task_{uuid.uuid4().hex[:8]}_{i+1}"
            task_ids.append(task_id)

            task = PipelineTask(
                id=task_id,
                pipeline_id=pipeline_id,
                task_type=task_config["task_type"],
                status=TaskStatus.PENDING,
                parameters=task_config.get("parameters", {}),
                created_at=datetime.now(timezone.utc).replace(tzinfo=None)
            )

            # 设置依赖关系
            dependencies = task_config.get("dependencies", [])
            if dependencies:
                task.dependencies = [task_ids[dep_index] for dep_index in dependencies]
            else:
                task.dependencies = []

            # 添加任务到流水线
            success = await pipeline_operations.add_task_to_pipeline(pipeline_id, task)
            if not success:
                raise RuntimeError(f"添加任务到流水线失败: {task_id}")

        logger.info(f"为流水线 {pipeline_id} 创建了 {len(task_chain)} 个任务")

    async def get_pipeline_status(self, pipeline_id: str) -> Optional[Dict[str, Any]]:
        """
        获取流水线状态

        Args:
            pipeline_id: 流水线ID

        Returns:
            Dict[str, Any]: 流水线状态信息
        """

        # 获取流水线信息
        pipeline = await pipeline_operations.get_pipeline(pipeline_id)
        if not pipeline:
            return None

        # 获取流水线中的任务统计
        tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)

        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t.status.value == TaskStatus.COMPLETED.value])
        failed_tasks = len([t for t in tasks if t.status.value == TaskStatus.FAILED.value])
        running_tasks = len([t for t in tasks if t.status.value == TaskStatus.RUNNING.value])
        pending_tasks = len([t for t in tasks if t.status.value == TaskStatus.PENDING.value])

        return {
            "pipeline_id": pipeline_id,
            "status": pipeline.status.value,
            "name": pipeline.name,
            "description": pipeline.description,
            "doc_id": pipeline.doc_id,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "running_tasks": running_tasks,
            "pending_tasks": pending_tasks,
            "created_at": pipeline.created_at.isoformat() if pipeline.created_at else None,
            "started_at": pipeline.started_at.isoformat() if pipeline.started_at else None,
            "completed_at": pipeline.completed_at.isoformat() if pipeline.completed_at else None,
            "created_by": pipeline.created_by,
            "progress": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        }

    async def cancel_pipeline(self, pipeline_id: str) -> bool:
        """
        取消流水线

        Args:
            pipeline_id: 流水线ID

        Returns:
            bool: 是否成功取消
        """

        # 更新流水线状态
        success = await pipeline_operations.update_pipeline_status(
            pipeline_id,
            PipelineStatus.CANCELLED,
            completed_at=datetime.now(timezone.utc).replace(tzinfo=None)
        )

        if success:
            # 取消所有待执行的任务
            tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)
            for task in tasks:
                if task.status.value in [TaskStatus.PENDING.value, TaskStatus.RUNNING.value]:
                    await pipeline_operations.update_task_status(
                        task.id,
                        TaskStatus.CANCELLED,
                        error_message="流水线已取消"
                    )

            logger.info(f"流水线已取消: {pipeline_id}")

        return success

    async def retry_failed_pipeline(self, pipeline_id: str) -> bool:
        """
        重试失败的流水线

        Args:
            pipeline_id: 流水线ID

        Returns:
            bool: 是否成功重试
        """

        # 获取流水线信息
        pipeline = await pipeline_operations.get_pipeline(pipeline_id)
        if not pipeline or pipeline.status != PipelineStatus.FAILED:
            return False

        # 重置流水线状态
        success = await pipeline_operations.update_pipeline_status(
            pipeline_id,
            PipelineStatus.PENDING,
            started_at=None,
            completed_at=None,
            error_message=None
        )

        if success:
            # 重置失败的任务
            tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)
            for task in tasks:
                if task.status.value == TaskStatus.FAILED.value:
                    await pipeline_operations.update_task_status(
                        task.id,
                        TaskStatus.PENDING,
                        error_message=None,
                        started_at=None,
                        completed_at=None
                    )

            logger.info(f"流水线重试已启动: {pipeline_id}")

        return success


pipeline_manager = PipelineManager()