"""
查询处理器

处理查询任务
"""

from typing import List

from src.rag.tools import logger
from .base import TaskProcessor, register_processor
from ..models import PipelineTask, TaskResult, TaskType
from ...service.retrieval import knowledge_graph_retriever


@register_processor(TaskType.QUERY_PROCESSING)
class QueryProcessor(TaskProcessor):
    """查询处理器"""

    def get_supported_task_types(self) -> List[TaskType]:
        """获取支持的任务类型"""
        return [TaskType.QUERY_PROCESSING]

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理查询任务"""
        query = task.parameters["query"]
        mode = task.parameters.get("mode", "mix")

        logger.info(f"开始处理查询: {query[:100]}... (模式: {mode})")

        # 这里应该调用实际的检索服务
        result = await knowledge_graph_retriever.query(query, mode, task.parameters)
        logger.info(f"查询处理完成，置信度: {result.get('confidence', 0)}")

        return TaskResult(
            task_id=task.id,
            success=True,
            result_data={
                "query": query,
                "mode": mode,
                "result": result,
                "status": "completed"
            }
        )