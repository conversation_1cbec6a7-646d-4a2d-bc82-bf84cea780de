"""
清理处理器

负责清理流水线执行过程中产生的临时数据
"""

from src.rag.infrastructure.redis.client import redis_client
from src.rag.tools import logger
from .base import TaskProcessor, register_processor, PipelineTask, TaskResult


@register_processor("cleanup")
class CleanupProcessor(TaskProcessor):
    """清理处理器"""

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理清理任务"""
        pipeline_id = task.pipeline_id
        logger.info(f"🧹 [清理] 开始清理临时数据: task.pipeline_id={pipeline_id}")
        # 从前一个任务结果中获取Redis key
        chunks_redis_key = pre_task_result.result_data.get("chunks_redis_key")
        # 清空 redis_chunk_key 的 set 集合
        await redis_client.redis.delete(chunks_redis_key)
        return TaskResult(
            task_id=task.id,
            success=True,
            # TODO 记录任务执行的耗时
            # execution_time =
        )
