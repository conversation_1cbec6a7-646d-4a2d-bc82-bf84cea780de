"""
实体处理器

处理实体抽取相关的任务
"""

import logging
from typing import List, Dict, Any

from src.rag.llm import call_llm
from src.rag.service import EntityExtractor
from src.rag.tools import logger
from .base import TaskProcessor, register_processor
from ..models import <PERSON>pelineTask, TaskResult, TaskType


@register_processor(TaskType.ENTITY_EXTRACTION)
class EntityExtractionProcessor(TaskProcessor):
    """实体抽取处理器"""

    def __init__(self):
        # 使用配置文件中的实体类型，而不是默认的PROMPTS配置
        from src.config import config
        processing_config = config.processing
        self.extractor = EntityExtractor(
            llm_model_func=call_llm,
            entity_types=processing_config.entity_types,
            max_gleaning_rounds=processing_config.max_gleaning_rounds,
            extract_max_retry=processing_config.extract_max_retry
        )


    def get_supported_task_types(self) -> List[TaskType]:
        """获取支持的任务类型"""
        return [TaskType.ENTITY_EXTRACTION]

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理实体抽取任务"""

        # 获取参数
        doc_id = task.parameters.get("doc_id", "unknown")

        # 从前一个任务获取chunks数据
        chunks = []
        if pre_task_result and pre_task_result.result_data:
            # 优先从前一个任务的result_data中获取chunk_list
            chunks = pre_task_result.result_data.get("chunk_list", [])
            logger.info(f"🔍 [实体抽取] 从前一个任务接收到 {len(chunks)} 个分块")

        # 如果前一个任务没有chunks，尝试从Redis获取
        if not chunks:
            chunks_redis_key = None
            if pre_task_result and pre_task_result.result_data:
                chunks_redis_key = pre_task_result.result_data.get("chunks_redis_key")

            if chunks_redis_key:
                chunks = await self._get_chunks_from_redis(chunks_redis_key)
                logger.info(f"🔍 [实体抽取] 从Redis获取到 {len(chunks)} 个分块")
            else:
                # 最后尝试直接从文档ID构建Redis key
                redis_key = f"chunks:{doc_id}"
                chunks = await self._get_chunks_from_redis(redis_key)
                logger.info(f"🔍 [实体抽取] 从文档Redis key获取到 {len(chunks)} 个分块")

        if not chunks:
            logger.error(f"任务 {task.id} 无法获取分块数据")
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message=f"无法获取分块数据"
            )

        logger.info(f"开始抽取实体: doc_id={doc_id}, 分块数量={len(chunks)}")

        # 执行实体抽取
        entities = []
        relationships = []
        entity_name_to_id = {}  # 实体名称到ID的映射

        for chunk_data in chunks:
            chunk_content = chunk_data.get("content", "")
            chunk_id = chunk_data.get("chunk_id", "")

            if not chunk_content:
                continue

            logger.debug(f"处理分块: {chunk_id}")

            # 抽取实体和关系
            chunk_entities, chunk_relationships = await self.extractor.extract_from_chunk(
                chunk_content=chunk_content,
                chunk_id=chunk_id
            )

            # 转换为字典格式并建立映射
            for entity in chunk_entities:
                entity_id = f"{chunk_id}_{entity.entity_name}"
                entity_data = {
                    "entity_id": entity_id,
                    "entity_name": entity.entity_name,
                    "entity_type": entity.entity_type,
                    "description": entity.description,
                    "source_chunk": chunk_id,
                    "confidence": 1.0  # 默认置信度
                }
                entities.append(entity_data)
                # 建立实体名称到ID的映射
                entity_name_to_id[entity.entity_name.lower()] = entity_id

            # 先收集所有关系，稍后统一处理ID映射
            for relationship in chunk_relationships:
                relationship_data = {
                    "relationship_id": f"{chunk_id}_{relationship.src_id}_{relationship.tgt_id}",
                    "src_entity_name": relationship.src_id,  # 原始实体名称
                    "tgt_entity_name": relationship.tgt_id,  # 原始实体名称
                    "description": relationship.description,
                    "keywords": relationship.keywords,
                    "weight": relationship.weight,
                    "source_chunk": chunk_id
                }
                relationships.append(relationship_data)

        # 处理关系的实体ID映射
        processed_relationships = []
        for relationship in relationships:
            src_name = relationship["src_entity_name"].lower()
            tgt_name = relationship["tgt_entity_name"].lower()

            # 查找对应的实体ID
            src_entity_id = entity_name_to_id.get(src_name)
            tgt_entity_id = entity_name_to_id.get(tgt_name)

            if src_entity_id and tgt_entity_id:
                processed_relationship = {
                    "relationship_id": relationship["relationship_id"],
                    "src_entity": src_entity_id,
                    "tgt_entity": tgt_entity_id,
                    "description": relationship["description"],
                    "keywords": relationship["keywords"],
                    "weight": relationship["weight"],
                    "source_chunk": relationship["source_chunk"]
                }
                processed_relationships.append(processed_relationship)
                logger.debug(f"✅ 关系映射成功: '{src_name}' -> '{tgt_name}' 映射为 '{src_entity_id}' -> '{tgt_entity_id}'")
            else:
                logger.warning(f"❌ 关系中的实体未找到对应ID: '{src_name}' -> '{tgt_name}'")
                logger.debug(f"   源实体 '{src_name}' 映射结果: {src_entity_id}")
                logger.debug(f"   目标实体 '{tgt_name}' 映射结果: {tgt_entity_id}")
                logger.debug(f"   可用的实体名称: {list(entity_name_to_id.keys())}")

        # 调试日志（仅在需要时启用）
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("=" * 60)
            logger.debug("🔍 [调试] 抽取的实体列表:")
            for i, entity in enumerate(entities):
                logger.debug(f"  实体 {i+1}: {entity['entity_name']} (类型: {entity['entity_type']}, ID: {entity['entity_id']})")

            logger.debug("🔍 [调试] 实体名称到ID映射:")
            for name, entity_id in entity_name_to_id.items():
                logger.debug(f"  '{name}' -> '{entity_id}'")

            logger.debug("🔍 [调试] 原始关系三元组:")
            for i, rel in enumerate(relationships):
                logger.debug(f"  关系 {i+1}: {rel['src_entity_name']} -> {rel['tgt_entity_name']} ({rel['description']})")

            logger.debug("🔍 [调试] 处理后的关系三元组:")
            for i, rel in enumerate(processed_relationships):
                logger.debug(f"  关系 {i+1}: {rel['src_entity']} -> {rel['tgt_entity']} ({rel['description']})")
            logger.debug("=" * 60)

        # 去重实体和关系
        unique_entities = self._deduplicate_entities(entities)
        unique_relationships = self._deduplicate_relationships(processed_relationships)

        logger.info(f"实体抽取完成: 实体={len(unique_entities)}, 关系={len(unique_relationships)}")

        # 进行图构建
        graph_data = await self._build_graph(unique_entities, unique_relationships, doc_id)

        logger.info(f"🔨 [图构建] 图构建完成: 节点={len(graph_data['nodes'])}, 边={len(graph_data['edges'])}")

        return TaskResult(
            task_id=task.id,
            success=True,
            result_data={
                "doc_id": doc_id,
                "entities": unique_entities,
                "relationships": unique_relationships,
                "graph_data": graph_data,
                "total_entities": len(unique_entities),
                "total_relationships": len(unique_relationships),
                "processed_chunks": len(chunks)
            }
        )

    def _deduplicate_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重实体"""
        seen = set()
        unique_entities = []
        
        for entity in entities:
            # 使用实体名称和类型作为去重键
            key = (entity["entity_name"].lower(), entity["entity_type"])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)
        
        return unique_entities

    def _deduplicate_relationships(self, relationships: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重关系"""
        seen = set()
        unique_relationships = []
        
        for rel in relationships:
            # 使用源实体、目标实体作为去重键
            src = rel.get("src_entity") or rel.get("source_entity", "")
            tgt = rel.get("tgt_entity") or rel.get("target_entity", "")
            
            if src and tgt:
                key = (src.lower(), tgt.lower())
                if key not in seen:
                    seen.add(key)
                    unique_relationships.append(rel)
        
        return unique_relationships

    async def _get_chunks_from_redis(self, redis_key: str) -> list:
        """从Redis set中获取分块数据"""
        try:
            from src.rag.infrastructure import redis_client
            import json

            # 从Redis set中获取所有chunk数据
            # redis_key格式：chunks:doc_id
            redis_client = redis_client.redis
            chunk_set_data = await redis_client.smembers(redis_key)

            if not chunk_set_data:
                logger.warning(f"🔍 [实体抽取] Redis set中未找到分块数据: {redis_key}")
                return []

            # 解析JSON数据并转换为chunk列表
            chunks = []
            for chunk_json in chunk_set_data:
                try:
                    chunk_data = json.loads(chunk_json)
                    chunks.append(chunk_data)
                except json.JSONDecodeError as e:
                    logger.error(f"🔍 [实体抽取] 解析chunk JSON数据失败: {e}")
                    continue

            # 按order_index排序
            chunks.sort(key=lambda x: x.get("order_index", 0))

            logger.info(f"🔍 [实体抽取] 从Redis set获取到 {len(chunks)} 个分块: {redis_key}")
            return chunks

        except Exception as e:
            logger.error(f"🔍 [实体抽取] 从Redis set获取分块数据失败: {e}")
            return []

    async def _build_graph(self, entities: list, relationships: list, doc_id: str) -> dict:
        """构建图数据结构"""
        try:
            # 构建图数据结构
            graph_data = {
                "nodes": [],
                "edges": [],
            }

            # 处理实体节点
            for entity in entities:
                node = {
                    "id": entity.get("entity_id") or entity.get("entity_name"),
                    "label": entity.get("entity_name"),
                    "type": entity.get("entity_type"),
                    "description": entity.get("description", ""),
                    "source_chunk": entity.get("source_chunk", ""),
                    "confidence": entity.get("confidence", 1.0)
                }
                graph_data["nodes"].append(node)

            # 处理关系边
            for relationship in relationships:
                edge = {
                    "id": relationship.get("relationship_id"),
                    "source": relationship.get("src_entity"),
                    "target": relationship.get("tgt_entity"),
                    "label": relationship.get("description", ""),
                    "type": "relationship",
                    "weight": relationship.get("weight", 1.0),
                    "keywords": relationship.get("keywords", []),
                    "source_chunk": relationship.get("source_chunk", "")
                }
                graph_data["edges"].append(edge)

            logger.info(f"🔨 [图构建] 图数据构建完成: 节点={len(graph_data['nodes'])}, 边={len(graph_data['edges'])}")
            return graph_data

        except Exception as e:
            logger.error(f"🔨 [图构建] 图构建失败: {e}")
            return {
                "nodes": [],
                "edges": [],
            }