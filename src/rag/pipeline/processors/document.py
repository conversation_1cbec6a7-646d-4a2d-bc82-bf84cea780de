"""
文档处理器

处理文档分块相关的任务
"""
from datetime import datetime

# 导入文档服务
from src.rag.service import DocumentChunker
from src.rag.tools import logger, compute_mdhash_id
from src.config import config
from .base import TaskProcessor, register_processor
from ..models import TaskResult, TaskType, PipelineTask


@register_processor(TaskType.DOCUMENT_CHUNKING)
class DocumentChunkingProcessor(TaskProcessor):
    """文档分块处理器"""

    def __init__(self):
        # 使用配置文件中的分块参数创建DocumentChunker
        processing_config = config.processing
        self.chunker = DocumentChunker(
            chunk_token_size=processing_config.chunk_size,
            chunk_overlap_token_size=processing_config.chunk_overlap
        )

    async def _get_document_content(self, doc_id: str) -> str:
        """根据文档ID获取文档内容"""
        from src.rag.infrastructure import document_operations, object_operations

        # 获取文档元数据
        doc_metadata = await document_operations.get_document_metadata(doc_id)
        if not doc_metadata:
            logger.error(f"文档不存在: {doc_id}")
            return ""

        # 获取MinIO对象键
        minio_object_key = doc_metadata.get("minio_object_key")
        if not minio_object_key:
            logger.error(f"文档MinIO对象键为空: {doc_id}")
            return ""

        # 从MinIO获取文件内容
        # 下载文件字节数据并转换为文本
        file_bytes = await object_operations.download_bytes(minio_object_key)
        content = file_bytes.decode('utf-8')
        if isinstance(content, bytes):
            content = content.decode('utf-8')

        logger.info(f"成功获取文档内容: doc_id={doc_id}, 长度={len(content)}")
        return content

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理文档分块任务"""
        # 验证输入参数
        if not self.validate_task_input(task):
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message="缺少必需的输入参数"
            )

        # 获取参数
        doc_id = task.parameters.get("doc_id")

        # 从配置文件获取分块参数
        processing_config = config.processing
        chunk_size = processing_config.chunk_size
        overlap_size = processing_config.chunk_overlap

        # 根据doc_id从 MinIO 存储中获取内容
        content = await self._get_document_content(doc_id)

        if not content:
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message="无法获取文档内容：缺少content参数或doc_id无效"
            )

        if not doc_id:
            doc_id = f"doc_{compute_mdhash_id(content)}"

        logger.info(f"开始分块文档: doc_id={doc_id}, 内容长度={len(content)}")
        logger.info(f"分块参数: chunk_size={chunk_size}, overlap_size={overlap_size}")

        # 执行分块
        chunks = await self.chunker.chunk_document(
            content=content,
            doc_id=doc_id
        )

        # 转换为字典格式
        chunk_data = []
        for chunk in chunks:
            chunk_info = {
                "chunk_id": chunk.chunk_id,
                "doc_id": chunk.full_doc_id,
                "content": chunk.content,
                "token_count": chunk.tokens,
                "order_index": chunk.chunk_order_index,
                "start_char": 0,  # DocumentChunk 没有这个字段
                "end_char": len(chunk.content)
            }
            chunk_data.append(chunk_info)

        logger.info(f"文档分块完成: 生成 {len(chunk_data)} 个分块")

        # 将分块数据存储到Redis中
        redis_key = await self._store_chunks_to_redis(doc_id, chunk_data, task.id)

        return TaskResult(
            task_id=task.id,
            success=True,
            result_data={
                "doc_id": doc_id,
                "total_chunks": len(chunk_data),
                "total_tokens": sum(chunk["token_count"] for chunk in chunk_data),
                "chunk_size": chunk_size,
                "overlap_size": overlap_size,
                "chunks_redis_key": redis_key  # 将Redis key放在result_data中
            }
        )

    async def _store_chunks_to_redis(self, doc_id: str, chunk_data: list, task_id: str) -> str:
        """将分块数据存储到Redis set中"""
        from src.rag.infrastructure import redis_client
        import json

        # 使用文档ID作为Redis set的键
        redis_key = f"chunks:{doc_id}"

        # 准备要存储到set中的chunk数据
        chunk_set_data = []

        for chunk in chunk_data:
            # 将chunk数据序列化为JSON字符串存储到set中
            chunk_json = json.dumps({
                "doc_id": chunk["doc_id"],
                "content": chunk["content"],
                "order_index": chunk["order_index"],
                "token_count": chunk["token_count"],
                "chunk_id": chunk["chunk_id"],
                "start_char": chunk.get("start_char", 0),
                "end_char": chunk.get("end_char", len(chunk["content"])),
                "task_id": task_id,
                "created_at": str(datetime.now())
            }, ensure_ascii=False)

            chunk_set_data.append(chunk_json)

        # 使用Redis set存储所有chunks
        # 先清空现有的set（如果存在）
        redis_client = redis_client.redis

        # 检查Redis key是否已存在
        existing_count = await redis_client.scard(redis_key)
        if existing_count > 0:
            logger.warning(f"⚠️ Redis key已存在，包含 {existing_count} 个元素，将清空: {redis_key}")

        await redis_client.delete(redis_key)

        # 将所有chunks添加到set中
        if chunk_set_data:
            # 使用Redis的SADD命令添加多个元素到set
            await redis_client.sadd(redis_key, *chunk_set_data)

            # 验证存储结果
            stored_count = await redis_client.scard(redis_key)
            logger.info(f"分块数据已存储到Redis set: key={redis_key}, 预期数量={len(chunk_data)}, 实际数量={stored_count}")

            if stored_count != len(chunk_data):
                logger.error(f"❌ Redis存储数量不匹配！预期: {len(chunk_data)}, 实际: {stored_count}")
        else:
            logger.info(f"没有分块数据需要存储到Redis: {redis_key}")

        return redis_key
