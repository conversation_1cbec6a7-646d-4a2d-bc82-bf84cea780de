"""
任务处理器基类

定义任务处理器的基础接口和通用功能
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import List

from src.rag.pipeline import TaskType, PipelineTask, TaskResult
from src.rag.tools import logger


class TaskProcessor(ABC):
    """任务处理器基类"""

    @abstractmethod
    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理任务的抽象方法"""
        pass

    def validate_task_input(self, task: PipelineTask) -> bool:
        """验证任务输入参数，子类可以重写此方法"""
        return True

    async def execute_task(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """执行任务的通用方法"""
        start_time = time.time()

        logger.info(f"开始处理任务   📋 任务信息: ID={task.id}, 类型={task.task_type.value}")

        # 调用具体的处理方法
        result = await self.process(task, pre_task_result)

        execution_time = time.time() - start_time
        logger.info(f"任务处理成功   📋 任务信息: ID={task.id}, 类型={task.task_type.value} ⏱️ 执行时间: {execution_time:.2f} 秒")

        return result

    async def handle_task_timeout(self, task: PipelineTask, timeout_seconds: int = 300):
        """处理任务超时"""
        result = await asyncio.wait_for(
            self.process(task),
            timeout=timeout_seconds
        )
        return result


class ProcessorFactory:
    """处理器工厂类"""
    
    _processors = {}
    
    @classmethod
    def register_processor(cls, task_type, processor_class):
        """注册处理器"""
        cls._processors[task_type] = processor_class
        # 处理字符串和枚举类型
        if hasattr(task_type, 'value'):
            task_type_str = task_type.value
        else:
            task_type_str = str(task_type)
        logger.info(f"注册处理器: {task_type_str} -> {processor_class.__name__}")
    
    @classmethod
    def create_processor(cls, task_type: TaskType, **kwargs) -> TaskProcessor:
        """创建处理器实例"""
        if task_type not in cls._processors:
            raise ValueError(f"不支持的任务类型: {task_type.value}")

        processor_class = cls._processors[task_type]

        try:
            # 尝试传递所有参数
            return processor_class(**kwargs)
        except TypeError as e:
            logger.debug(f"处理器 {processor_class.__name__} 不接受额外参数: {e}")
            return processor_class()
    
    @classmethod
    def get_supported_task_types(cls) -> List[TaskType]:
        """获取所有支持的任务类型"""
        return list(cls._processors.keys())

    @classmethod
    def get_all_task_types(cls) -> List[TaskType]:
        """获取所有任务类型（别名方法，保持兼容性）"""
        return cls.get_supported_task_types()
    
    @classmethod
    def get_processor_info(cls) -> dict:
        """获取所有处理器信息"""
        return {
            task_type.value: processor_class.__name__
            for task_type, processor_class in cls._processors.items()
        }


# 处理器注册装饰器
def register_processor(task_type: TaskType):
    """处理器注册装饰器"""
    def decorator(processor_class):
        ProcessorFactory.register_processor(task_type, processor_class)
        return processor_class
    return decorator
