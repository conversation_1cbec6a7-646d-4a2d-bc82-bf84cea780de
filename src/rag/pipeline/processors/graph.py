"""
图处理器

处理知识图谱构建和存储任务
"""

from typing import List

from src.rag.tools import logger
from .base import TaskProcessor, register_processor
from ..models import PipelineTask, TaskResult, TaskType


@register_processor(TaskType.GRAPH_BUILDING)
class GraphBuildingProcessor(TaskProcessor):
    """图构建处理器"""

    def get_supported_task_types(self) -> List[TaskType]:
        """获取支持的任务类型"""
        return [TaskType.GRAPH_BUILDING]

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理图构建任务"""
        try:
            # 验证输入参数
            if not task.parameters:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message="缺少输入参数"
                )

            doc_id = task.parameters.get("doc_id", "unknown")
            logger.info(f"🔨 [图构建] 开始图构建: doc_id={doc_id}")

            # 从前一个任务获取实体和关系数据
            entities = []
            relationships = []
            if pre_task_result and pre_task_result.result_data:
                entities = pre_task_result.result_data.get("entities", [])
                relationships = pre_task_result.result_data.get("relationships", [])
                logger.info(f"🔨 [图构建] 从前一个任务接收数据: {len(entities)} 个实体, {len(relationships)} 个关系")
            else:
                # 兼容旧的方式，从任务参数获取
                entities = task.parameters.get("entities", [])
                relationships = task.parameters.get("relationships", [])
                logger.info(f"🔨 [图构建] 从任务参数获取数据: {len(entities)} 个实体, {len(relationships)} 个关系")

            # 检查是否成功获取到数据
            # 注意：这里不再检查task.parameters，因为数据来自前一个任务的result_data
            if entities is None and relationships is None:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message="无法从前一个任务获取实体和关系数据"
                )
            
            # 如果实体和关系为空，创建一个空图
            if not entities and not relationships:
                logger.info(f"🔨 [图构建] 实体和关系为空，创建空图")
            
            # 构建图数据结构
            graph_data = {
                "nodes": [],
                "edges": [],
            }

            # 处理实体节点
            for entity in entities:
                node = {
                    "id": entity.get("entity_id") or entity.get("entity_name"),
                    "label": entity.get("entity_name"),
                    "type": entity.get("entity_type"),
                    "description": entity.get("description", ""),
                    "properties": {
                        "source_id": entity.get("source_id"),
                        "source_chunk": entity.get("source_chunk"),
                        "doc_id": doc_id,
                        "confidence": entity.get("confidence", 1.0)
                    }
                }
                graph_data["nodes"].append(node)

            # 处理关系边
            for relationship in relationships:
                edge = {
                    "id": relationship.get("relationship_id"),
                    "source_id": relationship.get("src_entity") or relationship.get("source_entity"),
                    "target_id": relationship.get("tgt_entity") or relationship.get("target_entity"),
                    "relationship_type": relationship.get("relationship_type", "RELATED"),
                    "label": relationship.get("description", ""),
                    "type": "relationship",
                    "weight": relationship.get("weight", 1.0),
                    "properties": {
                        "keywords": relationship.get("keywords", ""),
                        "source_chunk": relationship.get("source_chunk"),
                        "doc_id": doc_id
                    }
                }
                graph_data["edges"].append(edge)

            logger.info(f"🔨 [图构建] 图构建完成: {len(graph_data['nodes'])} 个节点, {len(graph_data['edges'])} 个边")

            return TaskResult(
                task_id=task.id,
                success=True,
                result_data={
                    "doc_id": doc_id,
                    "graph": graph_data,
                    "stats": {
                        "nodes": len(graph_data["nodes"]),
                        "edges": len(graph_data["edges"]),
                        "density": len(graph_data["edges"]) / max(len(graph_data["nodes"]), 1)
                    }
                }
            )

        except Exception as e:
            logger.error(f"💥 [图构建] 图构建处理失败: {e}")
            import traceback
            traceback.print_exc()
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message=f"图构建失败: {str(e)}"
            )


@register_processor(TaskType.GRAPH_STORAGE)
class GraphStorageProcessor(TaskProcessor):
    """图存储处理器"""

    def __init__(self, graph_storage=None):
        super().__init__()
        self.graph_storage = graph_storage

    async def initialize(self):
        """初始化图存储客户端"""
        if self.graph_storage is None:
            from src.rag.infrastructure import neo4j_graph_operations
            # 使用全局的Neo4j存储实例
            self.graph_storage = neo4j_graph_operations

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理图存储任务"""
        try:
            # 初始化图存储客户端
            await self.initialize()
            
            # 验证输入参数
            if not task.parameters:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message="缺少输入参数"
                )

            doc_id = task.parameters.get("doc_id", "unknown")
            logger.info(f"💾 [图存储] 开始图存储: doc_id={doc_id}")

            # 从前一个任务获取图数据
            graph_data = None
            if pre_task_result and pre_task_result.result_data:
                # 修复：图构建任务返回的数据在 "graph" 键下，不是 "graph_data"
                graph_data = pre_task_result.result_data.get("graph")
                if graph_data:
                    logger.info(f"💾 [图存储] 从前一个任务接收图数据")
                else:
                    # 兼容旧的键名
                    graph_data = pre_task_result.result_data.get("graph_data")
                    if graph_data:
                        logger.info(f"💾 [图存储] 从前一个任务接收图数据（兼容模式）")

            # 如果前一个任务没有图数据，尝试从参数中获取
            if not graph_data:
                graph_data = task.parameters.get("graph_data")
                if graph_data:
                    logger.info(f"💾 [图存储] 从任务参数获取图数据")

            # 检查是否有图数据
            if not graph_data:
                logger.warning(f"💾 [图存储] 未找到图数据，创建空图")
                graph_data = {
                    "nodes": [],
                    "edges": [],
                }

            nodes = graph_data.get("nodes", [])
            edges = graph_data.get("edges", [])
            
            logger.info(f"💾 [图存储] 准备存储图: {len(nodes)} 个节点, {len(edges)} 个边")

            if not self.graph_storage:
                logger.warning("💾 [图存储] 缺少图存储实例，跳过实际存储")
                return TaskResult(
                    task_id=task.id,
                    success=True,
                    result_data={
                        "doc_id": doc_id,
                        "storage_status": "skipped",
                        "nodes_count": len(nodes),
                        "edges_count": len(edges),
                        "message": "缺少图存储实例，存储已跳过"
                    }
                )

            # 使用新的方法进行存储
            await self.graph_storage.upsert_document_graph(doc_id, nodes, edges)

            logger.info(f"💾 [图存储] 图存储任务完成: doc_id={doc_id}")

            return TaskResult(
                task_id=task.id,
                success=True,
                result_data={
                    "doc_id": doc_id,
                    "storage_status": "completed",
                    "stored_nodes": len(nodes),
                    "stored_edges": len(edges)
                }
            )

        except Exception as e:
            logger.error(f"💥 [图存储] 图存储处理失败: {e}", exc_info=True)
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message=f"图存储失败: {str(e)}"
            ) 