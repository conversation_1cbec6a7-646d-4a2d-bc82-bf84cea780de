"""
任务管理数据模型

定义任务和流水线的数据结构
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List

from .enums import TaskStatus, PipelineStatus, TaskType


@dataclass
class Pipeline:
    """
    流水线数据模型

    表示一个包含多个任务的处理流水线
    """
    id: str
    name: str
    description: Optional[str] = None
    status: PipelineStatus = PipelineStatus.PENDING
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_by: Optional[str] = None
    error_message: Optional[str] = None
    doc_id: Optional[str] = None


@dataclass
class PipelineTask:
    """
    流水线任务数据模型

    表示流水线中的一个任务节点
    """
    id: str
    pipeline_id: str
    task_type: TaskType
    status: TaskStatus
    parameters: Optional[dict] = None  # 任务参数
    output_data: Optional[dict] = None  # 任务输出数据
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    step_order: int = 0
    dependencies: List[str] = None
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


@dataclass
class TaskMetrics:
    """
    任务指标数据模型

    用于监控和统计任务执行情况
    """
    task_id: str
    execution_time: float
    memory_usage: int
    cpu_usage: float
    success: bool
    timestamp: Optional[datetime] = None


@dataclass
class TaskResult:
    """
    任务结果数据模型

    表示任务执行的结果
    """
    task_id: str
    success: bool
    result_data: Optional[dict] = None  # 任务输出数据，用于传递给下一个任务
    redis_chunk_key: Optional[str] = None
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    memory_usage: Optional[int] = None
    created_at: Optional[datetime] = None
