"""
任务管理枚举定义

定义任务和流水线相关的状态枚举
"""

from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 待处理
    RUNNING = "running"  # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消
    RETRY = "retry"  # 等待重试
    DEPENDENCY_FAILED = "dependency_failed"  # 依赖任务失败


class PipelineStatus(Enum):
    """流水线状态枚举"""
    PENDING = "pending"  # 待处理
    RUNNING = "running"  # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


class TaskType(Enum):
    """任务类型枚举"""
    DOCUMENT_CHUNKING = "document_chunking"  # 文档分块
    VECTOR_EMBEDDING = "vector_embedding"  # 向量嵌入
    VECTOR_STORAGE = "vector_storage"  # 向量存储
    ENTITY_EXTRACTION = "entity_extraction"  # 实体抽取
    GRAPH_BUILDING = "graph_building"  # 图构建
    GRAPH_STORAGE = "graph_storage"  # 图存储
    CLEANUP = "cleanup"  # 清理临时数据

    QUERY_PROCESSING = "query_processing"  # 查询处理