"""
任务处理器

为不同类型的任务提供具体的处理逻辑
"""

# 保持向后兼容性
from .models import PipelineTask, Pipeline, TaskResult, TaskMetrics, TaskType
from .models import TaskType, TaskStatus, PipelineStatus
from .pipeline_executor import pipeline_executor
from .pipeline_manager import pipeline_manager
from .processors import (
    TaskProcessor,
    ProcessorFactory,
    register_processor,
    DocumentChunkingProcessor,
    EntityExtractionProcessor,
    VectorEmbeddingProcessor,
    VectorStorageProcessor,
    GraphBuildingProcessor,
    GraphStorageProcessor,
    QueryProcessor
)

__all__ = [
    "Pipeline",
    "PipelineTask",
    "TaskMetrics",
    "TaskResult",

    "TaskType",
    "TaskStatus",
    "PipelineStatus",


    "TaskProcessor",
    "ProcessorFactory",
    "register_processor",
    "DocumentChunkingProcessor",
    "EntityExtractionProcessor",
    "VectorEmbeddingProcessor",
    "VectorStorageProcessor",
    "GraphBuildingProcessor",
    "GraphStorageProcessor",
    "QueryProcessor",

    "pipeline_executor",
    "pipeline_manager",
]