"""
MixRAG: 混合检索增强生成系统

一个集成了多种存储后端的知识图谱检索增强生成系统
支持文档处理、实体抽取、图谱构建和智能检索
"""

__version__ = "1.0.0"

# 核心组件导入
from .pipeline.models import PipelineTask, Pipeline, TaskResult, TaskMetrics, TaskType
from .pipeline.models import TaskType, TaskStatus, PipelineStatus
from .pipeline.pipeline_executor import pipeline_executor
from .pipeline.pipeline_manager import pipeline_manager

# 服务组件导入
from .service.text_chunking import DocumentChunker, DocumentChunk, chunking_by_token_size
from .service.entity_extraction import EntityExtractor, Entity, Relationship, extract_entities_from_chunks

# LLM 组件导入
from .llm import call_llm, call_embedding, PROMPTS

# 基础设施组件导入
from .infrastructure.schemas.base import BaseKVBaseStorage, BaseVectorBaseStorage, BaseGraphBaseStorage

# 工具导入
from .tools import logger, compute_mdhash_id

__all__ = [
    # 版本信息
    "__version__",
    
    # 流水线模型
    "Pipeline",
    "PipelineTask", 
    "TaskMetrics",
    "TaskResult",
    "TaskType",
    "TaskStatus", 
    "PipelineStatus",
    
    # 流水线管理
    "pipeline_manager",
    "pipeline_executor",
    
    # 服务组件
    "DocumentChunker",
    "DocumentChunk", 
    "chunking_by_token_size",
    "EntityExtractor",
    "Entity",
    "Relationship", 
    "extract_entities_from_chunks",
    
    # LLM 组件
    "call_llm",
    "call_embedding",
    "PROMPTS",
    
    # 基础设施
    "BaseKVBaseStorage",
    "BaseVectorBaseStorage", 
    "BaseGraphBaseStorage",
    
    # 工具
    "logger",
    "compute_mdhash_id",
]
