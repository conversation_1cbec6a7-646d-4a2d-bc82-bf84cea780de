"""
Swagger UI 配置文件
"""
from typing import Dict, Any

# API 标签元数据
TAGS_METADATA = [
    {
        "name": "健康检查",
        "description": "系统健康状态检查和基本信息获取",
        "externalDocs": {
            "description": "健康检查最佳实践",
            "url": "https://github.com/HKUDS/MixRAG/wiki/health-check",
        },
    },
    {
        "name": "任务管理",
        "description": "单个任务的创建、查询、取消等操作。支持各种类型的异步任务处理。",
        "externalDocs": {
            "description": "任务管理文档",
            "url": "https://github.com/HKUDS/MixRAG/wiki/task-management",
        },
    },
    {
        "name": "任务链管理",
        "description": "复杂工作流的管理，支持任务依赖和串行执行。",
        "externalDocs": {
            "description": "任务链文档",
            "url": "https://github.com/HKUDS/MixRAG/wiki/task-chains",
        },
    },
    {
        "name": "文档处理",
        "description": "文档解析、分块、实体抽取和知识图谱构建的完整流程。",
        "externalDocs": {
            "description": "文档处理指南",
            "url": "https://github.com/HKUDS/MixRAG/wiki/document-processing",
        },
    },
    {
        "name": "文档上传",
        "description": "文件上传、存储管理和批量处理功能。",
        "externalDocs": {
            "description": "文件上传指南",
            "url": "https://github.com/HKUDS/MixRAG/wiki/file-upload",
        },
    },
    {
        "name": "查询",
        "description": "基于知识图谱的智能查询，支持同步和异步查询模式。",
        "externalDocs": {
            "description": "查询接口文档",
            "url": "https://github.com/HKUDS/MixRAG/wiki/query-api",
        },
    },
    {
        "name": "系统管理",
        "description": "系统统计、配置管理和维护工具。",
        "externalDocs": {
            "description": "系统管理文档",
            "url": "https://github.com/HKUDS/MixRAG/wiki/admin",
        },
    },
    {
        "name": "API 示例",
        "description": "API 使用示例和最佳实践演示。包含各种常见的 API 操作模式。",
        "externalDocs": {
            "description": "API 使用指南",
            "url": "https://github.com/HKUDS/MixRAG/wiki/api-examples",
        },
    },
]

# 服务器配置
SERVERS = [
    {
        "url": "http://localhost:8000",
        "description": "开发环境"
    },
    {
        "url": "https://api.mixrag.example.com",
        "description": "生产环境"
    },
    {
        "url": "https://staging-api.mixrag.example.com",
        "description": "测试环境"
    }
]

# 自定义 Swagger UI 配置
SWAGGER_UI_PARAMETERS = {
    "deepLinking": True,
    "displayRequestDuration": True,
    "docExpansion": "list",
    "operationsSorter": "method",
    "filter": True,
    "showExtensions": True,
    "showCommonExtensions": True,
    "defaultModelsExpandDepth": 2,
    "defaultModelExpandDepth": 2,
    "displayOperationId": False,
    "tryItOutEnabled": True,
    "persistAuthorization": True,
    "layout": "BaseLayout"
}

# 自定义 CSS 样式
CUSTOM_CSS = """
.swagger-ui .topbar {
    background-color: #1e293b;
    border-bottom: 3px solid #3b82f6;
}

.swagger-ui .topbar .download-url-wrapper {
    display: none;
}

.swagger-ui .info .title {
    color: #1e293b;
    font-size: 36px;
    font-weight: bold;
}

.swagger-ui .info .description {
    color: #475569;
    font-size: 16px;
    line-height: 1.6;
}

.swagger-ui .scheme-container {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
}

.swagger-ui .opblock.opblock-post {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.swagger-ui .opblock.opblock-get {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.swagger-ui .opblock.opblock-put {
    border-color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.swagger-ui .opblock.opblock-delete {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.swagger-ui .opblock-tag {
    font-size: 20px;
    font-weight: bold;
    color: #1e293b;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.swagger-ui .btn.authorize {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.swagger-ui .btn.authorize:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.swagger-ui .model-box {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
}

.swagger-ui .model-title {
    color: #1e293b;
    font-weight: bold;
}

.swagger-ui .parameter__name {
    color: #1e293b;
    font-weight: bold;
}

.swagger-ui .parameter__type {
    color: #059669;
    font-weight: normal;
}

.swagger-ui .response-col_status {
    font-weight: bold;
}

.swagger-ui .response .response-controls {
    margin-top: 10px;
}

/* 自定义滚动条 */
.swagger-ui ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.swagger-ui ::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.swagger-ui ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.swagger-ui ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
"""

# 自定义 JavaScript
CUSTOM_JS = """
// 自定义 Swagger UI 行为
window.onload = function() {
    // 自动展开第一个标签
    setTimeout(function() {
        const firstTag = document.querySelector('.opblock-tag-section');
        if (firstTag && !firstTag.classList.contains('is-open')) {
            firstTag.click();
        }
    }, 1000);
    
    // 添加版权信息
    const infoSection = document.querySelector('.info');
    if (infoSection) {
        const copyright = document.createElement('div');
        copyright.innerHTML = '<p style="margin-top: 20px; color: #64748b; font-size: 14px;">© 2024 MixRAG Team. All rights reserved.</p>';
        infoSection.appendChild(copyright);
    }
    
    // 添加使用统计（可选）
    console.log('MixRAG API Documentation loaded');
};
"""


def get_security_schemes() -> Dict[str, Any]:
    """获取安全认证方案"""
    return {
        "APIKeyHeader": {
            "type": "apiKey",
            "in": "header",
            "name": "X-API-Key",
            "description": "API 密钥认证。在请求头中添加 X-API-Key 字段。"
        },
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT Token 认证。格式：Bearer <token>"
        },
        "BasicAuth": {
            "type": "http",
            "scheme": "basic",
            "description": "HTTP Basic 认证。格式：Basic <credentials>"
        }
    }


def get_common_responses() -> Dict[str, Any]:
    """获取通用响应定义"""
    return {
        "ValidationError": {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {
                        "error": "请求参数验证失败",
                        "detail": "field 'query' is required",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        "ServerError": {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {
                        "error": "服务器内部错误",
                        "detail": "数据库连接失败",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        "NotFound": {
            "description": "资源不存在",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {
                        "error": "资源不存在",
                        "detail": "任务 ID 'task-123' 不存在",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        "Unauthorized": {
            "description": "未授权访问",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {
                        "error": "未授权访问",
                        "detail": "无效的 API 密钥",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        "Forbidden": {
            "description": "访问被禁止",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {
                        "error": "访问被禁止",
                        "detail": "权限不足",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        "TooManyRequests": {
            "description": "请求过于频繁",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {
                        "error": "请求过于频繁",
                        "detail": "已达到速率限制，请稍后重试",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        }
    }


def get_common_schemas() -> Dict[str, Any]:
    """获取通用 Schema 定义"""
    return {
        "ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string",
                    "description": "错误信息",
                    "example": "请求参数无效"
                },
                "detail": {
                    "type": "string",
                    "description": "详细错误信息",
                    "example": "字段 'content' 不能为空"
                },
                "timestamp": {
                    "type": "string",
                    "format": "date-time",
                    "description": "错误发生时间",
                    "example": "2024-01-01T12:00:00Z"
                },
                "request_id": {
                    "type": "string",
                    "description": "请求追踪ID",
                    "example": "req_1234567890"
                }
            },
            "required": ["error", "timestamp"]
        },
        "SuccessResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string",
                    "description": "成功信息",
                    "example": "操作成功完成"
                },
                "data": {
                    "type": "object",
                    "description": "响应数据"
                },
                "timestamp": {
                    "type": "string",
                    "format": "date-time",
                    "description": "响应时间",
                    "example": "2024-01-01T12:00:00Z"
                }
            },
            "required": ["message", "timestamp"]
        },
        "PaginationInfo": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer",
                    "description": "总记录数",
                    "example": 100
                },
                "page": {
                    "type": "integer",
                    "description": "当前页码",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "description": "每页数量",
                    "example": 20
                },
                "total_pages": {
                    "type": "integer",
                    "description": "总页数",
                    "example": 5
                }
            },
            "required": ["total", "page", "page_size"]
        }
    }
