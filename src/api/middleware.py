"""
中间件配置模块

配置FastAPI应用的各种中间件
"""

import datetime
import enum
import functools
from typing import Callable

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse


def setup_cors_middleware(app: FastAPI) -> None:
    """
    配置CORS中间件
    
    允许跨域请求，适用于前后端分离的架构
    """
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应限制特定域名
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )


def setup_middleware(app: FastAPI) -> None:
    """
    设置所有中间件
    """
    setup_cors_middleware(app)


def serialize_data(data, _depth=0, _max_depth=10):
    """序列化数据，处理Pydantic模型等特殊对象"""
    from types import MappingProxyType

    # 防止无限递归
    if _depth > _max_depth:
        return f"<max_depth_reached: {type(data).__name__}>"

    if isinstance(data, (int, float, str, bool)) or data is None:
        # 基础类型直接返回
        return data
    elif hasattr(data, 'dict') and callable(getattr(data, 'dict')):
        return serialize_data(data.dict(), _depth + 1, _max_depth)
    elif isinstance(data, datetime.datetime):
        # datetime对象转换为ISO格式字符串
        return data.isoformat()
    elif isinstance(data, datetime.date):
        # date对象转换为ISO格式字符串
        return data.isoformat()
    elif isinstance(data, enum.Enum):
        # 枚举对象转换为值
        return data.value
    elif isinstance(data, (list, tuple)):
        # 列表和元组，递归处理每个元素
        return [serialize_data(item, _depth + 1, _max_depth) for item in data]
    elif isinstance(data, (dict, MappingProxyType)):
        # 字典和映射代理，递归处理每个值
        return {str(key): serialize_data(value, _depth + 1, _max_depth) for key, value in data.items()}
    elif hasattr(data, '__dict__'):
        # 普通类实例 - 排除特殊类型
        if hasattr(data, '__class__') and data.__class__.__name__ in ['type', 'module', 'function', 'method']:
            return str(data)
        return serialize_data(data.__dict__, _depth + 1, _max_depth)
    else:
        # 其他类型转换为字符串
        return str(data)


def unified_response_wrapper(func: Callable) -> Callable:
    """
    统一响应包装器装饰器
    
    包装所有 API 接口返回值为统一格式：
    {
        "success": bool,
        "data": Any,
        "message": str
    }
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 执行原函数
        result = await func(*args, **kwargs)

        # 如果是StreamingResponse，直接返回，不进行包装
        if isinstance(result, StreamingResponse):
            return result

        # 完全序列化数据
        serialized_result = serialize_data(result)

        # 如果返回值已经是统一格式，直接返回
        if isinstance(serialized_result, dict) and all(key in serialized_result for key in ["success", "data", "message"]):
            return JSONResponse(content=serialized_result)

        # 包装成功响应
        response_data = {
            "success": True,
            "data": serialized_result,
            "message": "操作成功"
        }

        return JSONResponse(content=response_data)

    return wrapper


def apply_unified_response_wrapper(router):
    """
    为路由器中的所有路由应用统一响应包装器
    
    Args:
        router: FastAPI 路由器实例
    """
    for route in router.routes:
        if hasattr(route, 'endpoint') and callable(route.endpoint):
            # 如果路由函数没有被装饰，则应用装饰器
            if not hasattr(route.endpoint, '_is_wrapped'):
                original_endpoint = route.endpoint
                wrapped_endpoint = unified_response_wrapper(original_endpoint)
                wrapped_endpoint._is_wrapped = True
                route.endpoint = wrapped_endpoint
