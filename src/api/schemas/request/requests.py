"""
API 请求模型

定义所有 API 接口的输入参数模型
"""

from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field


class DocumentUploadRequest(BaseModel):
    """文档上传请求"""
    filename: str = Field(..., description="文件名")
    content_type: str = Field(..., description="文件类型")


class DocumentProcessRequest(BaseModel):
    """文档处理请求"""
    doc_id: str = Field(..., description="文档ID")
    content: Optional[str] = Field(None, description="文档内容")
    chunk_size: Optional[int] = Field(512, description="分块大小")
    overlap_size: Optional[int] = Field(50, description="重叠大小")


class QueryRequest(BaseModel):
    """查询请求"""
    query: str = Field(..., description="查询内容")
    mode: str = Field("mix", description="检索模式: local, global, mix")
    top_k: Optional[int] = Field(10, description="返回结果数量")


class GraphQueryRequest(BaseModel):
    """图查询请求"""
    query: str = Field(..., description="图查询语句")
    limit: Optional[int] = Field(100, description="结果限制")


class PipelineCreateRequest(BaseModel):
    """流水线创建请求"""
    name: str = Field(..., description="流水线名称")
    description: Optional[str] = Field(None, description="流水线描述")
    tasks: List[Dict[str, Any]] = Field(..., description="任务列表")


class TaskCreateRequest(BaseModel):
    """任务创建请求"""
    task_type: str = Field(..., description="任务类型")
    parameters: Dict[str, Any] = Field(..., description="任务参数")


class PaginationRequest(BaseModel):
    """分页请求"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")


class DocumentSearchRequest(PaginationRequest):
    """文档搜索请求"""
    keyword: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[str] = Field(None, description="文档状态")


class ChunkSearchRequest(PaginationRequest):
    """分块搜索请求"""
    doc_id: Optional[str] = Field(None, description="文档ID")
    content: Optional[str] = Field(None, description="内容搜索")


class EntitySearchRequest(PaginationRequest):
    """实体搜索请求"""
    entity_type: Optional[str] = Field(None, description="实体类型")
    entity_name: Optional[str] = Field(None, description="实体名称")


class RelationshipSearchRequest(PaginationRequest):
    """关系搜索请求"""
    source_entity: Optional[str] = Field(None, description="源实体")
    target_entity: Optional[str] = Field(None, description="目标实体")
    relation_type: Optional[str] = Field(None, description="关系类型")
