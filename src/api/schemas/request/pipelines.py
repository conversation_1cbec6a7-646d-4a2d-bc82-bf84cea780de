"""
流水线请求相关的数据模型
"""

from typing import Dict, Any, Optional, List

from pydantic import BaseModel, Field

from ..models.base import TaskTypeEnum


class TaskInPipelineRequest(BaseModel):
    """流水线中的任务请求"""
    task_type: TaskTypeEnum
    step_order: int = 0
    max_retries: int = 3


class CreatePipelineRequest(BaseModel):
    """创建流水线请求"""
    name: str = Field(..., description="流水线名称")
    description: Optional[str] = Field(None, description="流水线描述")
    tasks: List[TaskInPipelineRequest] = Field(..., description="任务列表")
    created_by: Optional[str] = Field(None, description="创建者")


class DocumentPipelineRequest(BaseModel):
    """文档处理流水线请求"""
    doc_id: str = Field(..., description="文档ID")
    pipeline_name: Optional[str] = Field("文档处理流水线", description="流水线名称")
    enable_chunking: bool = Field(True, description="是否启用文档分块")
    enable_entity_extraction: bool = Field(True, description="是否启用实体抽取")
    enable_graph_building: bool = Field(True, description="是否启用知识图谱构建")
    enable_vector_embedding: bool = Field(True, description="是否启用向量化")
    chunk_size: int = Field(500, description="分块大小")
    chunk_overlap: int = Field(50, description="分块重叠")
    created_by: Optional[str] = Field(None, description="创建者")
    document_content: Optional[str] = Field(None, description="文档内容（直接传递内容时使用）")
