"""
任务请求相关的数据模型
"""

from typing import Dict, Any, Optional

from pydantic import BaseModel, Field

from ..models.base import TaskTypeEnum, TaskStatusEnum


class TaskRequest(BaseModel):
    """任务请求"""
    task_type: TaskTypeEnum


class CreateTaskRequest(BaseModel):
    """创建任务请求"""
    task_type: TaskTypeEnum
    max_retries: int = 3


class UpdateTaskRequest(BaseModel):
    """更新任务请求"""
    status: Optional[TaskStatusEnum] = None
    max_retries: Optional[int] = None
