"""
API 请求模型包

包含所有 API 接口的输入参数模型
"""

from .requests import *
from .queries import *
from .graphs import *
from .documents import *
from .chunks import *
from .pipelines import *
from .tasks import *

__all__ = [
    # 通用请求模型
    'DocumentUploadRequest',
    'DocumentProcessRequest',
    'QueryRequest',
    'GraphQueryRequest',
    'PipelineCreateRequest',
    'TaskCreateRequest',
    'PaginationRequest',

    # 查询请求模型
    'SyncQueryRequest',

    # 图查询请求模型
    'NodeQueryRequest',
    'NeighborsQueryRequest',
    'EdgeQueryRequest',
    'PathQueryRequest',
    'TraversalQueryRequest',
    'SubGraphQueryRequest',
    'GraphSearchRequest',

    # 文档请求模型
    'DocumentStatusUpdate',
    'DocumentSearchRequest',
    'FileUploadRequest',

    # Chunk请求模型
    'ChunkSimilaritySearchRequest',
    'ChunkKeywordSearchRequest',

    # 流水线请求模型
    'TaskInPipelineRequest',
    'CreatePipelineRequest',
    'DocumentPipelineRequest',

    # 任务请求模型
    'TaskRequest',
    'CreateTaskRequest',
    'UpdateTaskRequest',
]
