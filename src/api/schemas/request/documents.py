"""
文档管理相关的请求模型
"""

from typing import Optional, Dict, Any

from pydantic import BaseModel, Field, validator

from ..models.documents import UploadStatus, ProcessStatus


class DocumentStatusUpdate(BaseModel):
    """文档状态更新请求"""
    upload_status: Optional[UploadStatus] = Field(None, description="上传状态")
    process_status: Optional[ProcessStatus] = Field(None, description="处理状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    chunks_count: Optional[int] = Field(None, description="分块数量")
    entities_count: Optional[int] = Field(None, description="实体数量")
    relationships_count: Optional[int] = Field(None, description="关系数量")


class DocumentProcessRequest(BaseModel):
    """文档处理请求"""
    doc_id: str = Field(..., description="文档ID")
    force_reprocess: bool = Field(False, description="是否强制重新处理")


class DocumentSearchRequest(BaseModel):
    """文档搜索请求"""
    filename: Optional[str] = Field(None, description="文件名搜索")
    upload_status: Optional[UploadStatus] = Field(None, description="上传状态过滤")
    process_status: Optional[ProcessStatus] = Field(None, description="处理状态过滤")
    uploaded_by: Optional[str] = Field(None, description="上传用户过滤")
    limit: int = Field(50, ge=1, le=200, description="限制数量")
    offset: int = Field(0, ge=0, description="偏移量")


class FileUploadRequest(BaseModel):
    """文件上传元数据请求"""
    filename: str = Field(..., description="文件名")
    content_type: Optional[str] = Field(None, description="内容类型")
    uploaded_by: Optional[str] = Field(None, description="上传用户")

    @validator('filename')
    def validate_filename(cls, v):
        """验证文件名"""
        if not v or not v.strip():
            raise ValueError("文件名不能为空")

        # 检查文件名长度
        if len(v) > 255:
            raise ValueError("文件名过长")

        # 检查是否包含非法字符
        illegal_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in illegal_chars:
            if char in v:
                raise ValueError(f"文件名不能包含字符: {char}")

        return v.strip() 