"""
图查询请求相关的数据模式
"""

from typing import List, Dict, Optional, Any, Union

from pydantic import BaseModel, Field

from ..models.graphs import TraversalType


# 请求模型
class NodeQueryRequest(BaseModel):
    """节点查询请求"""
    node_id: str = Field(..., description="节点ID")


class NeighborsQueryRequest(BaseModel):
    """邻居查询请求"""
    node_id: str = Field(..., description="节点ID")
    max_depth: int = Field(1, ge=1, le=5, description="最大深度，1-5之间")
    limit: Optional[int] = Field(None, ge=1, le=1000, description="返回数量限制")


class EdgeQueryRequest(BaseModel):
    """边查询请求"""
    source_id: str = Field(..., description="源节点ID")
    target_id: str = Field(..., description="目标节点ID")


class PathQueryRequest(BaseModel):
    """路径查询请求"""
    source_id: str = Field(..., description="源节点ID")
    target_id: str = Field(..., description="目标节点ID")
    max_depth: int = Field(3, ge=1, le=10, description="最大搜索深度")
    algorithm: str = Field("shortest", description="路径算法：shortest（最短路径）或all（所有路径）")


class TraversalQueryRequest(BaseModel):
    """图遍历查询请求"""
    start_node_id: str = Field(..., description="起始节点ID")
    traversal_type: TraversalType = Field(TraversalType.BFS, description="遍历类型")
    max_depth: int = Field(2, ge=1, le=5, description="最大遍历深度")
    limit: Optional[int] = Field(100, ge=1, le=1000, description="返回数量限制")
    filter_conditions: Optional[Dict[str, Any]] = Field(None, description="过滤条件")


class SubGraphQueryRequest(BaseModel):
    """子图查询请求"""
    center_node_ids: List[str] = Field(..., description="中心节点ID列表")
    radius: int = Field(1, ge=1, le=3, description="子图半径")
    include_isolated: bool = Field(False, description="是否包含孤立节点")


class GraphSearchRequest(BaseModel):
    """图搜索请求"""
    query: str = Field(..., description="搜索查询")
    search_type: str = Field("node", description="搜索类型：node（节点）或edge（边）")
    limit: Optional[int] = Field(20, ge=1, le=100, description="返回数量限制")


class NodeListRequest(BaseModel):
    """节点分页查询请求"""
    page: int = Field(1, ge=1, description="页码，从1开始")
    page_size: int = Field(20, ge=1, le=100, description="每页数量，1-100之间")
    node_type: Optional[str] = Field(None, description="节点类型筛选")
    property_filters: Optional[Dict[str, Any]] = Field(None, description="属性筛选条件")
    search_text: Optional[str] = Field(None, description="文本搜索")
    sort_by: Optional[str] = Field("id", description="排序字段")
    sort_order: Optional[str] = Field("asc", description="排序方向：asc或desc")


class EdgeListRequest(BaseModel):
    """边分页查询请求"""
    page: int = Field(1, ge=1, description="页码，从1开始")
    page_size: int = Field(20, ge=1, le=100, description="每页数量，1-100之间")
    edge_type: Optional[str] = Field(None, description="边类型筛选")
    source_id: Optional[str] = Field(None, description="源节点ID筛选")
    target_id: Optional[str] = Field(None, description="目标节点ID筛选")
    property_filters: Optional[Dict[str, Any]] = Field(None, description="属性筛选条件")
    search_text: Optional[str] = Field(None, description="文本搜索")
    sort_by: Optional[str] = Field("source_id", description="排序字段")
    sort_order: Optional[str] = Field("asc", description="排序方向：asc或desc")


class GraphQueryRequest(BaseModel):
    """图查询请求 - 同时查询节点和边"""
    label: Optional[str] = Field(None, description="标签过滤条件，*表示不过滤")
    max_depth: int = Field(2, ge=1, le=5, description="图遍历最大深度，1-5之间")
    max_nodes: int = Field(100, ge=1, le=1000, description="总节点数限制，1-1000之间")
