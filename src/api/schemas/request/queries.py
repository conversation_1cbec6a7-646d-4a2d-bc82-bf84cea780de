"""
查询相关数据模型
"""

from typing import Dict, Any, Optional

from pydantic import BaseModel, Field


class QueryRequest(BaseModel):
    """查询请求"""
    query: str = Field(..., description="查询内容")
    mode: str = Field(default="mix", description="查询模式: local, global, mix")
    query_param: Optional[Dict[str, Any]] = Field(default=None, description="查询参数")


class SyncQueryRequest(QueryRequest):
    """同步查询请求"""
    timeout: float = Field(default=30.0, description="超时时间（秒）")

