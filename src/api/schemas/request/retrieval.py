"""
检索相关请求数据模型
"""

from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field


class ConversationMessage(BaseModel):
    """对话消息"""
    role: Literal["user", "assistant"] = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")


class RetrievalQueryRequest(BaseModel):
    """检索查询请求"""
    query: str = Field(..., description="查询内容")
    mode: Literal["naive", "global", "mix"] = Field(default="mix", description="检索模式")
    response_format: Literal["单段落", "多段落", "要点"] = Field(default="多段落", description="响应格式")
    top_k: int = Field(default=10, ge=1, le=50, description="返回结果数量")
    similarity_threshold: Optional[float] = Field(default=0.75, ge=0.01, le=0.99, description="相似度阈值")
    text_unit_max_tokens: int = Field(default=4000, ge=100, le=8000, description="文本单元最大token数")
    global_context_max_tokens: int = Field(default=4000, ge=100, le=8000, description="全局上下文最大token数")
    history_turns: int = Field(default=3, ge=0, le=20, description="历史轮次")
    user_prompt: Optional[str] = Field(default="", description="用户自定义提示词")
    context_only: bool = Field(default=False, description="仅需上下文")
    prompt_only: bool = Field(default=False, description="仅需提示")
    stream: bool = Field(default=False, description="是否启用流式响应")
    conversation_history: List[ConversationMessage] = Field(default_factory=list, description="对话历史")


class RetrievalStreamRequest(RetrievalQueryRequest):
    """流式检索查询请求"""
    stream: bool = Field(default=True, description="强制启用流式响应")


class RetrievalHistoryRequest(BaseModel):
    """检索历史请求"""
    history: List[Dict[str, Any]] = Field(..., description="历史记录数据")


class RetrievalHistoryQueryRequest(BaseModel):
    """检索历史查询请求"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    start_date: Optional[str] = Field(default=None, description="开始日期")
    end_date: Optional[str] = Field(default=None, description="结束日期")
