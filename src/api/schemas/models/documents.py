"""
文档管理相关的 API 模式定义
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from pydantic import BaseModel, Field


class UploadStatus(str, Enum):
    """上传状态枚举"""
    PENDING = "pending"
    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    FAILED = "failed"


class ProcessStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    CHUNK_TEXT = "chunk_text"
    EXTRACT_ENTITIES = "extract_entities"
    BUILD_GRAPH = "build_graph"
    COMPLETED = "completed"  # 向后兼容
    FAILED = "failed"


class ProcessStep(str, Enum):
    """处理步骤枚举"""
    UPLOAD = "upload"
    CHUNK_TEXT = "chunk_text"
    EXTRACT_ENTITIES = "extract_entities"
    BUILD_GRAPH = "build_graph"
    COMPLETE = "complete"


class DocumentInfo(BaseModel):
    """文档详细信息"""
    doc_id: str = Field(..., description="文档唯一标识")
    original_filename: str = Field(..., description="原始文件名")
    file_extension: Optional[str] = Field(None, description="文件扩展名")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    mime_type: Optional[str] = Field(None, description="MIME类型")
    minio_bucket: str = Field(..., description="MINIO存储桶")
    minio_object_key: str = Field(..., description="MINIO对象键")
    content_hash: Optional[str] = Field(None, description="内容哈希值")
    upload_status: UploadStatus = Field(..., description="上传状态")
    process_status: ProcessStatus = Field(..., description="处理状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    uploaded_by: Optional[str] = Field(None, description="上传用户")
    error_message: Optional[str] = Field(None, description="错误信息")
    chunks_count: int = Field(0, description="分块数量")
    entities_count: int = Field(0, description="实体数量")
    relationships_count: int = Field(0, description="关系数量")
    pipeline_id: Optional[str] = Field(None, description="关联的流水线ID")
