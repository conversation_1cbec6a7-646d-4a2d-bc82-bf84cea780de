"""
图查询相关的数据模式
"""

from enum import Enum
from typing import List, Dict, Optional, Any

from pydantic import BaseModel, Field


class TraversalType(str, Enum):
    """图遍历类型"""
    DFS = "dfs"  # 深度优先遍历
    BFS = "bfs"  # 广度优先遍历


class GraphNode(BaseModel):
    """图节点模型"""
    entity_id: str = Field(..., description="节点ID")
    entity_type: Optional[str] = Field(None, description="节点类型")
    description: Optional[str] = Field(None, description="节点描述")
    properties: Dict[str, Any] = Field(default_factory=dict, description="节点属性")


class GraphEdge(BaseModel):
    """图边模型"""
    source_id: str = Field(..., description="源节点ID")
    target_id: str = Field(..., description="目标节点ID")
    weight: Optional[float] = Field(1.0, description="边权重")
    description: Optional[str] = Field(None, description="边描述")
    keywords: Optional[str] = Field(None, description="关键词")
    properties: Dict[str, Any] = Field(default_factory=dict, description="边属性")


class GraphPath(BaseModel):
    """图路径模型"""
    nodes: List[str] = Field(..., description="路径节点ID列表")
    edges: List[GraphEdge] = Field(..., description="路径边列表")
    total_weight: Optional[float] = Field(None, description="路径总权重")
    length: int = Field(..., description="路径长度")


class SubGraph(BaseModel):
    """子图模型"""
    nodes: List[GraphNode] = Field(..., description="子图节点列表")
    edges: List[GraphEdge] = Field(..., description="子图边列表")
    node_count: int = Field(..., description="节点数量")
    edge_count: int = Field(..., description="边数量")


class GraphStats(BaseModel):
    """图统计信息"""
    total_nodes: int = Field(..., description="总节点数")
    total_edges: int = Field(..., description="总边数")
    avg_degree: Optional[float] = Field(None, description="平均度数")
    max_degree: Optional[int] = Field(None, description="最大度数")
    min_degree: Optional[int] = Field(None, description="最小度数")
    node_types: Dict[str, int] = Field(default_factory=dict, description="节点类型统计")
