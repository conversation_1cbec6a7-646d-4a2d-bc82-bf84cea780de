"""
基础数据模型和枚举定义
"""

import math
from datetime import datetime
from enum import Enum
from typing import List, TypeVar, Generic, Any

from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """通用响应基类"""
    success: bool = Field(True, description="操作是否成功")
    message: str = Field("操作成功", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间戳")


class TaskTypeEnum(str, Enum):
    """任务类型枚举"""
    DOCUMENT_CHUNKING = "document_chunking"
    ENTITY_EXTRACTION = "entity_extraction"
    GRAPH_BUILDING = "graph_building"
    VECTOR_EMBEDDING = "vector_embedding"
    GRAPH_STORAGE = "graph_storage"
    QUERY_PROCESSING = "query_processing"


class TaskStatusEnum(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRY = "retry"
    DEPENDENCY_FAILED = "dependency_failed"


class PaginationInfo(BaseModel):
    """分页信息"""
    page: int = Field(..., description="当前页码", example=1)
    page_size: int = Field(..., description="每页数量", example=20)
    total: int = Field(..., description="总记录数", example=100)
    total_pages: int = Field(..., description="总页数", example=5)


# 泛型类型变量，用于分页响应的数据类型
T = TypeVar('T')


class PaginatedResponse(BaseModel, Generic[T]):
    """统一的分页响应基类"""
    records: List[T] = Field(..., description="数据列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


def create_paginated_response(records: List[T], page: int, page_size: int, total: int) -> PaginatedResponse[T]:
    """创建分页响应"""
    total_pages = math.ceil(total / page_size) if page_size > 0 else 0

    pagination = PaginationInfo(
        page=page,
        page_size=page_size,
        total=total,
        total_pages=total_pages
    )

    return PaginatedResponse(
        records=records,
        pagination=pagination
    )
