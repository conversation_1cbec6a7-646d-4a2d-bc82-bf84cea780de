"""
知识库管理相关的 API 模式定义
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


class KnowledgeBaseInfo(BaseModel):
    """知识库详细信息"""
    id: str = Field(..., description="知识库唯一标识")
    kb_name: str = Field(..., description="知识库名称")
    kb_des: Optional[str] = Field(None, description="知识库描述")
    doc_count: int = Field(0, description="文档数量")
    create_time: datetime = Field(..., description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")


class KnowledgeBaseDetail(KnowledgeBaseInfo):
    """知识库详细信息（包含扩展统计）"""
    chunks_count: Optional[int] = Field(0, description="分块数量")
    entities_count: Optional[int] = Field(0, description="实体数量")
    relationships_count: Optional[int] = Field(0, description="关系数量")
    total_file_size: Optional[int] = Field(0, description="总文件大小")


class KnowledgeBaseStats(BaseModel):
    """知识库统计信息"""
    total_knowledge_bases: int = Field(0, description="知识库总数")
    total_documents: int = Field(0, description="文档总数")
    total_chunks: int = Field(0, description="分块总数")
    total_entities: int = Field(0, description="实体总数")
    total_relationships: int = Field(0, description="关系总数")
    total_file_size: int = Field(0, description="文件总大小")
    avg_docs_per_kb: float = Field(0.0, description="平均每个知识库的文档数")


class CreateKnowledgeBaseRequest(BaseModel):
    """创建知识库请求"""
    kb_name: str = Field(..., description="知识库名称", min_length=1, max_length=100)
    kb_des: Optional[str] = Field(None, description="知识库描述", max_length=200)


class UpdateKnowledgeBaseRequest(BaseModel):
    """更新知识库请求"""
    kb_name: Optional[str] = Field(None, description="知识库名称", min_length=1, max_length=100)
    kb_des: Optional[str] = Field(None, description="知识库描述", max_length=200)


class KnowledgeBaseListItem(BaseModel):
    """知识库列表项"""
    id: str = Field(..., description="知识库唯一标识")
    kb_name: str = Field(..., description="知识库名称")
    kb_des: Optional[str] = Field(None, description="知识库描述")
    doc_count: int = Field(0, description="文档数量")
    create_time: datetime = Field(..., description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    
    # 可选的扩展信息
    chunks_count: Optional[int] = Field(None, description="分块数量")
    entities_count: Optional[int] = Field(None, description="实体数量")
    relationships_count: Optional[int] = Field(None, description="关系数量")


class KnowledgeBaseSearchRequest(BaseModel):
    """知识库搜索请求"""
    query: str = Field(..., description="搜索关键词", min_length=1)
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(20, description="每页数量", ge=1, le=100)


class KnowledgeBaseListRequest(BaseModel):
    """知识库列表请求"""
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(20, description="每页数量", ge=1, le=100)
    search: Optional[str] = Field(None, description="搜索关键词")
    sort_by: Optional[str] = Field("create_time", description="排序字段")
    sort_order: Optional[str] = Field("desc", description="排序顺序", pattern="^(asc|desc)$")
