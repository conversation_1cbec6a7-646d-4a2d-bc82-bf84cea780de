"""
流水线响应相关的数据模型
"""

from datetime import datetime
from typing import Dict, Any, Optional, List

from pydantic import BaseModel, Field

from ..models.base import PaginatedResponse
from ..models.pipelines import PipelineStatusEnum
from ..models.base import TaskTypeEnum


class PipelineResponse(BaseModel):
    """流水线响应"""
    id: str
    name: str
    description: Optional[str] = None
    status: PipelineStatusEnum
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_by: Optional[str] = None
    error_message: Optional[str] = None


class TaskInPipelineResponse(BaseModel):
    """流水线中的任务响应"""
    id: str
    task_type: TaskTypeEnum
    status: str
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    step_order: int = 0


class PipelineDetailResponse(BaseModel):
    """流水线详情响应"""
    pipeline: PipelineResponse
    tasks: List[TaskInPipelineResponse]


class CreatePipelineResponse(BaseModel):
    """创建流水线响应"""
    pipeline_id: str
    message: str = "流水线创建成功"


class PipelineListResponse(PaginatedResponse[PipelineResponse]):
    """流水线列表响应"""
    pass


class PipelineStatsResponse(BaseModel):
    """流水线统计响应"""
    total_pipelines: int = Field(..., description="总流水线数")
    running_pipelines: int = Field(..., description="运行中的流水线数")
    completed_pipelines: int = Field(..., description="已完成的流水线数")
    failed_pipelines: int = Field(..., description="失败的流水线数")
    avg_execution_time: Optional[float] = Field(None, description="平均执行时间（秒）")
