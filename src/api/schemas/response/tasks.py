"""
任务响应相关的数据模型
"""

from datetime import datetime
from typing import Dict, Any, Optional, List

from pydantic import BaseModel, Field

from ..models.base import TaskTypeEnum, TaskStatusEnum, PaginatedResponse


class TaskResponse(BaseModel):
    """任务响应"""
    id: str
    task_type: TaskTypeEnum
    status: TaskStatusEnum
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    dependencies: Optional[List[str]] = None


# 使用新的统一分页响应格式
TaskListResponse = PaginatedResponse[TaskResponse]


class CreateTaskResponse(BaseModel):
    """创建任务响应"""
    task_id: str
    message: str = "任务创建成功"


class TaskStatsResponse(BaseModel):
    """任务统计响应"""
    is_initialized: bool = Field(..., description="是否已初始化")
    active_chains: int = Field(..., description="活跃任务链数量")
    chain_details: Dict[str, Any] = Field(default_factory=dict, description="任务链详情")
    scheduler_stats: Dict[str, Any] = Field(default_factory=dict, description="调度器统计")
    database_stats: Dict[str, Any] = Field(default_factory=dict, description="数据库统计")
    recent_errors: int = Field(default=0, description="最近错误数量")
    error_details: List[Dict[str, Any]] = Field(default_factory=list, description="错误详情")
