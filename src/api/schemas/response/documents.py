"""
文档响应相关的 API 模式定义
"""

from datetime import datetime
from typing import List, Dict, Any, Optional

from pydantic import BaseModel, Field

from ..models.base import PaginatedResponse
from ..models.documents import DocumentInfo


class DocumentUploadResponse(BaseModel):
    """文档上传响应"""
    doc_id: str = Field(..., description="文档唯一标识")
    original_filename: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    mime_type: str = Field(..., description="MIME类型")
    minio_bucket: str = Field(..., description="MINIO存储桶")
    minio_object_key: str = Field(..., description="MINIO对象键")
    upload_status: str = Field(..., description="上传状态")
    created_at: datetime = Field(..., description="创建时间")
    message: str = Field("文档上传成功", description="响应消息")


class DocumentProcessResponse(BaseModel):
    """文档处理响应"""
    doc_id: str = Field(..., description="文档ID")
    task_id: Optional[str] = Field(None, description="任务ID")
    status: str = Field(..., description="处理状态")
    message: str = Field(..., description="响应消息")
    started_at: datetime = Field(..., description="开始时间")


class DocumentListResponse(PaginatedResponse[DocumentInfo]):
    """文档列表响应"""
    pass


class DocumentDeleteResponse(BaseModel):
    """文档删除响应"""
    doc_id: str = Field(..., description="文档ID")
    overall_success: bool = Field(..., description="总体删除是否成功")
    message: str = Field(..., description="响应消息")
    
    # 删除详情
    postgresql_deleted: bool = Field(..., description="PostgreSQL删除是否成功")
    minio_deleted: bool = Field(..., description="MinIO删除是否成功")
    neo4j_deleted: bool = Field(..., description="Neo4j删除是否成功")
    milvus_deleted: bool = Field(..., description="Milvus删除是否成功")
    redis_deleted: bool = Field(..., description="Redis删除是否成功")
    
    # 删除的系统列表
    deleted_from: List[str] = Field(..., description="成功删除数据的系统列表")
    failed_systems: List[str] = Field(..., description="删除失败的系统列表")


class ComprehensiveDeleteResponse(BaseModel):
    """综合删除响应"""
    doc_id: str = Field(..., description="文档ID")
    overall_success: bool = Field(..., description="总体删除是否成功")
    
    # 各个存储系统的结果
    postgresql: Dict[str, Any] = Field(..., description="PostgreSQL删除结果")
    minio: Dict[str, Any] = Field(..., description="MinIO删除结果")
    neo4j: Dict[str, Any] = Field(..., description="Neo4j删除结果")
    milvus: Dict[str, Any] = Field(..., description="Milvus删除结果")
    redis: Dict[str, Any] = Field(..., description="Redis删除结果")
    
    # 汇总信息
    summary: Dict[str, Any] = Field(..., description="删除汇总信息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="删除时间")


class DocumentStatsResponse(BaseModel):
    """文档统计响应"""
    total_documents: int = Field(..., description="文档总数")
    processing_count: int = Field(..., description="处理中的文档数量")
    storage_size_bytes: int = Field(..., description="文档总大小(字节)")
    avg_file_size_bytes: Optional[float] = Field(None, description="文档平均大小(字节)")
