"""
检索相关响应数据模型
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from src.api.schemas.models.base import BaseResponse, PaginatedResponse


class RetrievalQueryResponse(BaseResponse):
    """检索查询响应"""
    response: str = Field(..., description="检索结果")
    mode: str = Field(..., description="使用的检索模式")
    processing_time: float = Field(..., description="处理时间（秒）")
    context_info: Optional[Dict[str, Any]] = Field(default=None, description="上下文信息")
    sources: Optional[List[str]] = Field(default=None, description="来源信息")


class RetrievalStreamChunk(BaseModel):
    """流式响应数据块"""
    content: Optional[str] = Field(default=None, description="内容片段")
    delta: Optional[str] = Field(default=None, description="增量内容")
    error: Optional[str] = Field(default=None, description="错误信息")
    finished: bool = Field(default=False, description="是否完成")


class RetrievalHistoryItem(BaseModel):
    """检索历史项"""
    id: str = Field(..., description="记录ID")
    role: str = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    timestamp: datetime = Field(..., description="时间戳")
    mode: Optional[str] = Field(default=None, description="检索模式")
    processing_time: Optional[float] = Field(default=None, description="处理时间")
    is_error: bool = Field(default=False, description="是否为错误消息")
    mermaid_rendered: bool = Field(default=False, description="是否包含Mermaid图表")


class RetrievalHistoryResponse(BaseResponse):
    """检索历史响应"""
    data: List[RetrievalHistoryItem] = Field(..., description="历史记录列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")


class RetrievalHistorySaveResponse(BaseResponse):
    """检索历史保存响应"""
    saved_count: int = Field(..., description="保存的记录数量")


class RetrievalHistoryClearResponse(BaseResponse):
    """检索历史清除响应"""
    cleared_count: int = Field(..., description="清除的记录数量")


class RetrievalStatsResponse(BaseResponse):
    """检索统计响应"""
    total_queries: int = Field(..., description="总查询数")
    total_sessions: int = Field(..., description="总会话数")
    avg_processing_time: float = Field(..., description="平均处理时间")
    mode_distribution: Dict[str, int] = Field(..., description="模式分布统计")
    recent_activity: List[Dict[str, Any]] = Field(..., description="最近活动")
