"""
API 响应模型

定义所有 API 接口的输出响应模型
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, TypeVar, Generic

from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """通用响应基类"""
    success: bool = Field(True, description="操作是否成功")
    message: str = Field("操作成功", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间戳")


class ErrorResponse(BaseResponse):
    """错误响应"""
    success: bool = Field(False, description="操作失败")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_detail: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class QueryResponse(BaseModel):
    """查询响应"""
    success: bool
    task_id: Optional[str] = None
    result: Optional[str] = None
    error: Optional[str] = None
    query: str
    mode: str


class DocumentUploadResponse(BaseResponse):
    """文档上传响应"""
    doc_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    upload_url: Optional[str] = Field(None, description="上传URL")


class DocumentProcessResponse(BaseResponse):
    """文档处理响应"""
    task_id: str = Field(..., description="任务ID")
    doc_id: str = Field(..., description="文档ID")
    status: str = Field(..., description="处理状态")
    estimated_time: Optional[int] = Field(None, description="预估处理时间(秒)")


class QueryResponse(BaseResponse):
    """查询响应"""
    query: str = Field(..., description="查询内容")
    results: List[Dict[str, Any]] = Field(..., description="查询结果")
    total_count: int = Field(..., description="结果总数")
    processing_time: float = Field(..., description="处理时间(秒)")


class GraphQueryResponse(BaseResponse):
    """图查询响应"""
    query: str = Field(..., description="图查询语句")
    nodes: List[Dict[str, Any]] = Field(..., description="节点列表")
    edges: List[Dict[str, Any]] = Field(..., description="边列表")
    total_nodes: int = Field(..., description="节点总数")
    total_edges: int = Field(..., description="边总数")


class TaskResponse(BaseResponse):
    """任务响应"""
    task_id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型")
    status: str = Field(..., description="任务状态")
    progress: float = Field(0.0, description="任务进度(0-1)")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class PipelineResponse(BaseResponse):
    """流水线响应"""
    pipeline_id: str = Field(..., description="流水线ID")
    name: str = Field(..., description="流水线名称")
    status: str = Field(..., description="流水线状态")
    tasks: List[TaskResponse] = Field(..., description="任务列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class PaginationInfo(BaseModel):
    """分页信息"""
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total: int = Field(..., description="总记录数")
    total_pages: int = Field(..., description="总页数")


# 泛型类型变量
T = TypeVar('T')


class PaginatedResponse(BaseResponse, Generic[T]):
    """分页响应"""
    records: List[T] = Field(..., description="数据列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


class DocumentInfo(BaseModel):
    """文档信息"""
    doc_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    status: str = Field(..., description="处理状态")
    chunk_count: int = Field(0, description="分块数量")
    entity_count: int = Field(0, description="实体数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class ChunkInfo(BaseModel):
    """分块信息"""
    chunk_id: str = Field(..., description="分块ID")
    doc_id: str = Field(..., description="文档ID")
    content: str = Field(..., description="分块内容")
    token_count: int = Field(..., description="Token数量")
    order_index: int = Field(..., description="顺序索引")
    created_at: datetime = Field(..., description="创建时间")


class EntityInfo(BaseModel):
    """实体信息"""
    entity_id: str = Field(..., description="实体ID")
    entity_name: str = Field(..., description="实体名称")
    entity_type: str = Field(..., description="实体类型")
    description: Optional[str] = Field(None, description="实体描述")
    source_chunks: List[str] = Field(..., description="来源分块ID列表")


class RelationshipInfo(BaseModel):
    """关系信息"""
    relation_id: str = Field(..., description="关系ID")
    source_entity: str = Field(..., description="源实体")
    target_entity: str = Field(..., description="目标实体")
    relation_type: str = Field(..., description="关系类型")
    description: Optional[str] = Field(None, description="关系描述")
    weight: float = Field(1.0, description="关系权重")


class HealthResponse(BaseResponse):
    """健康检查响应"""
    version: str = Field(..., description="系统版本")
    uptime: float = Field(..., description="运行时间(秒)")
    status: Dict[str, str] = Field(..., description="各组件状态")


class StatsResponse(BaseResponse):
    """统计信息响应"""
    documents: int = Field(..., description="文档总数")
    chunks: int = Field(..., description="分块总数")
    entities: int = Field(..., description="实体总数")
    relationships: int = Field(..., description="关系总数")
    tasks: Dict[str, int] = Field(..., description="任务统计")
    storage_usage: Dict[str, Any] = Field(..., description="存储使用情况")


class ManagerStatsResponse(BaseResponse):
    """管理器统计响应"""
    is_initialized: bool = Field(..., description="是否已初始化")
    active_chains: int = Field(..., description="活跃任务链数量")
    chain_details: Dict[str, Any] = Field(default_factory=dict, description="任务链详情")
    scheduler_stats: Dict[str, Any] = Field(default_factory=dict, description="调度器统计")
    pipeline_stats: Dict[str, Any] = Field(default_factory=dict, description="流水线统计")
