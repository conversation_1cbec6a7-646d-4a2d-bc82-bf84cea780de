"""
图查询响应相关的数据模式
"""

from typing import List, Dict, Optional, Any

from pydantic import BaseModel, Field

from .responses import BaseResponse, PaginatedResponse, PaginationInfo
from ..models.graphs import GraphNode, GraphEdge, GraphPath, SubGraph, GraphStats


# 响应模型
class NodeQueryResponse(BaseResponse):
    """节点查询响应"""
    data: Optional[GraphNode] = Field(None, description="节点数据")
    degree: Optional[int] = Field(None, description="节点度数")


class NeighborsQueryResponse(BaseResponse):
    """邻居查询响应"""
    data: List[GraphNode] = Field(default_factory=list, description="邻居节点列表")
    total: int = Field(0, description="邻居总数")
    center_node_id: str = Field(..., description="中心节点ID")
    depth: int = Field(..., description="查询深度")


class EdgeQueryResponse(BaseResponse):
    """边查询响应"""
    data: Optional[GraphEdge] = Field(None, description="边数据")


class PathQueryResponse(BaseResponse):
    """路径查询响应"""
    data: List[GraphPath] = Field(default_factory=list, description="路径列表")
    total: int = Field(0, description="路径总数")
    source_id: str = Field(..., description="源节点ID")
    target_id: str = Field(..., description="目标节点ID")


class TraversalQueryResponse(BaseResponse):
    """图遍历查询响应"""
    data: List[GraphNode] = Field(default_factory=list, description="遍历节点列表")
    edges: List[GraphEdge] = Field(default_factory=list, description="遍历边列表")
    total_nodes: int = Field(0, description="遍历节点总数")
    total_edges: int = Field(0, description="遍历边总数")
    start_node_id: str = Field(..., description="起始节点ID")
    max_depth_reached: int = Field(0, description="实际达到的最大深度")


class SubGraphQueryResponse(BaseResponse):
    """子图查询响应"""
    data: SubGraph = Field(..., description="子图数据")
    center_node_ids: List[str] = Field(..., description="中心节点ID列表")
    radius: int = Field(..., description="子图半径")


class GraphStatsResponse(BaseResponse):
    """图统计响应"""
    data: GraphStats = Field(..., description="图统计数据")


class GraphSearchResponse(BaseResponse):
    """图搜索响应"""
    nodes: List[GraphNode] = Field(default_factory=list, description="匹配的节点")
    edges: List[GraphEdge] = Field(default_factory=list, description="匹配的边")
    total: int = Field(0, description="匹配总数")
    query: str = Field(..., description="搜索查询")
    search_type: str = Field(..., description="搜索类型")


class NodeEdgesResponse(BaseResponse):
    """节点边响应"""
    node_id: str = Field(..., description="节点ID")
    incoming_edges: List[GraphEdge] = Field(default_factory=list, description="入边列表")
    outgoing_edges: List[GraphEdge] = Field(default_factory=list, description="出边列表")
    total_incoming: int = Field(0, description="入边总数")
    total_outgoing: int = Field(0, description="出边总数")


class NodeListResponse(BaseResponse):
    """节点分页查询响应"""
    records: List[GraphNode] = Field(default_factory=list, description="节点列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


class EdgeListResponse(BaseResponse):
    """边分页查询响应"""
    records: List[GraphEdge] = Field(default_factory=list, description="边列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


# 自定义节点和边模型，用于符合用户指定的响应格式
class GraphQueryNode(BaseModel):
    """图查询节点模型 - 符合用户指定格式"""
    id: str = Field(..., description="节点ID")
    labels: List[str] = Field(default_factory=list, description="节点标签列表")
    properties: Dict[str, Any] = Field(default_factory=dict, description="节点属性")


class GraphQueryEdge(BaseModel):
    """图查询边模型 - 符合用户指定格式"""
    id: str = Field(..., description="边ID")
    type: str = Field(..., description="边类型")
    source: str = Field(..., description="源节点ID")
    target: str = Field(..., description="目标节点ID")
    properties: Dict[str, Any] = Field(default_factory=dict, description="边属性")


class GraphQueryResponse(BaseResponse):
    """图查询响应 - 同时返回节点和边"""
    nodes: List[GraphQueryNode] = Field(default_factory=list, description="节点列表")
    edges: List[GraphQueryEdge] = Field(default_factory=list, description="边列表")
    is_truncated: bool = Field(False, description="是否被截断")
