"""
Chunk 响应相关的 API 模式定义
"""

from typing import List, Dict, Any

from pydantic import BaseModel, Field

from ..models.base import PaginationInfo
from ..models.chunks import ChunkInfo, ChunkSearchResult


class ChunkListResponse(BaseModel):
    """Chunk列表响应"""
    records: List[ChunkInfo] = Field(..., description="Chunk列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


class ChunkSearchResponse(BaseModel):
    """Chunk搜索响应"""
    chunks: List[ChunkSearchResult] = Field(..., description="搜索结果")
    total: int = Field(..., description="总数")
    query: str = Field(..., description="搜索查询")
    search_type: str = Field(..., description="搜索类型")


class ChunkStatsResponse(BaseModel):
    """Chunk统计响应"""
    total_chunks: int = Field(..., description="总Chunk数")
    total_documents: int = Field(..., description="包含Chunk的文档数")
    avg_chunk_size: float = Field(..., description="平均Chunk大小")
    avg_chunks_per_document: float = Field(..., description="每个文档平均Chunk数")
    document_chunk_counts: Dict[str, int] = Field(..., description="每个文档的Chunk数量")
