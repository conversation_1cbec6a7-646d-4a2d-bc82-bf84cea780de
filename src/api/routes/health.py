"""
健康检查和基础路由模块

提供系统健康状态检查和基础API信息：
- ✅ API基础信息查询
- 🏥 健康状态检查
- ⏱️ 系统运行时间统计
- 🌐 CORS预检请求处理
"""

import time
from typing import Dict, Any

from fastapi import APIRouter
from fastapi.responses import JSONResponse

from src.api.schemas.response import HealthResponse

# 创建路由器
router = APIRouter()

# 应用启动时间记录
start_time = time.time()


@router.get("/", response_model=Dict[str, Any])
async def get_api_info() -> Dict[str, Any]:
    """
    🚀 获取API基础信息

    返回API的名称、版本、描述和主要端点信息
    """
    return {
        "name": "MixRAG API",
        "version": "1.0.0",
        "description": "🧠 混合检索增强生成系统API - 高性能知识图谱RAG解决方案",
        "features": [
            "📄 智能文档处理",
            "🔍 多模式检索 (local/global/mix)",
            "🕸️ 知识图谱构建",
            "⚡ 异步任务处理",
            "💾 多存储后端支持"
        ],
        "endpoints": {
            "health": "/api/v1/health",
            "pipelines": "/api/v1/pipelines",
            "documents": "/api/v1/upload",
            "chunks": "/api/v1/chunks",
            "graphs": "/api/v1/graphs",
            "admin": "/api/v1/stats"
        },
        "docs": "/docs"
    }


@router.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """
    🏥 系统健康检查

    返回系统运行状态、版本信息和运行时间
    """
    uptime_seconds = time.time() - start_time

    return HealthResponse(
        success=True,
        message="系统运行正常",
        version="1.0.0",
        uptime=uptime_seconds,
        status={
            "api": "healthy",
            "database": "connected",
            "services": "running"
        }
    )


@router.options("/health")
async def health_check_options() -> JSONResponse:
    """
    健康检查CORS预检请求处理

    处理浏览器发送的OPTIONS预检请求
    """
    return JSONResponse(
        status_code=200,
        content={"message": "CORS preflight"},
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, OPTIONS",
            "Access-Control-Allow-Headers": "*",
        }
    )
