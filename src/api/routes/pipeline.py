"""
Pipeline 管理 API 路由 - 接口层

📤 职责：
- 参数接收和验证
- 调用流水线服务
- 返回标准化响应

三层架构：Route → Service → RAG
"""

from typing import Optional

from fastapi import APIRouter, HTTPException, Query

from src.api.service.pipeline_service import pipeline_service

# 创建路由器
router = APIRouter()


@router.get("/executor/status")
async def get_executor_status(
):
    """获取 Pipeline 执行器状态 - 接口层"""
    result = await pipeline_service.get_executor_status()
    return result


@router.get("/executor/health")
async def health_check(
):
    """Pipeline 执行器健康检查 - 接口层"""
    result = await pipeline_service.health_check()
    return result


@router.get("/list")
async def list_pipelines(
    status: Optional[str] = Query(None, description="流水线状态过滤"),
    created_by: Optional[str] = Query(None, description="创建者过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
):
    """获取流水线列表 - 接口层"""
    result = await pipeline_service.list_pipelines(
        status=status,
        created_by=created_by,
        page=page,
        page_size=page_size
    )
    return result

@router.get("/{pipeline_id}")
async def get_pipeline_status(
    pipeline_id: str,
):
    """获取流水线详细状态 - 接口层"""
    result = await pipeline_service.get_pipeline_status(pipeline_id)
    
    if not result["success"]:
        if "流水线不存在" in result["error"]:
            raise HTTPException(status_code=404, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result


@router.post("/{pipeline_id}/cancel")
async def cancel_pipeline(
    pipeline_id: str,
):
    """取消流水线执行 - 接口层"""
    result = await pipeline_service.cancel_pipeline(pipeline_id)
    
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["error"])
    
    return result


@router.get("/statistics/overview")
async def get_pipeline_statistics(
):
    """获取流水线统计信息 - 接口层"""
    result = await pipeline_service.get_pipeline_statistics()
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result


@router.get("/{pipeline_id}/tasks")
async def get_pipeline_tasks(
    pipeline_id: str,
):
    """获取流水线中的任务列表 - 接口层"""
    result = await pipeline_service.get_pipeline_tasks(pipeline_id)
    
    if not result["success"]:
        if "流水线不存在" in result["error"]:
            raise HTTPException(status_code=404, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result


@router.post("/{pipeline_id}/restart")
async def restart_pipeline(
    pipeline_id: str,
):
    """重启流水线（将失败的流水线重新设置为待执行状态） - 接口层"""
    result = await pipeline_service.restart_pipeline(pipeline_id)
    
    if not result["success"]:
        if "流水线不存在" in result["error"]:
            raise HTTPException(status_code=404, detail=result["error"])
        elif "流水线状态不支持重启" in result["message"]:
            raise HTTPException(status_code=400, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result


@router.post("/{pipeline_id}/resume")
async def resume_pipeline(
    pipeline_id: str,
):
    """恢复流水线（从取消状态恢复，只重置未完成的任务） - 接口层"""
    result = await pipeline_service.resume_pipeline(pipeline_id)
    
    if not result["success"]:
        if "流水线不存在" in result["error"]:
            raise HTTPException(status_code=404, detail=result["error"])
        elif "流水线状态不支持恢复" in result["message"]:
            raise HTTPException(status_code=400, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result


@router.post("/executor/refresh")
async def refresh_executor(
):
    """刷新执行器状态（强制检查待执行的流水线） - 接口层"""
    result = await pipeline_service.refresh_executor()
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result


@router.get("/{pipeline_id}/logs")
async def get_pipeline_logs(
    pipeline_id: str,
):
    """获取流水线执行日志 - 接口层"""
    result = await pipeline_service.get_pipeline_logs(pipeline_id)
    
    if not result["success"]:
        if "流水线不存在" in result["error"]:
            raise HTTPException(status_code=404, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result


@router.post("/cleanup/completed")
async def cleanup_completed_pipelines(
    keep_days: int = Query(7, ge=1, le=365, description="保留天数"),
):
    """清理已完成的流水线 - 接口层"""
    result = await pipeline_service.cleanup_completed_pipelines(keep_days)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result
