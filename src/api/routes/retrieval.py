"""
检索路由 - 接口层

📤 职责：
- 参数接收和验证
- 调用检索服务
- 返回标准化响应
- 处理流式响应

三层架构：Route → Service → RAG
"""

import json
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from typing import Optional

from src.api.schemas.request.retrieval import (
    RetrievalQueryRequest,
    RetrievalStreamRequest,
    RetrievalHistoryRequest,
    RetrievalHistoryQueryRequest
)
from src.api.schemas.response.retrieval import (
    RetrievalQueryResponse,
    RetrievalHistoryResponse,
    RetrievalHistorySaveResponse,
    RetrievalHistoryClearResponse,
    RetrievalStatsResponse
)
from src.api.service.retrieval_service import retrieval_service

# 创建路由器
router = APIRouter()


@router.post("/query", response_model=RetrievalQueryResponse)
async def query_knowledge(
    request: RetrievalQueryRequest,
):
    """知识检索查询 - 接口层"""
    return await retrieval_service.query_knowledge(request)


@router.post("/query/stream", dependencies=[])
async def query_knowledge_stream(
    request: RetrievalStreamRequest,
):
    """流式知识检索查询 - 接口层"""
    
    async def generate_stream():
        """生成流式响应"""
        try:
            async for chunk in retrieval_service.query_knowledge_stream(request):
                if chunk.error:
                    # 使用JSON序列化确保格式正确
                    error_data = {"error": chunk.error}
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                elif chunk.content:
                    # 使用JSON序列化确保格式正确
                    content_data = {"content": chunk.content}
                    yield f"data: {json.dumps(content_data, ensure_ascii=False)}\n\n"
                elif chunk.delta:
                    # 使用JSON序列化确保格式正确
                    delta_data = {"delta": chunk.delta}
                    yield f"data: {json.dumps(delta_data, ensure_ascii=False)}\n\n"

                if chunk.finished:
                    yield "data: {\"finished\": true}\n\n"
                    break
        except Exception as e:
            error_data = {"error": f"流式查询失败: {str(e)}"}
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            yield "data: {\"finished\": true}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type",
        }
    )


@router.post("/retrieval/history", response_model=RetrievalHistorySaveResponse)
async def save_retrieval_history(
    request: RetrievalHistoryRequest,
):
    """保存检索历史 - 接口层"""
    return await retrieval_service.save_history(request)


@router.get("/retrieval/history", response_model=RetrievalHistoryResponse)
async def get_retrieval_history(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
):
    """获取检索历史 - 接口层"""
    request = RetrievalHistoryQueryRequest(
        page=page,
        page_size=page_size,
        start_date=start_date,
        end_date=end_date
    )
    return await retrieval_service.get_history(request)


@router.delete("/retrieval/history", response_model=RetrievalHistoryClearResponse)
async def clear_retrieval_history():
    """清除检索历史 - 接口层"""
    return await retrieval_service.clear_history()


@router.get("/retrieval/stats", response_model=RetrievalStatsResponse)
async def get_retrieval_stats():
    """获取检索统计信息 - 接口层"""
    try:
        # 这里可以实现统计信息获取逻辑
        # 暂时返回模拟数据
        return RetrievalStatsResponse(
            success=True,
            total_queries=0,
            total_sessions=0,
            avg_processing_time=0.0,
            mode_distribution={},
            recent_activity=[],
            message="统计信息获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


# 兼容性路由 - 支持前端现有的API调用
@router.post("/query", response_model=RetrievalQueryResponse, include_in_schema=False)
async def query_knowledge_compat(
    request: RetrievalQueryRequest,
):
    """知识检索查询 - 兼容性接口"""
    return await retrieval_service.query_knowledge(request)


@router.post("/query/stream", include_in_schema=False)
async def query_knowledge_stream_compat(
    request: RetrievalStreamRequest,
):
    """流式知识检索查询 - 兼容性接口"""
    
    async def generate_stream():
        """生成流式响应"""
        try:
            async for chunk in retrieval_service.query_knowledge_stream(request):
                if chunk.error:
                    error_data = {"error": chunk.error}
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                elif chunk.content:
                    content_data = {"content": chunk.content}
                    yield f"data: {json.dumps(content_data, ensure_ascii=False)}\n\n"
                elif chunk.delta:
                    delta_data = {"delta": chunk.delta}
                    yield f"data: {json.dumps(delta_data, ensure_ascii=False)}\n\n"

                if chunk.finished:
                    yield "data: {\"finished\": true}\n\n"
                    break
        except Exception as e:
            error_data = {"error": f"流式查询失败: {str(e)}"}
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
