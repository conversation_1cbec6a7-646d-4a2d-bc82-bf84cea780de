"""
图查询API路由 - 接口层

📤 职责：
- 参数接收和验证
- 调用图服务
- 返回标准化响应

三层架构：Route → Service → RAG
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from src.api.schemas.request.graphs import (
    NeighborsQueryRequest,
    EdgeQueryRequest,
    PathQueryRequest,
    TraversalQueryRequest,
    SubGraphQueryRequest,
    GraphSearchRequest,
    NodeListRequest,
    EdgeListRequest,
    GraphQueryRequest,
)
from src.api.schemas.response.graphs import (
    NodeQueryResponse,
    NeighborsQueryResponse,
    EdgeQueryResponse,
    PathQueryResponse,
    TraversalQueryResponse,
    SubGraphQueryResponse,
    GraphStatsResponse,
    GraphSearchResponse,
    NodeEdgesResponse,
    NodeListResponse,
    EdgeListResponse,
)
from src.api.service.graph_service import graph_service

# 创建路由器
router = APIRouter()


@router.get("/node/{node_id}", response_model=NodeQueryResponse)
async def get_node(
    node_id: str,

):
    """获取节点信息 - 接口层"""
    result = await graph_service.get_node(node_id)
    
    if not result.success:
        if "不存在" in result.message:
            raise HTTPException(status_code=404, detail=result.message)
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.get("/node/{node_id}/edges", response_model=NodeEdgesResponse)
async def get_node_edges(
    node_id: str,

):
    """获取节点的所有边 - 接口层"""
    result = await graph_service.get_node_edges(node_id)
    
    if not result.success:
        if "不存在" in result.message:
            raise HTTPException(status_code=404, detail=result.message)
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.post("/node/neighbors", response_model=NeighborsQueryResponse)
async def get_node_neighbors(
    request: NeighborsQueryRequest,

):
    """获取节点的邻居 - 接口层"""
    result = await graph_service.get_node_neighbors(request)
    
    if not result.success:
        if "不存在" in result.message:
            raise HTTPException(status_code=404, detail=result.message)
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.post("/edge", response_model=EdgeQueryResponse)
async def get_edge(
    request: EdgeQueryRequest,

):
    """获取边信息 - 接口层"""
    result = await graph_service.get_edge(request)
    
    if not result.success:
        if "不存在" in result.message:
            raise HTTPException(status_code=404, detail=result.message)
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.post("/path", response_model=PathQueryResponse)
async def find_paths(
    request: PathQueryRequest,

):
    """查找两个节点之间的路径 - 接口层"""
    result = await graph_service.find_paths(request)
    
    if not result.success:
        if "不存在" in result.message:
            raise HTTPException(status_code=404, detail=result.message)
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.post("/traversal", response_model=TraversalQueryResponse)
async def graph_traversal(
    request: TraversalQueryRequest,

):
    """图遍历 - 接口层"""
    result = await graph_service.graph_traversal(request)
    
    if not result.success:
        if "不存在" in result.message:
            raise HTTPException(status_code=404, detail=result.message)
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.post("/subgraph", response_model=SubGraphQueryResponse)
async def get_subgraph(
    request: SubGraphQueryRequest,

):
    """获取子图 - 接口层"""
    result = await graph_service.get_subgraph(request)
    
    if not result.success:
        if "不存在" in result.message:
            raise HTTPException(status_code=404, detail=result.message)
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.get("/stats", response_model=GraphStatsResponse)
async def get_graph_stats(

):
    """获取图统计信息 - 接口层"""
    result = await graph_service.get_graph_stats()
    
    if not result.success:
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.post("/search", response_model=GraphSearchResponse)
async def search_graph(
    request: GraphSearchRequest,

):
    """图搜索 - 接口层"""
    result = await graph_service.search_graph(request)
    
    if not result.success:
        raise HTTPException(status_code=500, detail=result.message)
    
    return result


@router.get("/health")
async def health_check(

):
    """图存储健康检查 - 接口层"""
    result = await graph_service.health_check()

    if result["status"] == "unhealthy":
        return JSONResponse(
            status_code=503,
            content=result
        )

    return result


@router.post("/nodes/list", response_model=NodeListResponse)
async def get_nodes_list(
    request: NodeListRequest,

):
    """分页查询节点列表 - 接口层"""
    result = await graph_service.get_nodes_list(request)

    if not result.success:
        raise HTTPException(status_code=500, detail=result.message)

    # 返回符合统一响应包装器要求的格式
    response_data = {
        "records": result.records,
        "pagination": {
            "page": result.pagination.page,
            "page_size": result.pagination.page_size,
            "total": result.pagination.total,
            "total_pages": result.pagination.total_pages
        }
    }

    # 返回统一格式，让包装器识别并直接返回
    return {
        "success": result.success,
        "data": response_data,
        "message": result.message
    }


@router.post("/edges/list", response_model=EdgeListResponse)
async def get_edges_list(
    request: EdgeListRequest,

):
    """分页查询边列表 - 接口层"""
    result = await graph_service.get_edges_list(request)

    if not result.success:
        raise HTTPException(status_code=500, detail=result.message)

    # 返回符合统一响应包装器要求的格式
    response_data = {
        "records": result.records,
        "pagination": {
            "page": result.pagination.page,
            "page_size": result.pagination.page_size,
            "total": result.pagination.total,
            "total_pages": result.pagination.total_pages
        }
    }

    # 返回统一格式，让包装器识别并直接返回
    return {
        "success": result.success,
        "data": response_data,
        "message": result.message
    }


@router.get("/graphs")
async def query_graph(
    label: str = None,
    max_depth: int = 2,
    max_nodes: int = 100
):
    """图查询接口 - 同时返回节点和边"""
    # 处理label参数，如果是"*"则设为None
    if label == "*":
        label = None

    # 创建请求对象
    request = GraphQueryRequest(
        label=label,
        max_depth=max_depth,
        max_nodes=max_nodes
    )

    # 调用服务层
    result = await graph_service.query_graph(request)

    if not result.success:
        raise HTTPException(status_code=500, detail=result.message)

    # 直接返回符合用户指定格式的数据
    return {
        "nodes": [
            {
                "id": node.id,
                "labels": node.labels,
                "properties": node.properties
            }
            for node in result.nodes
        ],
        "edges": [
            {
                "id": edge.id,
                "type": edge.type,
                "source": edge.source,
                "target": edge.target,
                "properties": edge.properties
            }
            for edge in result.edges
        ],
        "is_truncated": result.is_truncated
    }


@router.get("/labels")
async def get_all_labels():
    """获取所有标签列表 - 用于label参数的可选值"""
    result = await graph_service.get_all_labels()

    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["message"])

    return result
