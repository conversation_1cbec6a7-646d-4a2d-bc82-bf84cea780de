"""
知识库管理路由模块 - 接口层

📤 职责：
- 参数接收和验证
- 调用业务层服务
- 返回标准化响应

三层架构：Route → Service → RAG
"""

from typing import Optional

from fastapi import APIRouter, Query, Path, HTTPException

from src.api.schemas.models.knowledge_bases import (
    CreateKnowledgeBaseRequest,
    UpdateKnowledgeBaseRequest,
    KnowledgeBaseListRequest,
    KnowledgeBaseSearchRequest
)
from src.api.schemas.response.knowledge_bases import (
    KnowledgeBaseCreateResponse,
    KnowledgeBaseUpdateResponse,
    KnowledgeBaseDetailResponse,
    KnowledgeBaseDeleteResponse,
    KnowledgeBaseListResponse,
    KnowledgeBaseStatsResponse,
    KnowledgeBaseSearchResponse
)
from src.api.service.knowledge_base_service import knowledge_base_service

# 创建路由器
router = APIRouter()


@router.post("", response_model=KnowledgeBaseCreateResponse)
async def create_knowledge_base(
    request: CreateKnowledgeBaseRequest
):
    """创建知识库 - 接口层"""
    return await knowledge_base_service.create_knowledge_base(request)


@router.get("/list", response_model=KnowledgeBaseListResponse)
async def list_knowledge_bases(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("create_time", description="排序字段"),
    sort_order: str = Query("desc", description="排序顺序", regex="^(asc|desc)$")
):
    """获取知识库列表 - 接口层"""
    request = KnowledgeBaseListRequest(
        page=page,
        page_size=page_size,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )
    return await knowledge_base_service.list_knowledge_bases(request)


@router.get("/stats", response_model=KnowledgeBaseStatsResponse)
async def get_knowledge_base_stats():
    """获取知识库统计信息 - 接口层"""
    return await knowledge_base_service.get_knowledge_base_stats()


@router.get("/search", response_model=KnowledgeBaseSearchResponse)
async def search_knowledge_bases(
    query: str = Query(..., description="搜索关键词", min_length=1),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """搜索知识库 - 接口层"""
    request = KnowledgeBaseSearchRequest(
        query=query,
        page=page,
        page_size=page_size
    )
    return await knowledge_base_service.search_knowledge_bases(request)


@router.get("/{kb_id}", response_model=KnowledgeBaseDetailResponse)
async def get_knowledge_base_detail(
    kb_id: str = Path(..., description="知识库ID")
):
    """获取知识库详情 - 接口层"""
    return await knowledge_base_service.get_knowledge_base_detail(kb_id)


@router.put("/{kb_id}", response_model=KnowledgeBaseUpdateResponse)
async def update_knowledge_base(
    kb_id: str = Path(..., description="知识库ID"),
    request: UpdateKnowledgeBaseRequest = ...
):
    """更新知识库 - 接口层"""
    return await knowledge_base_service.update_knowledge_base(kb_id, request)


@router.delete("/{kb_id}", response_model=KnowledgeBaseDeleteResponse)
async def delete_knowledge_base(
    kb_id: str = Path(..., description="知识库ID")
):
    """删除知识库 - 接口层"""
    return await knowledge_base_service.delete_knowledge_base(kb_id)
