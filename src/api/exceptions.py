"""
异常处理模块

统一的异常处理器，用于处理FastAPI应用中的各种异常
"""

import json
import traceback
from datetime import datetime, timezone
from typing import Any

from fastapi import HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from src.api.schemas import ErrorResponse


class DateTimeEncoder(json.JSONEncoder):
    """
    自定义JSON编码器
    
    用于处理datetime对象的序列化，将datetime对象转换为ISO格式字符串
    """

    def default(self, obj: Any) -> Any:
        """重写default方法以处理datetime对象"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


async def http_exception_handler(_: Request, exc: HTTPException) -> JSONResponse:
    """
    ⚠️ HTTP异常处理器
    
    处理FastAPI的HTTPException，返回标准化的错误响应
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail
        }
    )


async def validation_exception_handler(_: Request, exc: RequestValidationError) -> JSONResponse:
    """
    📝 请求验证异常处理器
    
    处理Pydantic模型验证失败的情况，返回详细的验证错误信息
    """
    return JSONResponse(
        status_code=422,
        content={
            "detail": f"请求参数验证失败: {str(exc)}"
        }
    )


async def general_exception_handler(_: Request, exc: Exception) -> JSONResponse:
    """
    🚨 通用异常处理器
    
    处理所有未被其他处理器捕获的异常，避免应用崩溃
    """
    # 记录异常详情用于调试
    traceback.print_exc()

    return JSONResponse(
        status_code=500,
        content={
            "detail": f"服务器内部错误: {str(exc)}"
        }
    )


def setup_exception_handlers(app):
    """
    设置应用的异常处理器
    """
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler) 