"""
Pipeline 服务 - 简化版本

只保留流水线调度功能，移除复杂的监控逻辑
流水线调度器会自动更新任务状态，无需额外监控
"""

from datetime import datetime
from typing import Optional

from src.rag.pipeline.models import PipelineStatus
from src.rag.pipeline.models.enums import TaskStatus
from src.rag.infrastructure.db.pipeline_operations import pipeline_operations
from src.rag.infrastructure.db.client import postgre_sql_client
from src.rag.pipeline.pipeline_executor import pipeline_executor
from src.rag.pipeline.pipeline_manager import pipeline_manager
from src.rag.tools import logger


class PipelineService:
    """简化的Pipeline服务"""

    def __init__(self):
        self.is_running = False
        self.start_time: Optional[datetime] = None

    async def start(self):
        """启动服务"""
        if self.is_running:
            logger.warning("⚠️ [Pipeline服务] 服务已在运行中")
            return

        self.is_running = True
        self.start_time = datetime.utcnow()

        logger.info("🚀 [Pipeline服务] 启动简化版Pipeline服务")
        logger.info(f"   🕐 启动时间: {self.start_time.isoformat()}")

        # 1. 初始化数据库
        logger.info("   🔧 初始化数据库连接...")
        await pipeline_operations.initialize()
        logger.info("   ✅ 数据库连接初始化完成")

        # 2. 启动流水线调度器（自动处理状态更新）
        logger.info("   🔧 启动流水线调度器...")
        await pipeline_executor.start()
        logger.info("   ✅ 流水线调度器启动完成")

        logger.info("🎉 [Pipeline服务] 简化版Pipeline服务启动成功!")
        logger.info("   📝 注意: 流水线执行过程将自动更新状态，无需额外监控")

    async def stop(self):
        """停止服务"""
        if not self.is_running:
            return

        logger.info("🛑 [Pipeline服务] 停止简化版Pipeline服务")

        # 1. 停止流水线调度器
        logger.info("   ⏹️ 停止流水线调度器...")
        await pipeline_executor.stop()
        logger.info("   ✅ 流水线调度器已停止")

        # 2. 关闭数据库连接
        logger.info("   ⏹️ 关闭数据库连接...")
        await pipeline_operations.close()
        logger.info("   ✅ 数据库连接已关闭")

    async def get_service_status(self):
        """获取服务状态"""
        scheduler_stats = await pipeline_executor.get_execution_stats()

        return {
            "service_running": self.is_running,
            "service_start_time": self.start_time.isoformat() if self.start_time else None,
            "scheduler_status": scheduler_stats,
            "database_connected": postgre_sql_client.pool is not None,
            "service_type": "simplified",
            "description": "自动状态更新，无需额外监控"
        }

    async def health_check(self):
        """健康检查"""
        # 检查数据库连接
        if not postgre_sql_client.pool:
            return {"healthy": False, "error": "数据库未连接"}

        # 检查流水线调度器状态
        if not pipeline_executor.is_running:
            return {"healthy": False, "error": "流水线调度器未运行"}

        return {
            "healthy": True,
            "status": "运行正常",
            "service_type": "simplified",
            "uptime_seconds": (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
        }

    async def list_pipelines(
        self,
        status: Optional[str] = None,
        created_by: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ):
        """获取流水线列表"""

        # 构建过滤条件
        filters = {}
        if status:
            filters['status'] = status
        if created_by:
            filters['created_by'] = created_by

        # 转换状态字符串为枚举
        status_enum = None
        if status:
            status_enum = PipelineStatus(status)

        # 获取流水线列表
        pipelines = await pipeline_operations.get_pipelines(
            status=status_enum,
            created_by=created_by,
            page=page,
            page_size=page_size
        )

        # 计算总数
        total = await pipeline_operations.count_pipelines(filters)

        # 格式化响应
        pipeline_list = []
        for pipeline in pipelines:
            # 获取任务统计
            tasks = await pipeline_operations.get_pipeline_tasks(pipeline.id)
            total_tasks = len(tasks)
            completed_tasks = len([t for t in tasks if t.status == TaskStatus.COMPLETED])
            failed_tasks = len([t for t in tasks if t.status == TaskStatus.FAILED])

            pipeline_data = {
                "pipeline_id": pipeline.id,
                "name": pipeline.name,
                "status": pipeline.status.value,
                "description": pipeline.description,
                "doc_id": pipeline.doc_id,
                "created_by": pipeline.created_by,
                "created_at": pipeline.created_at.isoformat() if pipeline.created_at else None,
                "started_at": pipeline.started_at.isoformat() if pipeline.started_at else None,
                "completed_at": pipeline.completed_at.isoformat() if pipeline.completed_at else None,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "failed_tasks": failed_tasks,
                "progress": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            }
            pipeline_list.append(pipeline_data)

        return {
            "success": True,
            "data": {
                "records": pipeline_list,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "total_pages": (total + page_size - 1) // page_size
                }
            },
            "message": "获取流水线列表成功"
        }

    async def get_pipeline_status(self, pipeline_id: str):
        """获取流水线状态"""
        status = await pipeline_manager.get_pipeline_status(pipeline_id)
        if not status:
            return {
                "success": False,
                "error": f"流水线不存在: {pipeline_id}",
                "message": "流水线不存在"
            }

        return {
            "success": True,
            "data": status,
            "message": "获取流水线状态成功"
        }

    async def cancel_pipeline(self, pipeline_id: str):
        success = await pipeline_manager.cancel_pipeline(pipeline_id)
        if success:
            return {
                "success": True,
                "data": {"pipeline_id": pipeline_id},
                "message": "流水线取消成功"
            }
        else:
            return {
                "success": False,
                "error": "流水线取消失败",
                "message": "流水线取消失败"
            }

    async def get_pipeline_tasks(self, pipeline_id: str):
        tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)

        # 转换PipelineTask对象为字典格式
        task_list = []
        for task in tasks:
            task_dict = {
                "id": task.id,
                "pipeline_id": task.pipeline_id,
                "task_type": task.task_type.value,
                "status": task.status.value,
                "parameters": task.parameters,
                "error_message": task.error_message,
                "retry_count": task.retry_count,
                "max_retries": task.max_retries,
                "step_order": task.step_order,
                "dependencies": task.dependencies,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None
            }
            task_list.append(task_dict)

        return {
            "success": True,
            "data": {
                "pipeline_id": pipeline_id,
                "tasks": task_list
            },
            "message": "获取流水线任务成功"
        }

    async def restart_pipeline(self, pipeline_id: str):
        """重启流水线"""
        success = await pipeline_manager.retry_failed_pipeline(pipeline_id)
        if success:
            return {
                "success": True,
                "data": {"pipeline_id": pipeline_id},
                "message": "流水线重启成功"
            }
        else:
            return {
                "success": False,
                "error": "流水线状态不支持重启",
                "message": "流水线状态不支持重启"
            }

    async def get_pipeline_statistics(self):
        """获取流水线统计信息"""
        stats = await pipeline_operations.get_statistics()

        # 构造前端期望的数据结构
        pipeline_stats = stats.get("pipelines", {})
        return {
            "success": True,
            "data": {
                "total_pipelines": pipeline_stats.get("total", 0),
                "completed_pipelines": pipeline_stats.get("completed", 0),
                "failed_pipelines": pipeline_stats.get("failed", 0),
                "pending_pipelines": pipeline_stats.get("pending", 0),
                "running_pipelines": pipeline_stats.get("running", 0),
                # 保留原始统计信息
                "raw_stats": stats
            },
            "message": "获取统计信息成功"
        }

    async def cleanup_completed_pipelines(self, keep_days: int = 7):
        """清理已完成的流水线"""
        # 这里可以实现清理逻辑
        # 暂时返回成功响应
        return {
            "success": True,
            "data": {
                "keep_days": keep_days,
                "cleaned_count": 0
            },
            "message": f"清理{keep_days}天前的已完成流水线成功"
        }

    async def get_executor_status(self):
        """获取执行器状态"""
        stats = await pipeline_executor.get_execution_stats()

        # 构造前端期望的数据结构
        return {
            "success": True,
            "data": {
                "service_running": pipeline_executor.is_running,
                "executor_status": {
                    "is_running": stats.get("is_running", False),
                    "current_status": {
                        "running_pipelines": stats.get("running_pipelines", 0),
                        "processed_pipelines": stats.get("completed_pipelines", 0) + stats.get("failed_pipelines", 0)
                    }
                },
                "database_connected": True,  # 假设数据库已连接，可以根据实际情况检查
                "scheduler_running": pipeline_executor.is_running,
                "configuration": {
                    "interval": stats.get("check_interval", 5),
                    "threads": 4,  # 可以从配置中获取
                    "max_concurrent": stats.get("max_concurrent_pipelines", 10)
                },
                "stats": stats,  # 保留原始统计信息
                "service_type": "simplified"
            },
            "message": "获取执行器状态成功"
        }

    async def refresh_executor(self):
        # 触发执行器立即检查待执行的流水线
        # 这里可以调用执行器的刷新方法
        logger.info("🔄 手动刷新执行器状态")
        # 获取当前执行器状态
        stats = await pipeline_executor.get_execution_stats()
        return {
            "success": True,
            "data": {
                "message": "执行器刷新完成",
                "executor_running": pipeline_executor.is_running,
                "stats": stats,
                "refresh_time": datetime.now().isoformat()
            },
            "message": "执行器刷新成功"
        }

    async def get_pipeline_logs(self, pipeline_id: str):
        """获取流水线执行日志"""
        # 检查流水线是否存在
        pipeline = await pipeline_operations.get_pipeline(pipeline_id)
        if not pipeline:
            return {
                "success": False,
                "error": f"流水线不存在: {pipeline_id}",
                "message": "流水线不存在"
            }
        # 获取流水线任务列表作为日志信息
        tasks = await pipeline_operations.get_pipeline_tasks(pipeline_id)
        # 构建日志信息
        logs = []
        # 流水线创建日志
        logs.append({
            "timestamp": pipeline.created_at.isoformat() if pipeline.created_at else None,
            "level": "INFO",
            "message": f"流水线创建: {pipeline.name}",
            "status": "created",
            "step_order": -1,  # 流水线级别事件使用负数
            "task_type": "pipeline",
            "id": pipeline_id,
            "details": {
                "pipeline_id": pipeline_id,
                "created_by": pipeline.created_by,
                "description": pipeline.description
            }
        })
        # 流水线开始执行日志
        if pipeline.started_at:
            logs.append({
                "timestamp": pipeline.started_at.isoformat(),
                "level": "INFO",
                "message": "流水线开始执行",
                "status": "running",
                "step_order": -1,
                "task_type": "pipeline",
                "id": pipeline_id,
                "details": {"pipeline_id": pipeline_id}
            })

        # 添加任务日志
        for task in tasks:
            # 任务开始日志
            if task.started_at:
                logs.append({
                    "timestamp": task.started_at.isoformat() if isinstance(task.started_at, datetime) else task.started_at,
                    "level": "INFO",
                    "message": f"任务开始: {task.task_type.value}",
                    "status": "running",
                    "step_order": task.step_order,
                    "task_type": task.task_type.value,
                    "id": task.id,
                    "details": {
                        "task_id": task.id,
                        "task_type": task.task_type.value,
                        "parameters": task.parameters
                    }
                })

            # 任务完成/失败日志
            if task.completed_at:
                level = "ERROR" if task.status.value == "failed" else "INFO"
                message = f"任务{'失败' if task.status.value == 'failed' else '完成'}: {task.task_type.value}"
                logs.append({
                    "timestamp": task.completed_at.isoformat() if isinstance(task.completed_at, datetime) else task.completed_at,
                    "level": level,
                    "message": message,
                    "status": task.status.value,
                    "step_order": task.step_order,
                    "task_type": task.task_type.value,
                    "id": task.id,
                    "details": {
                        "task_id": task.id,
                        "task_type": task.task_type.value,
                        "status": task.status.value,
                        "error_message": task.error_message,
                        "output_data": task.output_data
                    }
                })

        # 流水线完成/失败日志
        if pipeline.completed_at:
            level = "ERROR" if pipeline.status.value == "failed" else "INFO"
            message = f"流水线{'失败' if pipeline.status.value == 'failed' else '完成'}"
            logs.append({
                "timestamp": pipeline.completed_at.isoformat(),
                "level": level,
                "message": message,
                "status": pipeline.status.value,
                "step_order": -1,
                "task_type": "pipeline",
                "id": pipeline_id,
                "details": {
                    "pipeline_id": pipeline_id,
                    "final_status": pipeline.status.value,
                    "error_message": pipeline.error_message
                }
            })

        # 按时间排序
        logs.sort(key=lambda x: x["timestamp"] or "")
        return {
            "success": True,
            "data": {
                "pipeline_id": pipeline_id,
                "logs": logs,
                "total_logs": len(logs)
            },
            "message": "获取流水线日志成功"
        }


# 保持向后兼容
pipeline_service = PipelineService()
