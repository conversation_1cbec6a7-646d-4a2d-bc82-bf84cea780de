"""
知识库服务 - 业务逻辑层

负责处理所有知识库相关的业务逻辑，调用数据库操作层的基础功能
"""

from typing import Dict, Any, Optional

from src.api.schemas.models.base import create_paginated_response
from src.api.schemas.models.knowledge_bases import (
    KnowledgeBaseInfo,
    KnowledgeBaseDetail,
    KnowledgeBaseStats,
    CreateKnowledgeBaseRequest,
    UpdateKnowledgeBaseRequest,
    KnowledgeBaseListRequest,
    KnowledgeBaseSearchRequest
)
from src.api.schemas.response.knowledge_bases import (
    KnowledgeBaseCreateResponse,
    KnowledgeBaseUpdateResponse,
    KnowledgeBaseDetailResponse,
    KnowledgeBaseDeleteResponse,
    KnowledgeBaseListResponse,
    KnowledgeBaseStatsResponse,
    KnowledgeBaseSearchResponse
)
from src.rag.infrastructure.db.knowledge_base_operations import knowledge_base_operations
from src.rag.tools import logger


class KnowledgeBaseService:
    """知识库服务 - 业务逻辑层"""

    def __init__(self):
        """初始化知识库服务"""
        self._initialized = False

    async def create_knowledge_base(
            self, 
            request: CreateKnowledgeBaseRequest
    ) -> KnowledgeBaseCreateResponse:
        """创建知识库"""
        try:
            # 检查知识库名称是否已存在
            existing_kbs = await knowledge_base_operations.list_knowledge_bases(
                page=1, 
                page_size=1, 
                search_query=request.kb_name
            )
            
            # 简单的名称重复检查（可以优化为精确匹配）
            for kb in existing_kbs.get("records", []):
                if kb["kb_name"] == request.kb_name:
                    return KnowledgeBaseCreateResponse(
                        success=False,
                        message=f"知识库名称 '{request.kb_name}' 已存在",
                        data=None
                    )

            # 创建知识库
            kb_data = await knowledge_base_operations.create_knowledge_base(
                kb_name=request.kb_name,
                kb_des=request.kb_des
            )

            # 转换为响应模型
            kb_info = KnowledgeBaseInfo(**kb_data)

            return KnowledgeBaseCreateResponse(
                success=True,
                message="知识库创建成功",
                data=kb_info
            )

        except Exception as e:
            logger.error(f"创建知识库失败: {str(e)}")
            return KnowledgeBaseCreateResponse(
                success=False,
                message=f"创建知识库失败: {str(e)}",
                data=None
            )

    async def update_knowledge_base(
            self, 
            kb_id: str, 
            request: UpdateKnowledgeBaseRequest
    ) -> KnowledgeBaseUpdateResponse:
        """更新知识库"""
        try:
            # 检查知识库是否存在
            existing_kb = await knowledge_base_operations.get_knowledge_base_by_id(kb_id)
            if not existing_kb:
                return KnowledgeBaseUpdateResponse(
                    success=False,
                    message=f"知识库 {kb_id} 不存在",
                    data=None
                )

            # 如果要更新名称，检查新名称是否已被其他知识库使用
            if request.kb_name and request.kb_name != existing_kb["kb_name"]:
                existing_kbs = await knowledge_base_operations.list_knowledge_bases(
                    page=1, 
                    page_size=1, 
                    search_query=request.kb_name
                )
                
                for kb in existing_kbs.get("records", []):
                    if kb["kb_name"] == request.kb_name and kb["id"] != kb_id:
                        return KnowledgeBaseUpdateResponse(
                            success=False,
                            message=f"知识库名称 '{request.kb_name}' 已存在",
                            data=None
                        )

            # 更新知识库
            kb_data = await knowledge_base_operations.update_knowledge_base(
                kb_id=kb_id,
                kb_name=request.kb_name,
                kb_des=request.kb_des
            )

            if not kb_data:
                return KnowledgeBaseUpdateResponse(
                    success=False,
                    message="更新知识库失败",
                    data=None
                )

            # 转换为响应模型
            kb_info = KnowledgeBaseInfo(**kb_data)

            return KnowledgeBaseUpdateResponse(
                success=True,
                message="知识库更新成功",
                data=kb_info
            )

        except Exception as e:
            logger.error(f"更新知识库失败: {str(e)}")
            return KnowledgeBaseUpdateResponse(
                success=False,
                message=f"更新知识库失败: {str(e)}",
                data=None
            )

    async def get_knowledge_base_detail(self, kb_id: str) -> KnowledgeBaseDetailResponse:
        """获取知识库详情"""
        try:
            kb_data = await knowledge_base_operations.get_knowledge_base_by_id(kb_id)
            
            if not kb_data:
                return KnowledgeBaseDetailResponse(
                    success=False,
                    message=f"知识库 {kb_id} 不存在",
                    data=None
                )

            # 转换为响应模型
            kb_detail = KnowledgeBaseDetail(**kb_data)

            return KnowledgeBaseDetailResponse(
                success=True,
                message="获取知识库详情成功",
                data=kb_detail
            )

        except Exception as e:
            logger.error(f"获取知识库详情失败: {str(e)}")
            return KnowledgeBaseDetailResponse(
                success=False,
                message=f"获取知识库详情失败: {str(e)}",
                data=None
            )

    async def delete_knowledge_base(self, kb_id: str) -> KnowledgeBaseDeleteResponse:
        """删除知识库"""
        try:
            # 检查知识库是否存在
            existing_kb = await knowledge_base_operations.get_knowledge_base_by_id(kb_id)
            if not existing_kb:
                return KnowledgeBaseDeleteResponse(
                    success=False,
                    message=f"知识库 {kb_id} 不存在",
                    data={"deleted": False}
                )

            # 删除知识库
            success = await knowledge_base_operations.delete_knowledge_base(kb_id)

            if success:
                return KnowledgeBaseDeleteResponse(
                    success=True,
                    message="知识库删除成功",
                    data={"deleted": True}
                )
            else:
                return KnowledgeBaseDeleteResponse(
                    success=False,
                    message="删除知识库失败",
                    data={"deleted": False}
                )

        except Exception as e:
            logger.error(f"删除知识库失败: {str(e)}")
            return KnowledgeBaseDeleteResponse(
                success=False,
                message=f"删除知识库失败: {str(e)}",
                data={"deleted": False}
            )

    async def list_knowledge_bases(
            self, 
            request: KnowledgeBaseListRequest
    ) -> KnowledgeBaseListResponse:
        """获取知识库列表"""
        try:
            result = await knowledge_base_operations.list_knowledge_bases(
                page=request.page,
                page_size=request.page_size,
                search_query=request.search,
                sort_by=request.sort_by,
                sort_order=request.sort_order
            )

            # 转换为响应模型
            kb_list = [KnowledgeBaseInfo(**kb) for kb in result["records"]]
            
            paginated_data = create_paginated_response(
                records=kb_list,
                page=result["page"],
                page_size=result["page_size"],
                total=result["total"]
            )

            return KnowledgeBaseListResponse(
                success=True,
                message="获取知识库列表成功",
                data=paginated_data
            )

        except Exception as e:
            logger.error(f"获取知识库列表失败: {str(e)}")
            return KnowledgeBaseListResponse(
                success=False,
                message=f"获取知识库列表失败: {str(e)}",
                data=create_paginated_response([], 1, 20, 0)
            )

    async def get_knowledge_base_stats(self) -> KnowledgeBaseStatsResponse:
        """获取知识库统计信息"""
        try:
            stats_data = await knowledge_base_operations.get_knowledge_base_stats()
            
            # 转换为响应模型
            stats = KnowledgeBaseStats(**stats_data)

            return KnowledgeBaseStatsResponse(
                success=True,
                message="获取统计信息成功",
                data=stats
            )

        except Exception as e:
            logger.error(f"获取知识库统计信息失败: {str(e)}")
            return KnowledgeBaseStatsResponse(
                success=False,
                message=f"获取统计信息失败: {str(e)}",
                data=KnowledgeBaseStats()
            )

    async def search_knowledge_bases(
            self, 
            request: KnowledgeBaseSearchRequest
    ) -> KnowledgeBaseSearchResponse:
        """搜索知识库"""
        try:
            result = await knowledge_base_operations.search_knowledge_bases(
                query=request.query,
                page=request.page,
                page_size=request.page_size
            )

            # 转换为响应模型
            kb_list = [KnowledgeBaseInfo(**kb) for kb in result["records"]]
            
            paginated_data = create_paginated_response(
                records=kb_list,
                page=result["page"],
                page_size=result["page_size"],
                total=result["total"]
            )

            return KnowledgeBaseSearchResponse(
                success=True,
                message="搜索知识库成功",
                data=paginated_data
            )

        except Exception as e:
            logger.error(f"搜索知识库失败: {str(e)}")
            return KnowledgeBaseSearchResponse(
                success=False,
                message=f"搜索知识库失败: {str(e)}",
                data=create_paginated_response([], 1, 20, 0)
            )


# 创建全局实例
knowledge_base_service = KnowledgeBaseService()
