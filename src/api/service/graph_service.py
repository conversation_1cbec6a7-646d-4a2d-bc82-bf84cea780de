"""
图数据服务层
包含所有图查询相关的业务逻辑
"""

import logging
from collections import deque
from typing import Dict, Any

logger = logging.getLogger(__name__)

from src.api.schemas.models.graphs import (
    GraphNode,
    GraphEdge,
    GraphPath,
    SubGraph,
    GraphStats,
    TraversalType
)
from src.api.schemas.request.graphs import (
    NeighborsQueryRequest,
    EdgeQueryRequest,
    PathQueryRequest,
    TraversalQueryRequest,
    SubGraphQueryRequest,
    GraphSearchRequest,
    NodeListRequest,
    EdgeListRequest,
    GraphQueryRequest,
)
from src.api.schemas.response.graphs import (
    NodeQueryResponse,
    NeighborsQueryResponse,
    EdgeQueryResponse,
    PathQueryResponse,
    TraversalQueryResponse,
    SubGraphQueryResponse,
    GraphStatsResponse,
    GraphSearchResponse,
    NodeEdgesResponse,
    NodeListResponse,
    EdgeListResponse,
    GraphQueryResponse,
    GraphQueryNode,
    GraphQueryEdge,
)
from src.rag.infrastructure import neo4j_graph_operations


class GraphService:
    """图数据服务层"""
    
    def _convert_node_data_to_graph_node(self, node_id: str, node_data: Dict[str, Any]) -> GraphNode:
        """将存储的节点数据转换为GraphNode"""
        if not node_data:
            return None

        return GraphNode(
            entity_id=node_id,
            entity_type=node_data.get("entity_type"),
            description=node_data.get("description"),
            properties={k: v for k, v in node_data.items() if k not in {"entity_id", "entity_type", "description"}}
        )

    def _convert_edge_data_to_graph_edge(self, source_id: str, target_id: str, edge_data: Dict[str, Any]) -> GraphEdge:
        """将存储的边数据转换为GraphEdge"""
        if not edge_data:
            return None

        return GraphEdge(
            source_id=source_id,
            target_id=target_id,
            weight=float(edge_data.get("weight", 1.0)),
            description=edge_data.get("description"),
            keywords=edge_data.get("keywords"),
            properties={k: v for k, v in edge_data.items()
                        if k not in {"source_id", "target_id", "weight", "description", "keywords"}}
        )
    
    async def get_node(self, node_id: str) -> NodeQueryResponse:
        """获取节点信息"""

        # 检查节点是否存在
        if not await neo4j_graph_operations.has_node(node_id):
            return NodeQueryResponse(
                success=False,
                message=f"节点 {node_id} 不存在"
            )

        # 获取节点数据
        node_data = await neo4j_graph_operations.get_node(node_id)
        node_degree = await neo4j_graph_operations.node_degree(node_id)

        graph_node = self._convert_node_data_to_graph_node(node_id, node_data)

        return NodeQueryResponse(
            success=True,
            message="获取节点成功",
            data=graph_node,
            degree=node_degree
        )

    async def get_node_edges(self, node_id: str) -> NodeEdgesResponse:
        """获取节点的所有边"""

        # 检查节点是否存在
        if not await neo4j_graph_operations.has_node(node_id):
            return NodeEdgesResponse(
                success=False,
                message=f"节点 {node_id} 不存在",
                node_id=node_id
            )

        # 获取节点的边
        node_edges = await neo4j_graph_operations.get_node_edges(node_id)

        if not node_edges:
            return NodeEdgesResponse(
                success=True,
                message="节点没有边",
                data=[],
                total=0,
                node_id=node_id
            )

        # 转换为GraphEdge对象
        edges = []
        for edge_data in node_edges:
            source_id = node_id  # 当前节点作为源节点
            target_id = edge_data.get("target_id")
            if target_id:
                graph_edge = self._convert_edge_data_to_graph_edge(source_id, target_id, edge_data)
                if graph_edge:
                    edges.append(graph_edge)

        return NodeEdgesResponse(
            success=True,
            message=f"获取节点边成功，共 {len(edges)} 条边",
            data=edges,
            total=len(edges),
            node_id=node_id
        )

    async def get_node_neighbors(self, request: NeighborsQueryRequest) -> NeighborsQueryResponse:
        """获取节点的邻居"""

        # 检查节点是否存在
        if not await neo4j_graph_operations.has_node(request.node_id):
            return NeighborsQueryResponse(
                success=False,
                message=f"节点 {request.node_id} 不存在",
                center_node_id=request.node_id,
                depth=request.max_depth
            )

        # 使用BFS获取指定深度内的所有邻居
        visited = set()
        neighbors = []
        queue = deque([(request.node_id, 0)])  # (node_id, current_depth)
        visited.add(request.node_id)

        while queue and len(neighbors) < (request.limit or 1000):
            current_node, depth = queue.popleft()

            if depth >= request.max_depth:
                continue

            # 获取当前节点的直接邻居
            node_edges = await neo4j_graph_operations.get_node_edges(current_node)
            if node_edges:
                for edge_data in node_edges:
                    neighbor_id = edge_data.get("target_id")

                    if neighbor_id and neighbor_id not in visited:
                        visited.add(neighbor_id)

                        # 获取邻居节点数据
                        neighbor_data = await neo4j_graph_operations.get_node(neighbor_id)
                        if neighbor_data:
                            neighbor_node = self._convert_node_data_to_graph_node(neighbor_id, neighbor_data)
                            if neighbor_node:
                                neighbors.append(neighbor_node)

                        # 添加到队列以继续搜索
                        if depth + 1 < request.max_depth:
                            queue.append((neighbor_id, depth + 1))

        return NeighborsQueryResponse(
            success=True,
            message=f"获取邻居成功，共 {len(neighbors)} 个邻居",
            data=neighbors,
            total=len(neighbors),
            center_node_id=request.node_id,
            depth=request.max_depth
        )

    async def get_edge(self, request: EdgeQueryRequest) -> EdgeQueryResponse:
        """获取边信息"""

        # 检查边是否存在
        if not await neo4j_graph_operations.has_edge(request.source_id, request.target_id):
            return EdgeQueryResponse(
                success=False,
                message=f"边 {request.source_id} -> {request.target_id} 不存在"
            )

        # 获取边数据
        edge_data = await neo4j_graph_operations.get_edge(request.source_id, request.target_id)
        graph_edge = self._convert_edge_data_to_graph_edge(request.source_id, request.target_id, edge_data)

        return EdgeQueryResponse(
            success=True,
            message="获取边成功",
            data=graph_edge
        )

    async def find_paths(self, request: PathQueryRequest) -> PathQueryResponse:
        """查找两个节点之间的路径"""

        # 检查源节点和目标节点是否存在
        if not await neo4j_graph_operations.has_node(request.source_id):
            return PathQueryResponse(
                success=False,
                message=f"源节点 {request.source_id} 不存在",
                source_id=request.source_id,
                target_id=request.target_id
            )

        if not await neo4j_graph_operations.has_node(request.target_id):
            return PathQueryResponse(
                success=False,
                message=f"目标节点 {request.target_id} 不存在",
                source_id=request.source_id,
                target_id=request.target_id
            )

        # 使用BFS查找路径
        paths = []
        queue = deque([(request.source_id, [request.source_id])])  # (current_node, path)
        visited_paths = set()

        while queue and len(paths) < request.limit:
            current_node, path = queue.popleft()

            if len(path) > request.max_depth + 1:
                continue

            if current_node == request.target_id:
                # 找到目标，构造路径对象
                if len(path) > 1:
                    path_edges = []
                    total_weight = 0.0

                    for i in range(len(path) - 1):
                        source = path[i]
                        target = path[i + 1]
                        edge_data = await neo4j_graph_operations.get_edge(source, target)
                        if edge_data:
                            graph_edge = self._convert_edge_data_to_graph_edge(source, target, edge_data)
                            if graph_edge:
                                path_edges.append(graph_edge)
                                total_weight += graph_edge.weight or 1.0

                    graph_path = GraphPath(
                        nodes=path,
                        edges=path_edges,
                        total_weight=total_weight,
                        length=len(path) - 1
                    )
                    paths.append(graph_path)
                continue

            # 获取当前节点的邻居
            node_edges = await neo4j_graph_operations.get_node_edges(current_node)
            if node_edges:
                for edge_data in node_edges:
                    next_node = edge_data.get("target_id")

                    # 避免循环
                    if next_node and next_node not in path:
                        new_path = path + [next_node]
                        path_key = tuple(new_path)

                        if path_key not in visited_paths:
                            visited_paths.add(path_key)
                            queue.append((next_node, new_path))

        return PathQueryResponse(
            success=True,
            message=f"找到 {len(paths)} 条路径",
            data=paths,
            total=len(paths),
            source_id=request.source_id,
            target_id=request.target_id
        )

    async def graph_traversal(self, request: TraversalQueryRequest) -> TraversalQueryResponse:
        """图遍历"""

        # 检查起始节点是否存在
        if not await neo4j_graph_operations.has_node(request.start_node_id):
            return TraversalQueryResponse(
                success=False,
                message=f"起始节点 {request.start_node_id} 不存在",
                start_node_id=request.start_node_id,
                traversal_type=request.traversal_type,
                depth=request.max_depth
            )

        visited = set()
        result_nodes = []

        if request.traversal_type == TraversalType.BFS:
            # 广度优先遍历
            queue = deque([(request.start_node_id, 0)])  # (node_id, depth)
            visited.add(request.start_node_id)

            while queue and len(result_nodes) < request.limit:
                current_node, depth = queue.popleft()

                # 获取节点数据并添加到结果
                node_data = await neo4j_graph_operations.get_node(current_node)
                if node_data:
                    graph_node = self._convert_node_data_to_graph_node(current_node, node_data)
                    if graph_node:
                        result_nodes.append(graph_node)

                if depth < request.max_depth:
                    # 获取邻居节点
                    node_edges = await neo4j_graph_operations.get_node_edges(current_node)
                    if node_edges:
                        for edge_data in node_edges:
                            neighbor_id = edge_data.get("target_id")

                            if neighbor_id and neighbor_id not in visited:
                                visited.add(neighbor_id)
                                queue.append((neighbor_id, depth + 1))

        else:  # DFS
            # 深度优先遍历
            stack = [(request.start_node_id, 0)]  # (node_id, depth)
            visited.add(request.start_node_id)

            while stack and len(result_nodes) < request.limit:
                current_node, depth = stack.pop()

                # 获取节点数据并添加到结果
                node_data = await neo4j_graph_operations.get_node(current_node)
                if node_data:
                    graph_node = self._convert_node_data_to_graph_node(current_node, node_data)
                    if graph_node:
                        result_nodes.append(graph_node)

                if depth < request.max_depth:
                    # 获取邻居节点
                    node_edges = await neo4j_graph_operations.get_node_edges(current_node)
                    if node_edges:
                        for edge_data in node_edges:
                            neighbor_id = edge_data.get("target_id")
                            if neighbor_id and neighbor_id not in visited:
                                visited.add(neighbor_id)
                                stack.append((neighbor_id, depth + 1))

        return TraversalQueryResponse(
            success=True,
            message=f"{request.traversal_type.value.upper()} 遍历完成，访问了 {len(result_nodes)} 个节点",
            data=result_nodes,
            total=len(result_nodes),
            start_node_id=request.start_node_id,
            traversal_type=request.traversal_type,
            depth=request.max_depth
        )

    async def get_subgraph(self, request: SubGraphQueryRequest) -> SubGraphQueryResponse:
        """获取子图"""

        # 检查中心节点是否存在
        for node_id in request.center_node_ids:
            if not await neo4j_graph_operations.has_node(node_id):
                return SubGraphQueryResponse(
                    success=False,
                    message=f"中心节点 {node_id} 不存在",
                    center_node_ids=request.center_node_ids,
                    radius=request.radius
                )

        # 从中心节点开始扩展子图
        subgraph_nodes = set(request.center_node_ids)

        # 逐层扩展
        for radius in range(request.radius):
            current_level_nodes = list(subgraph_nodes)
            for node_id in current_level_nodes:
                node_edges = await neo4j_graph_operations.get_node_edges(node_id)
                if node_edges:
                    for edge_data in node_edges:
                        neighbor_id = edge_data.get("target_id")
                        if neighbor_id:
                            subgraph_nodes.add(neighbor_id)

        # 获取子图中的所有节点数据
        nodes = []
        for node_id in subgraph_nodes:
            node_data = await neo4j_graph_operations.get_node(node_id)
            if node_data:
                # 检查节点类型过滤
                node_type = node_data.get("entity_type")
                if request.include_node_types and node_type not in request.include_node_types:
                    continue
                if request.exclude_node_types and node_type in request.exclude_node_types:
                    continue

                graph_node = self._convert_node_data_to_graph_node(node_id, node_data)
                if graph_node:
                    nodes.append(graph_node)

        # 获取子图中的所有边数据
        edges = []
        processed_edges = set()

        for node_id in [n.entity_id for n in nodes]:
            node_edges = await neo4j_graph_operations.get_node_edges(node_id)
            if node_edges:
                for edge_data in node_edges:
                    source_id = node_id  # 当前节点作为源节点
                    target_id = edge_data.get("target_id")

                    # 确保边的两个端点都在子图中
                    if (target_id and source_id in subgraph_nodes and target_id in subgraph_nodes and
                            (source_id, target_id) not in processed_edges and
                            (target_id, source_id) not in processed_edges):

                        # edge_data 已经包含了边的信息，不需要再次查询
                        if edge_data:
                            graph_edge = self._convert_edge_data_to_graph_edge(source_id, target_id, edge_data)
                            if graph_edge:
                                edges.append(graph_edge)
                                processed_edges.add((source_id, target_id))

        subgraph = SubGraph(
            nodes=nodes,
            edges=edges,
            node_count=len(nodes),
            edge_count=len(edges)
        )

        return SubGraphQueryResponse(
            success=True,
            message=f"获取子图成功，包含 {len(nodes)} 个节点，{len(edges)} 条边",
            data=subgraph,
            center_node_ids=request.center_node_ids,
            radius=request.radius
        )

    async def get_graph_stats(self) -> GraphStatsResponse:
        """获取图统计信息"""

        # 获取所有节点和边
        all_nodes = await neo4j_graph_operations.get_all_nodes()
        all_edges = await neo4j_graph_operations.get_all_edges()

        # 计算度数统计
        degrees = []
        node_types = {}

        for node_data in all_nodes:
            # 获取节点ID
            node_id = node_data.get('id') or node_data.get('name', '')
            if not node_id:
                continue

            degree = await neo4j_graph_operations.node_degree(node_id)
            degrees.append(degree)

            # 获取节点类型（直接从已有的node_data获取）
            node_type = node_data.get("entity_type", "Unknown")
            node_types[node_type] = node_types.get(node_type, 0) + 1

        # 计算统计值
        avg_degree = sum(degrees) / len(degrees) if degrees else 0
        max_degree = max(degrees) if degrees else 0
        min_degree = min(degrees) if degrees else 0

        stats = GraphStats(
            total_nodes=len(all_nodes),
            total_edges=len(all_edges),
            avg_degree=round(avg_degree, 2),
            max_degree=max_degree,
            min_degree=min_degree,
            node_types=node_types
        )

        return GraphStatsResponse(
            success=True,
            message="获取图统计信息成功",
            data=stats
        )

    async def search_graph(self, request: GraphSearchRequest) -> GraphSearchResponse:
        """图搜索"""

        results = []

        if request.search_type == "node":
            # 搜索节点
            all_nodes = await neo4j_graph_operations.get_all_nodes()
            count = 0

            for node_data in all_nodes:
                if count >= request.limit:
                    break

                # 获取节点ID
                node_id = node_data.get('id') or node_data.get('name', '')
                if not node_id:
                    continue

                # 在节点ID中搜索
                if request.query.lower() in str(node_id).lower():
                    graph_node = self._convert_node_data_to_graph_node(node_id, node_data)
                    if graph_node:
                        results.append(graph_node)
                        count += 1
                        continue

                # 在节点属性中搜索
                searchable_text = (
                        str(node_data.get("name", "")) + " " +
                        str(node_data.get("description", "")) + " " +
                        str(node_data.get("entity_type", "")) + " " +
                        str(node_data.get("properties", {}))
                ).lower()

                if request.query.lower() in searchable_text:
                    graph_node = self._convert_node_data_to_graph_node(node_id, node_data)
                    if graph_node:
                        results.append(graph_node)
                        count += 1

        elif request.search_type == "edge":
            # 搜索边
            all_edges = await neo4j_graph_operations.get_all_edges()
            count = 0

            for edge_data in all_edges:
                if count >= request.limit:
                    break

                # 获取边的源节点和目标节点ID
                source_id = edge_data.get('source_id', '')
                target_id = edge_data.get('target_id', '')

                if not source_id or not target_id:
                    continue

                # 在边ID中搜索
                edge_text = f"{source_id} {target_id}".lower()
                if request.query.lower() in edge_text:
                    graph_edge = self._convert_edge_data_to_graph_edge(source_id, target_id, edge_data)
                    if graph_edge:
                        results.append(graph_edge)
                        count += 1
                        continue

                # 在边属性中搜索
                searchable_text = (
                        str(edge_data.get("description", "")) + " " +
                        str(edge_data.get("keywords", "")) + " " +
                        str(edge_data.get("relation_type", "")) + " " +
                        str(edge_data.get("properties", {}))
                ).lower()

                if request.query.lower() in searchable_text:
                    graph_edge = self._convert_edge_data_to_graph_edge(source_id, target_id, edge_data)
                    if graph_edge:
                        results.append(graph_edge)
                        count += 1

        return GraphSearchResponse(
            success=True,
            message=f"搜索完成，找到 {len(results)} 个结果",
            data=results,
            total=len(results),
            query=request.query,
            search_type=request.search_type
        )
    async def health_check(self) -> Dict[str, Any]:
        """图存储健康检查"""

        # 简单测试连接
        await neo4j_graph_operations.get_all_nodes()
        return {"status": "healthy", "message": "图存储连接正常"}

    async def get_nodes_list(self, request: NodeListRequest) -> NodeListResponse:
        """分页查询节点列表"""
        try:
            # 调用Neo4j存储的分页查询方法
            result = await neo4j_graph_operations.get_nodes_paginated(
                page=request.page,
                page_size=request.page_size,
                node_type=request.node_type,
                property_filters=request.property_filters,
                search_text=request.search_text,
                sort_by=request.sort_by,
                sort_order=request.sort_order
            )

            # 转换节点数据为GraphNode对象
            nodes = []
            for node_data in result["data"]:
                node_id = node_data.get("id", "")
                graph_node = self._convert_node_data_to_graph_node(node_id, node_data)
                if graph_node:
                    nodes.append(graph_node)

            # 创建分页信息
            from ..schemas.response.responses import PaginationInfo
            pagination = PaginationInfo(
                page=result["page"],
                page_size=result["page_size"],
                total=result["total"],
                total_pages=result["total_pages"]
            )

            return NodeListResponse(
                success=True,
                message=f"查询成功，共找到 {result['total']} 个节点",
                records=nodes,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"分页查询节点失败: {e}")
            # 创建空的分页信息
            from ..schemas.response.responses import PaginationInfo
            empty_pagination = PaginationInfo(
                page=request.page,
                page_size=request.page_size,
                total=0,
                total_pages=0
            )

            return NodeListResponse(
                success=False,
                message=f"查询节点失败: {str(e)}",
                records=[],
                pagination=empty_pagination
            )

    async def get_edges_list(self, request: EdgeListRequest) -> EdgeListResponse:
        """分页查询边列表"""
        try:
            # 调用Neo4j存储的分页查询方法
            result = await neo4j_graph_operations.get_edges_paginated(
                page=request.page,
                page_size=request.page_size,
                edge_type=request.edge_type,
                source_id=request.source_id,
                target_id=request.target_id,
                property_filters=request.property_filters,
                search_text=request.search_text,
                sort_by=request.sort_by,
                sort_order=request.sort_order
            )

            # 转换边数据为GraphEdge对象
            edges = []
            for edge_data in result["data"]:
                source_id = edge_data.get("source_id", "")
                target_id = edge_data.get("target_id", "")
                graph_edge = self._convert_edge_data_to_graph_edge(source_id, target_id, edge_data)
                if graph_edge:
                    edges.append(graph_edge)

            # 创建分页信息
            from ..schemas.response.responses import PaginationInfo
            pagination = PaginationInfo(
                page=result["page"],
                page_size=result["page_size"],
                total=result["total"],
                total_pages=result["total_pages"]
            )

            return EdgeListResponse(
                success=True,
                message=f"查询成功，共找到 {result['total']} 条边",
                records=edges,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"分页查询边失败: {e}")
            # 创建空的分页信息
            from ..schemas.response.responses import PaginationInfo
            empty_pagination = PaginationInfo(
                page=request.page,
                page_size=request.page_size,
                total=0,
                total_pages=0
            )

            return EdgeListResponse(
                success=False,
                message=f"查询边失败: {str(e)}",
                records=[],
                pagination=empty_pagination
            )

    async def query_graph(self, request: GraphQueryRequest) -> GraphQueryResponse:
        """图查询 - 同时返回节点和边"""
        try:
            # 调用Neo4j存储的图遍历查询方法
            result = await neo4j_graph_operations.query_graph_with_traversal(
                label=request.label,
                max_depth=request.max_depth,
                max_nodes=request.max_nodes
            )

            # 转换节点数据为GraphQueryNode对象
            nodes = []
            for node_data in result["nodes"]:
                graph_node = GraphQueryNode(
                    id=node_data["id"],
                    labels=node_data["labels"],
                    properties=node_data["properties"]
                )
                nodes.append(graph_node)

            # 转换边数据为GraphQueryEdge对象
            edges = []
            for edge_data in result["edges"]:
                graph_edge = GraphQueryEdge(
                    id=edge_data["id"],
                    type=edge_data["type"],
                    source=edge_data["source"],
                    target=edge_data["target"],
                    properties=edge_data["properties"]
                )
                edges.append(graph_edge)

            return GraphQueryResponse(
                success=True,
                message=f"查询成功，共找到 {len(nodes)} 个节点和 {len(edges)} 条边",
                nodes=nodes,
                edges=edges,
                is_truncated=result["is_truncated"]
            )

        except Exception as e:
            logger.error(f"图查询失败: {e}")
            return GraphQueryResponse(
                success=False,
                message=f"查询失败: {str(e)}",
                nodes=[],
                edges=[],
                is_truncated=False
            )

    async def get_all_labels(self) -> Dict[str, Any]:
        """获取所有标签列表"""
        try:
            # 调用Neo4j存储的获取标签方法
            labels = await neo4j_graph_operations.get_all_labels()

            return {
                "success": True,
                "data": labels,
                "message": "获取标签列表成功"
            }

        except Exception as e:
            logger.error(f"获取标签列表失败: {e}")
            return {
                "success": False,
                "data": [],
                "message": f"获取标签列表失败: {str(e)}"
            }


graph_service = GraphService()
