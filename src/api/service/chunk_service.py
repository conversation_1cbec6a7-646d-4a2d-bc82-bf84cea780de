"""
块数据服务层
包含所有块查询和搜索相关的业务逻辑
"""

from datetime import datetime
from typing import Optional, Any

from src.api.schemas import PaginatedResponse
from src.api.schemas.models.base import create_paginated_response
from src.api.schemas.request.chunks import (
    ChunkSimilaritySearchRequest,
    ChunkKeywordSearchRequest,
)
from src.api.schemas.response.chunks import (
    ChunkListResponse,
    ChunkSearchResponse,
    ChunkStatsResponse,
    ChunkInfo,
    ChunkSearchResult
)
from src.rag.infrastructure import redis_client, milvus_vector_operations
# 获取真实的embedding函数
from src.rag.tools.logger import logger


class ChunkService:
    """块数据服务层"""

    async def list_chunks(
        self,
        page: int = 1,
        page_size: int = 20,
        doc_id: Optional[str] = None
    ) -> PaginatedResponse[Any]:
        """分页查看所有chunk"""
        # 获取所有chunk数据 - 使用新的Redis set格式
        chunk_list = []

        if doc_id:
            # 如果指定了doc_id，直接从对应的Redis set获取
            redis_key = f"chunks:{doc_id}"
            chunks = await self._get_chunks_from_redis_set(redis_key)
            for chunk_data in chunks:
                chunk_info = ChunkInfo.create(chunk_data.get("chunk_id", ""), chunk_data)
                chunk_list.append(chunk_info)
        else:
            # 如果没有指定doc_id，获取所有文档的chunks
            # 获取所有以"chunks:"开头的Redis键
            chunk_keys = []
            async for key in redis_client.redis.scan_iter(match="chunks:*"):
                chunk_keys.append(key)

            # 从每个set中获取chunks
            for redis_key in chunk_keys:
                chunks = await self._get_chunks_from_redis_set(redis_key)
                for chunk_data in chunks:
                    chunk_info = ChunkInfo.create(chunk_data.get("chunk_id", ""), chunk_data)
                    chunk_list.append(chunk_info)

        # 按chunk_index排序
        chunk_list.sort(key=lambda x: (x.doc_id, x.chunk_index))

        # 分页
        total = len(chunk_list)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_chunks = chunk_list[start_idx:end_idx]

        return create_paginated_response(
            records=paginated_chunks,
            page=page,
            page_size=page_size,
            total=total
        )

    async def _get_chunks_from_redis_set(self, redis_key: str) -> list:
        """从Redis set中获取chunk数据"""
        try:
            import json
            chunk_set_data = await redis_client.redis.smembers(redis_key)

            if not chunk_set_data:
                return []

            # 解析JSON数据并转换为chunk列表
            chunks = []
            for chunk_json in chunk_set_data:
                try:
                    chunk_data = json.loads(chunk_json)
                    chunks.append(chunk_data)
                except json.JSONDecodeError as e:
                    logger.error(f"解析chunk JSON数据失败: {e}")
                    continue

            # 按order_index排序
            chunks.sort(key=lambda x: x.get("order_index", 0))
            return chunks

        except Exception as e:
            logger.error(f"从Redis set获取chunk数据失败: {e}")
            return []

    async def search_chunks_by_similarity(
        self,
        request: ChunkSimilaritySearchRequest
    ) -> ChunkSearchResponse:
        """通过相似度搜索chunk（使用Milvus向量搜索）"""

        # 创建同步wrapper来适配Milvus存储
        def sync_embedding_wrapper(text):
            """
            同步embedding wrapper，用于Milvus存储
            将异步的call_embedding函数包装为同步函数
            """

            # 将单个文本转换为列表格式
            if isinstance(text, str):
                text_list = [text]
            else:
                text_list = [str(text)]

            try:
                # 创建同步的OpenAI客户端来避免事件循环问题
                from openai import OpenAI
                from src.config import config

                model_config = config.model
                sync_client = OpenAI(
                    api_key=model_config.embedding_api_key,
                    base_url=model_config.embedding_base_url,
                    timeout=model_config.llm_timeout
                )

                # 使用同步客户端生成嵌入向量
                response = sync_client.embeddings.create(
                    model=model_config.embedding_model_name,
                    input=text_list
                )

                embeddings = [data.embedding for data in response.data]
                logger.debug(f"同步嵌入向量生成完成: {len(embeddings)}个向量, 维度: {len(embeddings[0]) if embeddings else 0}")

                # 返回第一个向量（因为输入只有一个文本）
                return embeddings[0] if embeddings and len(embeddings) > 0 else []

            except Exception as e:
                logger.error(f"Error in sync_embedding_wrapper: {e}")
                return []

        milvus_vector_operations.embedding_func = sync_embedding_wrapper

        # 执行向量搜索
        search_results = await milvus_vector_operations.query_by_text(
            query_text=request.query,
            top_k=request.top_k,
            output_fields=["id", "content", "full_doc_id", "chunk_order_index", "tokens"]
        )

        # 转换结果格式
        chunk_results = []
        for result in search_results:
            # 获取距离值
            distance = result.get("distance", 0.0)

            # 对于COSINE度量类型，Milvus返回的实际上是余弦相似度（cosine similarity）
            # 不是余弦距离！值越大表示越相似，1表示完全相同，0表示完全不相关
            # 所以distance本身就是相似度，不需要转换
            similarity = distance

            # 为了保持API一致性，我们将distance设为 1 - similarity（真正的余弦距离）
            actual_distance = 1.0 - distance

            # 获取文档ID
            result_doc_id = result.get("full_doc_id", "")

            # 应用相似度阈值过滤
            if similarity >= request.threshold:
                chunk_result = ChunkSearchResult(
                    chunk_id=result.get("id", ""),
                    doc_id=result_doc_id,
                    content=result.get("content", ""),
                    chunk_index=result.get("chunk_order_index", 0),
                    similarity=round(similarity, 4),  # 保留4位小数
                    distance=round(actual_distance, 4),  # 使用真正的余弦距离
                    created_at=datetime.now(),
                )
                chunk_results.append(chunk_result)

        return ChunkSearchResponse(
            chunks=chunk_results,
            total=len(chunk_results),
            query=request.query,
            search_type="similarity"
        )

    async def search_chunks_by_keywords(
        self,
        request: ChunkKeywordSearchRequest
    ) -> ChunkSearchResponse:
        """通过关键词搜索chunk（使用Redis文本搜索）"""
        # 获取所有chunk数据 - 使用新的Redis set格式
        all_chunks = {}

        # 获取所有以"chunks:"开头的key
        chunk_keys = await redis_client.redis.keys("chunks:*")

        for key in chunk_keys:
            # 从Redis set中获取chunk数据
            chunks_in_doc = await self._get_chunks_from_redis_set(key)
            for chunk_data in chunks_in_doc:
                chunk_id = chunk_data.get("chunk_id")
                if chunk_id:
                    all_chunks[chunk_id] = chunk_data

        # 关键词搜索（简单的文本包含搜索）
        keywords = request.keywords.lower().split()
        matching_chunks = []

        for chunk_id, chunk_data in all_chunks.items():
            # 按文档ID过滤
            if request.doc_id and chunk_data.get("full_doc_id") != request.doc_id:
                continue

            content = chunk_data.get("content", "").lower()

            # 检查是否包含所有关键词
            if all(keyword in content for keyword in keywords):
                chunk_result = ChunkSearchResult(
                    chunk_id=chunk_id,
                    doc_id=chunk_data.get("full_doc_id", chunk_data.get("doc_id", "")),
                    content=chunk_data.get("content", ""),
                    chunk_index=chunk_data.get("chunk_order_index", chunk_data.get("order_index", 0)),
                    similarity=None,
                    distance=None,
                    created_at=datetime.now(),
                )
                matching_chunks.append(chunk_result)

        # 按chunk_index排序
        matching_chunks.sort(key=lambda x: (x.doc_id, x.chunk_index))

        # 分页
        total = len(matching_chunks)
        start_idx = request.offset
        end_idx = start_idx + request.limit
        paginated_chunks = matching_chunks[start_idx:end_idx]

        return ChunkSearchResponse(
            chunks=paginated_chunks,
            total=total,
            query=request.keywords,
            search_type="keyword"
        )

    async def get_chunks_by_document(
        self,
        doc_id: str,
        page: int = 1,
        page_size: int = 50
    ) -> ChunkListResponse:
        """按文档ID查看chunk"""
        # 使用新的Redis set格式直接获取指定文档的chunks
        redis_key = f"chunks:{doc_id}"
        chunks = await self._get_chunks_from_redis_set(redis_key)

        # 转换为ChunkInfo对象
        document_chunks = []
        for chunk_data in chunks:
            chunk_info = ChunkInfo.create(chunk_data.get("chunk_id", ""), chunk_data)
            document_chunks.append(chunk_info)

        # 按chunk_index排序
        document_chunks.sort(key=lambda x: x.chunk_index)

        # 分页
        total = len(document_chunks)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_chunks = document_chunks[start_idx:end_idx]

        return create_paginated_response(
            records=paginated_chunks,
            page=page,
            page_size=page_size,
            total=total
        )

    async def get_chunks_stats(self) -> ChunkStatsResponse:
        """获取chunk统计信息"""
        # 使用新的Redis set格式获取所有chunk数据
        all_chunks_data = []
        chunk_keys = []
        async for key in redis_client.redis.scan_iter(match="chunks:*"):
            chunk_keys.append(key)

        for redis_key in chunk_keys:
            chunks = await self._get_chunks_from_redis_set(redis_key)
            all_chunks_data.extend(chunks)

        if not all_chunks_data:
            return ChunkStatsResponse(
                total_chunks=0,
                total_documents=0,
                avg_chunk_size=0,
                avg_chunks_per_document=0,
                document_chunk_counts={}
            )

        # 统计信息
        total_chunks = len(all_chunks_data)
        document_chunks = {}
        total_chunk_size = 0

        for chunk_data in all_chunks_data:
            # 确保chunk_data是字典类型
            if not isinstance(chunk_data, dict):
                logger.error(f"Chunk data is not a dictionary, type: {type(chunk_data)}")
                continue

            doc_id = chunk_data.get("doc_id", "unknown")

            # 统计每个文档的chunk数量
            if doc_id not in document_chunks:
                document_chunks[doc_id] = 0
            document_chunks[doc_id] += 1

            # 统计chunk大小
            content = chunk_data.get("content", "")
            total_chunk_size += len(content)

        total_documents = len(document_chunks)
        avg_chunk_size = total_chunk_size / total_chunks if total_chunks > 0 else 0
        avg_chunks_per_document = total_chunks / total_documents if total_documents > 0 else 0

        return ChunkStatsResponse(
            total_chunks=total_chunks,
            total_documents=total_documents,
            avg_chunk_size=avg_chunk_size,
            avg_chunks_per_document=avg_chunks_per_document,
            document_chunk_counts=document_chunks
        )

# 保持向后兼容
chunk_service = ChunkService()
