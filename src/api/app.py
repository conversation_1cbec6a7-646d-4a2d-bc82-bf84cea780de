"""
FastAPI应用创建和配置模块

负责创建FastAPI应用实例，配置所有路由、中间件和自定义功能
"""

from contextlib import asynccontextmanager
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict

# 加载环境变量
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.responses import Response

from src.api.config.swagger_config import (
    TAGS_METADATA, SERVERS, SWAGGER_UI_PARAMETERS, CUSTOM_CSS, CUSTOM_JS,
    get_security_schemes, get_common_responses, get_common_schemas
)
from src.api.exceptions import setup_exception_handlers
from src.api.middleware import setup_middleware, apply_unified_response_wrapper
from src.api.routes import router
# 启动阶段 - 初始化存储客户端
from src.rag.infrastructure import (
    postgre_sql_client, redis_client, min_io_client, neo4j_connection_manager,
    milvus_vector_operations
)
from src.rag.infrastructure.milvus import milvus_client
from src.rag.pipeline.pipeline_executor import pipeline_executor

# 加载项目根目录的.env文件
project_root = Path(__file__).parent.parent.parent
env_path = project_root / ".env"
if env_path.exists():
    load_dotenv(env_path)
    print(f"✅ 已加载环境变量: {env_path}")
else:
    print(f"⚠️  未找到.env文件: {env_path}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    🔄 应用生命周期管理

    负责应用启动和关闭时的资源管理：
    - 🚀 启动时：初始化存储客户端和Pipeline执行器服务
    - 🛑 关闭时：清理所有资源
    """
    print("🚀 MixRAG API 服务启动中...")

    try:
        print("📊 初始化PostgreSQL存储...")
        await postgre_sql_client.initialize()
        print("✅ PostgreSQL存储初始化完成")
    except Exception as e:
        print(f"❌ PostgreSQL存储初始化失败: {e}")
        # 不阻止应用启动，但记录错误

    try:
        print("🔧 初始化Redis存储...")
        await redis_client.initialize()
        print("✅ Redis存储初始化完成")
    except Exception as e:
        print(f"❌ Redis存储初始化失败: {e}")

    try:
        print("📦 初始化MinIO存储...")
        await min_io_client.initialize()
        print("✅ MinIO存储初始化完成")
    except Exception as e:
        print(f"❌ MinIO存储初始化失败: {e}")

    try:
        print("🔍 初始化Milvus向量存储...")
        await milvus_client.initialize()
        await milvus_vector_operations.initialize()
        print("✅ Milvus向量存储初始化完成")
    except Exception as e:
        print(f"❌ Milvus向量存储初始化失败: {e}")

    try:
        print("🕸️ 初始化Neo4j图存储...")
        await neo4j_connection_manager.initialize()
        print("✅ Neo4j图存储初始化完成")
    except Exception as e:
        print(f"❌ Neo4j图存储初始化失败: {e}")

    # 启动Pipeline执行器
    await pipeline_executor.start()

    print("🎉 所有服务启动成功，MixRAG API 已就绪")

    yield

    # 关闭阶段
    print("🛑 MixRAG API 服务关闭中...")

    await pipeline_executor.stop()

    # 清理存储客户端
    try:
        await postgre_sql_client.finalize()
        await milvus_client.finalize()
        await neo4j_connection_manager.finalize()
        await redis_client.finalize()
        print("✅ 存储客户端清理完成")
    except Exception as e:
        print(f"⚠️ 存储客户端清理时出错: {e}")

    print("👋 MixRAG API 服务已关闭")


def create_app() -> FastAPI:
    """
    创建并配置FastAPI应用实例
    
    Returns:
        配置好的FastAPI应用实例
    """
    # 创建FastAPI应用实例
    app = FastAPI(
        title="MixRAG API",
        description="""
        ## 🧠 MixRAG - 混合检索增强生成系统

        高性能知识图谱RAG解决方案，提供以下核心功能：

        ### 🚀 核心特性
        - 📄 **智能文档处理** - 支持多格式文档解析和分块
        - 🕸️ **知识图谱构建** - 自动实体抽取和关系发现
        - 🔍 **多模式检索** - local/global/mix检索策略
        - ⚡ **异步任务处理** - 高并发流水线执行
        - 💾 **多存储支持** - Redis/Neo4j/Milvus/PostgreSQL

        ### 📚 API模块
        - 🏥 健康检查和系统状态
        - 📤 文档上传和管理
        - 🔧 流水线任务管理
        - 📊 分块和图谱查询
        - ⚙️ 系统管理和统计
        """,
        version="1.0.0",
        terms_of_service="https://github.com/HKUDS/MixRAG/blob/main/LICENSE",
        contact={
            "name": "MixRAG Team",
            "url": "https://github.com/HKUDS/MixRAG",
            "email": "<EMAIL>"
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
        openapi_tags=TAGS_METADATA,
        servers=SERVERS,
        docs_url=None,  # 使用自定义docs
        redoc_url="/redoc",
        openapi_url="/api/v1/openapi.json",
        swagger_ui_parameters=SWAGGER_UI_PARAMETERS,
        lifespan=lifespan
    )

    # 设置中间件
    setup_middleware(app)

    # 设置异常处理器
    setup_exception_handlers(app)

    # 应用统一响应包装器到所有路由
    apply_unified_response_wrapper(router)
    
    # 注册API路由
    app.include_router(router, prefix="/api/v1")

    # 设置自定义OpenAPI
    setup_custom_openapi(app)

    # 添加自定义路由
    setup_custom_routes(app)

    return app


def setup_custom_openapi(app: FastAPI) -> None:
    """
    设置自定义OpenAPI配置
    """
    def custom_openapi():
        """自定义 OpenAPI schema"""
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
            servers=app.servers,
            tags=app.openapi_tags,
        )

        # 添加自定义信息
        openapi_schema["info"]["x-logo"] = {
            "url": "https://raw.githubusercontent.com/HKUDS/MixRAG/main/docs/logo.png"
        }

        # 添加自定义扩展信息
        openapi_schema["info"]["x-api-id"] = "mixrag-api"
        openapi_schema["info"]["x-audience"] = "developers"

        # 确保 components 存在
        if "components" not in openapi_schema:
            openapi_schema["components"] = {}

        # 添加安全定义
        openapi_schema["components"]["securitySchemes"] = get_security_schemes()

        # 添加通用响应
        openapi_schema["components"]["responses"] = get_common_responses()

        # 合并通用 schemas
        if "schemas" not in openapi_schema["components"]:
            openapi_schema["components"]["schemas"] = {}

        openapi_schema["components"]["schemas"].update(get_common_schemas())

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    # 应用自定义 OpenAPI schema
    app.openapi = custom_openapi


def setup_custom_routes(app: FastAPI) -> None:
    """
    添加自定义路由
    """
    
    # 自定义 Swagger UI 路由
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        """自定义 Swagger UI 页面"""
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title=app.title + " - API Documentation",
            oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
            swagger_ui_parameters=SWAGGER_UI_PARAMETERS,
            swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
            init_oauth={"usePkceWithAuthorizationCodeGrant": True},
        )

    # 添加自定义样式和脚本的路由
    @app.get("/swagger-custom.css", include_in_schema=False)
    async def custom_swagger_css():
        """返回自定义 CSS"""
        return Response(content=CUSTOM_CSS, media_type="text/css")

    @app.get("/swagger-custom.js", include_in_schema=False)
    async def custom_swagger_js():
        """返回自定义 JavaScript"""
        return Response(content=CUSTOM_JS, media_type="application/javascript")

    # 根路径API信息
    @app.get("/", include_in_schema=False)
    async def root() -> Dict[str, Any]:
        """
        根路径API信息

        返回API的基本信息和可用端点
        """
        return {
            "message": "欢迎使用MixRAG API",
            "docs": "/docs",
            "redoc": "/redoc",
            "api": "/api/v1",
            "version": app.version,
            "status": "running",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


# 创建全局应用实例
app = create_app() 