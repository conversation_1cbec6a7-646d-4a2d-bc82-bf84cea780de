#!/usr/bin/env python3
"""
MixRAG 系统统一启动入口

🚀 高性能混合检索增强生成系统主启动模块
"""

import argparse
import asyncio
import sys

import uvicorn

from src.api.app import app
from src.service_manager import service_manager, SignalHandler


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MixRAG 系统启动器")

    parser.add_argument("--host", default="0.0.0.0", help="服务器地址 (默认: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--reload", action="store_true", help="启用热重载 (开发模式)")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数 (默认: 1，热重载模式下固定为1)")
    parser.add_argument("--install", action="store_true", help="安装依赖包")
    parser.add_argument("--dev", action="store_true", help="开发模式 (等同于 --reload)")
    parser.add_argument("--prod", action="store_true", help="生产模式 (多进程，禁用热重载)")

    args = parser.parse_args()

    print("=" * 60)
    print("🌟 MixRAG 混合检索增强生成系统")
    print("=" * 60)

    # 处理模式设置
    if args.dev:
        args.reload = True
        args.workers = 1
        print("🔧 开发模式: 启用热重载")

    elif args.prod:
        args.reload = False
        args.workers = max(args.workers, 4)  # 生产模式至少4个进程
        print(f"🏭 生产模式: {args.workers} 个工作进程")

    if args.reload and args.workers > 1:
        print("⚠️  热重载模式下将使用单进程")
        args.workers = 1

    print(f"\n📋 启动配置:")
    print(f"   主机: {args.host}")
    print(f"   端口: {args.port}")
    print(f"   热重载: {'是' if args.reload else '否'}")
    print(f"   工作进程: {args.workers}")
    print(f"   模式: {'开发' if args.reload else ('生产' if args.workers > 1 else '标准')}")
    print("")

    # 运行异步主函数
    try:
        # 设置信号处理器
        signal_handler = SignalHandler(service_manager)
        signal_handler.setup()
        # 启动服务
        asyncio.run(service_manager.start(
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers
        ))
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"💥 启动失败: {e}")
        return 1
    finally:
        asyncio.run(service_manager.stop())
    return 0


# 兼容直接运行uvicorn的方式
if __name__ == "__main__":
    # 检查是否是通过uvicorn直接启动
    if len(sys.argv) == 1 or (len(sys.argv) == 2 and sys.argv[1].endswith("main.py")):
        # 直接启动模式（用于uvicorn调用）
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False
        )
    else:
        # 命令行参数模式
        sys.exit(main())
