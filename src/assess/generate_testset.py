#!/usr/bin/env python3
"""
RAG 评估数据生成脚本

使用 Ragas 的测试数据生成功能，基于文档自动生成评估数据集
"""
import os
import sys
import asyncio
import argparse
from pathlib import Path
from typing import List

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from langchain_core.documents import Document
from ragas.testset import TestsetGenerator
from ragas.testset.graph import KnowledgeGraph, Node, NodeType
from ragas.testset.transforms import default_transforms, apply_transforms
from ragas.testset.synthesizers import default_query_distribution

from ragas.llms import LangchainLLMWrapper
from ragas.embeddings import LangchainEmbeddingsWrapper
from langchain_openai import ChatOpenAI, OpenAIEmbeddings


def load_sample_documents() -> List[Document]:
    """加载示例文档"""
    sample_docs = [
        Document(
            page_content="""<PERSON> proposed the theory of relativity, which transformed our understanding of time, space, and gravity. His work laid the foundation for modern physics and cosmology. The theory of relativity consists of two interrelated theories: special relativity and general relativity. Special relativity applies to all physical phenomena in the absence of gravity, while general relativity explains the law of gravitation and its relation to other forces of nature. Einstein's famous equation E=mc² demonstrates the mass-energy equivalence, showing that mass and energy are interchangeable. This revolutionary concept has had profound implications for nuclear physics, cosmology, and our understanding of the universe. The theory predicted phenomena such as time dilation, length contraction, and the bending of light by gravity, all of which have been experimentally verified. <PERSON>'s work fundamentally changed how we perceive space and time, showing that they are not absolute but relative to the observer's frame of reference.""",
            metadata={"source": "physics_doc_1", "topic": "relativity"}
        ),
        Document(
            page_content="""Marie Curie was a physicist and chemist who conducted pioneering research on radioactivity and won two Nobel Prizes. She was the first woman to win a Nobel Prize and the only person to win Nobel Prizes in two different scientific fields. Born Maria Skłodowska in Warsaw, Poland, she moved to Paris to pursue her studies at the University of Paris. Together with her husband Pierre Curie, she discovered the elements polonium and radium. Her research on radioactivity, a term she coined, led to the development of X-ray machines and cancer treatments. Despite facing significant discrimination as a woman in science, she persevered and made groundbreaking contributions to physics and chemistry. Her work laid the foundation for modern atomic physics and nuclear chemistry. The Curie Institute in Paris, which she founded, continues to be a leading cancer research center. Her legacy extends beyond science, as she became a symbol of women's achievements in STEM fields and inspired generations of female scientists.""",
            metadata={"source": "physics_doc_2", "topic": "radioactivity"}
        ),
        Document(
            page_content="""Isaac Newton formulated the laws of motion and universal gravitation, laying the foundation for classical mechanics. His work 'Principia Mathematica' is considered one of the most important scientific works ever written. Newton's three laws of motion describe the relationship between forces acting on a body and its motion. The first law states that an object at rest stays at rest and an object in motion stays in motion unless acted upon by an external force. The second law establishes that the force acting on an object equals its mass times acceleration (F=ma). The third law states that for every action, there is an equal and opposite reaction. His law of universal gravitation explains that every particle attracts every other particle with a force proportional to the product of their masses and inversely proportional to the square of the distance between them. Newton also made significant contributions to optics, discovering that white light is composed of different colors, and to mathematics, developing calculus independently of Leibniz. His work dominated scientific thought for over two centuries until Einstein's theory of relativity provided a more comprehensive framework.""",
            metadata={"source": "physics_doc_3", "topic": "mechanics"}
        ),
        Document(
            page_content="""Charles Darwin introduced the theory of evolution by natural selection in his book 'On the Origin of Species'. This theory explains how species change over time through the process of natural selection. Darwin's observations during his voyage on the HMS Beagle, particularly in the Galápagos Islands, led him to develop his revolutionary theory. He noticed that species on different islands had similar but distinct characteristics, suggesting they had evolved from common ancestors. Natural selection operates on the principle that individuals with favorable traits are more likely to survive and reproduce, passing these advantageous characteristics to their offspring. Over many generations, this process leads to the evolution of species. Darwin's theory provided a unifying explanation for the diversity of life on Earth and challenged prevailing religious and scientific beliefs of his time. The theory has been supported by extensive evidence from paleontology, comparative anatomy, embryology, biogeography, and molecular biology. Modern evolutionary biology has expanded on Darwin's work, incorporating genetics and molecular mechanisms to provide a more complete understanding of how evolution occurs.""",
            metadata={"source": "biology_doc_1", "topic": "evolution"}
        ),
        Document(
            page_content="""Ada Lovelace is regarded as the first computer programmer for her work on Charles Babbage's early mechanical computer, the Analytical Engine. She wrote the first algorithm intended to be processed by a machine. Born Augusta Ada King, Countess of Lovelace, she was the daughter of the famous poet Lord Byron. Despite the limited educational opportunities for women in the 19th century, she received an excellent education in mathematics and science. Her collaboration with Charles Babbage began when she translated an article about his Analytical Engine from French to English. However, she went far beyond translation, adding extensive notes that were longer than the original article. In these notes, she described how the machine could be programmed to perform various calculations and even suggested it could compose music or create art. Her Note G contained what is now recognized as the first computer program - an algorithm for calculating Bernoulli numbers. Lovelace's vision of computing extended beyond mere calculation; she saw the potential for machines to manipulate symbols and create, not just crunch numbers. Her work was largely forgotten until the mid-20th century when her contributions to computing were rediscovered and celebrated.""",
            metadata={"source": "computer_doc_1", "topic": "programming"}
        )
    ]

    print(f"加载了 {len(sample_docs)} 个示例文档")
    return sample_docs


def load_documents_from_directory(directory: str) -> List[Document]:
    """从目录加载文档"""
    try:
        from langchain_community.document_loaders import DirectoryLoader
        
        loader = DirectoryLoader(directory, glob="**/*.md")
        docs = loader.load()
        print(f"从 {directory} 加载了 {len(docs)} 个文档")
        return docs
    except ImportError:
        print("警告: langchain_community 未安装，使用示例文档")
        return load_sample_documents()
    except Exception as e:
        print(f"从目录加载文档失败: {e}，使用示例文档")
        return load_sample_documents()


async def generate_testset_simple(docs: List[Document], testset_size: int = 10):
    """简单方式生成测试集 - 手动创建测试数据"""
    print("=== 使用简单方式生成测试集 ===")

    # 由于 LLM 服务不稳定，我们手动创建一些测试数据
    from ragas import EvaluationDataset, SingleTurnSample

    print(f"手动生成 {testset_size} 个测试样本...")

    # 基于文档内容手动创建测试样本
    samples = []

    # 样本 1: 关于爱因斯坦
    samples.append(SingleTurnSample(
        user_input="Who proposed the theory of relativity and what does E=mc² represent?",
        response="Albert Einstein proposed the theory of relativity. His famous equation E=mc² demonstrates the mass-energy equivalence, showing that mass and energy are interchangeable.",
        retrieved_contexts=[docs[0].page_content],
        reference="Albert Einstein proposed the theory of relativity, and E=mc² represents the mass-energy equivalence principle."
    ))

    # 样本 2: 关于居里夫人
    samples.append(SingleTurnSample(
        user_input="Who was the first woman to win a Nobel Prize and in which fields?",
        response="Marie Curie was the first woman to win a Nobel Prize. She won Nobel Prizes in two different scientific fields: physics and chemistry, for her pioneering research on radioactivity.",
        retrieved_contexts=[docs[1].page_content],
        reference="Marie Curie was the first woman to win a Nobel Prize, winning in both physics and chemistry."
    ))

    # 样本 3: 关于牛顿
    samples.append(SingleTurnSample(
        user_input="What are Newton's three laws of motion?",
        response="Newton's three laws of motion are: 1) An object at rest stays at rest and an object in motion stays in motion unless acted upon by an external force. 2) The force acting on an object equals its mass times acceleration (F=ma). 3) For every action, there is an equal and opposite reaction.",
        retrieved_contexts=[docs[2].page_content],
        reference="Newton's three laws describe the relationship between forces and motion, including inertia, F=ma, and action-reaction pairs."
    ))

    # 样本 4: 关于达尔文
    samples.append(SingleTurnSample(
        user_input="What is the theory of evolution by natural selection?",
        response="The theory of evolution by natural selection, introduced by Charles Darwin, explains how species change over time. It operates on the principle that individuals with favorable traits are more likely to survive and reproduce, passing these advantageous characteristics to their offspring.",
        retrieved_contexts=[docs[3].page_content],
        reference="Darwin's theory explains how species evolve through natural selection, where favorable traits increase survival and reproduction chances."
    ))

    # 样本 5: 关于洛夫莱斯
    samples.append(SingleTurnSample(
        user_input="Who is considered the first computer programmer and why?",
        response="Ada Lovelace is regarded as the first computer programmer for her work on Charles Babbage's Analytical Engine. She wrote the first algorithm intended to be processed by a machine, specifically an algorithm for calculating Bernoulli numbers in her Note G.",
        retrieved_contexts=[docs[4].page_content],
        reference="Ada Lovelace is considered the first computer programmer because she wrote the first machine algorithm for Babbage's Analytical Engine."
    ))

    # 如果需要更多样本，重复使用现有文档创建不同的问题
    if testset_size > 5:
        # 样本 6: 爱因斯坦的其他贡献
        samples.append(SingleTurnSample(
            user_input="What phenomena did Einstein's theory of relativity predict?",
            response="Einstein's theory of relativity predicted several phenomena including time dilation, length contraction, and the bending of light by gravity, all of which have been experimentally verified.",
            retrieved_contexts=[docs[0].page_content],
            reference="The theory predicted time dilation, length contraction, and gravitational lensing."
        ))

        # 样本 7: 居里夫人的发现
        samples.append(SingleTurnSample(
            user_input="What elements did Marie Curie discover?",
            response="Together with her husband Pierre Curie, Marie Curie discovered the elements polonium and radium through her research on radioactivity.",
            retrieved_contexts=[docs[1].page_content],
            reference="Marie and Pierre Curie discovered polonium and radium."
        ))

        # 样本 8: 牛顿的万有引力定律
        samples.append(SingleTurnSample(
            user_input="What is Newton's law of universal gravitation?",
            response="Newton's law of universal gravitation states that every particle attracts every other particle with a force proportional to the product of their masses and inversely proportional to the square of the distance between them.",
            retrieved_contexts=[docs[2].page_content],
            reference="Universal gravitation describes the attractive force between masses, proportional to their masses and inversely proportional to distance squared."
        ))

        # 样本 9: 达尔文的观察
        samples.append(SingleTurnSample(
            user_input="What observations led Darwin to develop his theory?",
            response="Darwin's observations during his voyage on the HMS Beagle, particularly in the Galápagos Islands, led him to develop his theory. He noticed that species on different islands had similar but distinct characteristics, suggesting they had evolved from common ancestors.",
            retrieved_contexts=[docs[3].page_content],
            reference="Darwin's observations of species variations in the Galápagos Islands led to his evolutionary theory."
        ))

        # 样本 10: 洛夫莱斯的远见
        samples.append(SingleTurnSample(
            user_input="What was unique about Ada Lovelace's vision of computing?",
            response="Lovelace's vision of computing extended beyond mere calculation; she saw the potential for machines to manipulate symbols and create, not just crunch numbers. She even suggested that machines could compose music or create art.",
            retrieved_contexts=[docs[4].page_content],
            reference="Lovelace envisioned computers as capable of symbolic manipulation and creative tasks, not just numerical calculations."
        ))

    # 限制样本数量
    samples = samples[:testset_size]

    # 创建评估数据集
    testset = EvaluationDataset(samples=samples)

    print(f"✅ 成功生成 {len(testset.samples)} 个测试样本")
    return testset


async def generate_testset_advanced(docs: List[Document], testset_size: int = 10):
    """高级方式生成测试集（使用知识图谱）"""
    print("=== 使用高级方式生成测试集 ===")
    
    # 创建 Ragas 适配器
    generator_llm = create_ragas_llm()
    generator_embeddings = create_ragas_embeddings()
    
    # 创建知识图谱
    print("创建知识图谱...")
    kg = KnowledgeGraph()
    
    # 添加文档节点
    for doc in docs:
        kg.nodes.append(
            Node(
                type=NodeType.DOCUMENT,
                properties={
                    "page_content": doc.page_content,
                    "document_metadata": doc.metadata
                }
            )
        )
    
    print(f"知识图谱初始节点数: {len(kg.nodes)}")
    
    try:
        # 应用转换增强知识图谱
        print("应用转换增强知识图谱...")
        trans = default_transforms(
            documents=docs,
            llm=generator_llm,
            embedding_model=generator_embeddings
        )
        apply_transforms(kg, trans)
        
        print(f"增强后的知识图谱: 节点数={len(kg.nodes)}, 关系数={len(kg.relationships)}")
        
        # 创建测试集生成器
        generator = TestsetGenerator(
            llm=generator_llm,
            embedding_model=generator_embeddings,
            knowledge_graph=kg
        )
        
        # 定义查询分布
        query_distribution = default_query_distribution(generator_llm, kg)
        print("查询类型分布:")
        for synthesizer, probability in query_distribution:
            print(f"- {synthesizer.name}: {probability:.2%}")
        
        # 生成测试集
        print(f"开始生成 {testset_size} 个测试样本...")
        testset = generator.generate(
            testset_size=testset_size,
            query_distribution=query_distribution
        )
        
        print(f"✅ 成功生成 {len(testset.samples)} 个测试样本")
        return testset
        
    except Exception as e:
        print(f"❌ 高级生成失败: {e}")
        import traceback
        traceback.print_exc()
        print("回退到简单方式...")
        return await generate_testset_simple(docs, testset_size)


def save_testset(testset, output_path: str = "generated_testset.csv"):
    """保存测试集"""
    try:
        # 转换为 pandas DataFrame 并保存
        df = testset.to_pandas()
        df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"✅ 测试集已保存到: {output_path}")
        
        # 显示统计信息
        print("\n=== 测试集统计信息 ===")
        print(f"总样本数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        if 'synthesizer_name' in df.columns:
            print("\n查询类型分布:")
            print(df['synthesizer_name'].value_counts())
        
        # 显示前几个样本
        print("\n=== 样本示例 ===")
        for i, row in df.head(3).iterrows():
            print(f"\n样本 {i+1}:")
            print(f"查询: {row.get('user_input', 'N/A')}")
            print(f"参考答案: {row.get('reference', 'N/A')}")
            if 'synthesizer_name' in row:
                print(f"合成器: {row['synthesizer_name']}")
            print("-" * 50)
            
    except Exception as e:
        print(f"❌ 保存测试集失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RAG 评估数据生成脚本")
    parser.add_argument("--docs-dir", type=str, help="文档目录路径")
    parser.add_argument("--size", type=int, default=10, help="生成的测试样本数量")
    parser.add_argument("--output", type=str, default="generated_testset.csv", help="输出文件路径")
    parser.add_argument("--simple", action="store_true", help="使用简单模式生成")
    
    args = parser.parse_args()
    
    print("🚀 开始生成 RAG 评估数据...")
    
    # 加载文档
    if args.docs_dir and os.path.exists(args.docs_dir):
        docs = load_documents_from_directory(args.docs_dir)
    else:
        docs = load_sample_documents()
    
    if not docs:
        print("❌ 没有找到文档，退出")
        return
    
    # 生成测试集
    if args.simple:
        testset = await generate_testset_simple(docs, args.size)
    else:
        testset = await generate_testset_advanced(docs, args.size)
    
    if testset:
        # 保存测试集
        save_testset(testset, args.output)
        print("🎉 评估数据生成完成!")
    else:
        print("❌ 评估数据生成失败")


if __name__ == "__main__":
    asyncio.run(main())
