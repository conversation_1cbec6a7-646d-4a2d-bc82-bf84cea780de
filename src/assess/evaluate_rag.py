#!/usr/bin/env python3
"""
RAG 评估脚本

使用生成的测试数据集评估 RAG 系统性能
"""
import pandas as pd
import asyncio
from pathlib import Path
from typing import List

from ragas import EvaluationDataset, SingleTurnSample, evaluate
from ragas.metrics import (
    Faithfulness,
    AnswerRelevancy, 
    ContextPrecision,
    ContextRecall,
    AnswerCorrectness
)
from ragas.llms import LangchainLLMWrapper
from ragas.embeddings import LangchainEmbeddingsWrapper
from langchain_openai import ChatOpenAI, OpenAIEmbeddings

from assess.config import config


def load_testset_from_csv(csv_path: str) -> EvaluationDataset:
    """从 CSV 文件加载测试集"""
    df = pd.read_csv(csv_path)
    
    samples = []
    for _, row in df.iterrows():
        # 解析 retrieved_contexts (JSON 字符串格式)
        import json
        contexts = json.loads(row['retrieved_contexts'])
        
        sample = SingleTurnSample(
            user_input=row['user_input'],
            response=row['response'],
            retrieved_contexts=contexts,
            reference=row['reference']
        )
        samples.append(sample)
    
    return EvaluationDataset(samples=samples)


def create_evaluator_llm():
    """创建评估用的 LLM"""
    llm = ChatOpenAI(
        model=config.model.llm_model_name,
        base_url=config.model.llm_base_url,
        api_key=config.model.llm_api_key,
        temperature=0.1,  # 评估时使用较低的温度
        max_tokens=config.model.llm_max_tokens,
        timeout=config.model.llm_timeout
    )
    return LangchainLLMWrapper(llm)


def create_evaluator_embeddings():
    """创建评估用的 Embeddings"""
    embeddings = OpenAIEmbeddings(
        model=config.model.embedding_model_name,
        openai_api_base=config.model.embedding_base_url,
        openai_api_key=config.model.embedding_api_key
    )
    return LangchainEmbeddingsWrapper(embeddings)


async def evaluate_rag_system(testset_path: str = "generated_testset.csv"):
    """评估 RAG 系统"""
    print("🚀 开始 RAG 系统评估...")
    
    # 加载测试集
    print(f"📂 加载测试集: {testset_path}")
    testset = load_testset_from_csv(testset_path)
    print(f"✅ 加载了 {len(testset.samples)} 个测试样本")
    
    # 创建评估器
    print("🔧 初始化评估器...")
    evaluator_llm = create_evaluator_llm()
    evaluator_embeddings = create_evaluator_embeddings()
    
    # 定义评估指标
    metrics = [
        Faithfulness(llm=evaluator_llm),                    # 忠实性
        AnswerRelevancy(llm=evaluator_llm),                 # 答案相关性
        ContextPrecision(llm=evaluator_llm),                # 上下文精确度
        ContextRecall(llm=evaluator_llm),                   # 上下文召回率
        AnswerCorrectness(llm=evaluator_llm),               # 答案正确性
    ]
    
    print(f"📊 使用 {len(metrics)} 个评估指标:")
    for metric in metrics:
        print(f"  - {metric.__class__.__name__}")
    
    try:
        # 执行评估
        print("\n⏳ 开始评估...")
        result = evaluate(
            dataset=testset,
            metrics=metrics,
            show_progress=True
        )
        
        print("\n🎉 评估完成!")
        print("\n=== 评估结果 ===")
        
        # 显示总体结果
        for metric_name, score in result.items():
            print(f"{metric_name}: {score:.4f}")
        
        # 保存详细结果
        result_df = result.to_pandas()
        result_path = "evaluation_results.csv"
        result_df.to_csv(result_path, index=False)
        print(f"\n📁 详细结果已保存到: {result_path}")
        
        # 显示样本级别的结果
        print("\n=== 样本级别结果 (前3个) ===")
        for i, (_, row) in enumerate(result_df.head(3).iterrows()):
            print(f"\n样本 {i+1}:")
            print(f"  查询: {row['user_input'][:100]}...")
            print(f"  忠实性: {row.get('faithfulness', 'N/A'):.4f}")
            print(f"  答案相关性: {row.get('answer_relevancy', 'N/A'):.4f}")
            print(f"  上下文精确度: {row.get('context_precision', 'N/A'):.4f}")
        
        return result
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_results(result_path: str = "evaluation_results.csv"):
    """分析评估结果"""
    if not Path(result_path).exists():
        print(f"❌ 结果文件不存在: {result_path}")
        return
    
    df = pd.read_csv(result_path)
    
    print("📈 评估结果分析:")
    print(f"总样本数: {len(df)}")
    
    # 计算各指标的统计信息
    numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
    
    for col in numeric_cols:
        if col in ['faithfulness', 'answer_relevancy', 'context_precision', 'context_recall', 'answer_correctness']:
            values = df[col].dropna()
            if len(values) > 0:
                print(f"\n{col}:")
                print(f"  平均值: {values.mean():.4f}")
                print(f"  中位数: {values.median():.4f}")
                print(f"  标准差: {values.std():.4f}")
                print(f"  最小值: {values.min():.4f}")
                print(f"  最大值: {values.max():.4f}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RAG 系统评估")
    parser.add_argument("--testset", type=str, default="generated_testset.csv", help="测试集文件路径")
    parser.add_argument("--analyze", action="store_true", help="分析现有结果")
    
    args = parser.parse_args()
    
    if args.analyze:
        analyze_results()
    else:
        result = await evaluate_rag_system(args.testset)
        if result:
            analyze_results()


if __name__ == "__main__":
    asyncio.run(main())
