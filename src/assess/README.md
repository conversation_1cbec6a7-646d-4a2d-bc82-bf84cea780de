# RAG 评估数据生成和评估系统

本项目使用 Ragas 框架开发了一套完整的 RAG（检索增强生成）评估数据生成和评估系统。

## 🚀 项目概述

该系统包含以下核心功能：

1. **评估数据生成**：基于文档自动生成高质量的 RAG 评估数据集
2. **RAG 系统评估**：使用多种指标全面评估 RAG 系统性能
3. **结果分析**：提供详细的评估结果分析和可视化

## 📁 文件结构

```
assess/
├── .env                    # 环境配置文件
├── config.py              # 配置管理模块
├── llm_clients.py         # LLM 和 Embedding 客户端
├── ragas_adapters.py      # Ragas 适配器（备用）
├── generate_testset.py    # 测试数据生成脚本
├── evaluate_rag.py        # RAG 评估脚本
├── generated_testset.csv  # 生成的测试数据集
└── README.md              # 本文档
```

## 🛠️ 环境配置

### 1. 安装依赖

```bash
# 安装 Ragas 和相关依赖
pip install -e ../ragas
pip install rapidfuzz python-dotenv langchain-openai
```

### 2. 配置环境变量

编辑 `.env` 文件，配置 LLM 和 Embedding 服务：

```env
# LLM 配置
LLM_MODEL_NAME=qwen-turbo
LLM_BASE_URL=http://*************:3010/v1
LLM_API_KEY=your_api_key
LLM_MAX_TOKENS=16384
LLM_TEMPERATURE=0.1
LLM_TIMEOUT=60

# Embedding 配置
EMBEDDING_MODEL_NAME=nomic-embed-text:v1.5
EMBEDDING_BASE_URL=http://*************:3010/v1
EMBEDDING_API_KEY=your_api_key
EMBEDDING_DIMENSION=768
EMBEDDING_BATCH_SIZE=10
```

## 📊 使用方法

### 1. 生成评估数据

```bash
# 生成 10 个测试样本
python generate_testset.py --size 10 --simple

# 指定输出文件
python generate_testset.py --size 20 --output my_testset.csv

# 使用自定义文档目录
python generate_testset.py --docs-dir /path/to/docs --size 15
```

### 2. 评估 RAG 系统

```bash
# 使用默认测试集评估
python evaluate_rag.py

# 使用自定义测试集
python evaluate_rag.py --testset my_testset.csv

# 分析现有评估结果
python evaluate_rag.py --analyze
```

## 📈 评估指标

系统支持以下 RAG 评估指标：

### 检索质量指标
- **Context Precision（上下文精确度）**：检索到的上下文中相关信息的比例
- **Context Recall（上下文召回率）**：参考答案中的信息在检索上下文中的覆盖率

### 生成质量指标
- **Faithfulness（忠实性）**：生成答案与检索上下文的一致性
- **Answer Relevancy（答案相关性）**：生成答案与用户问题的相关程度
- **Answer Correctness（答案正确性）**：生成答案的准确性

## 📋 生成的测试数据格式

生成的测试数据集包含以下字段：

- `user_input`: 用户查询
- `retrieved_contexts`: 检索到的上下文（JSON 数组格式）
- `response`: RAG 系统生成的回答
- `reference`: 参考答案

示例：
```csv
user_input,retrieved_contexts,response,reference
"Who proposed the theory of relativity?","[""Albert Einstein proposed...""]","Albert Einstein proposed...","Albert Einstein proposed..."
```

## 🎯 特色功能

### 1. 灵活的配置管理
- 支持多种 LLM 和 Embedding 模型
- 可配置的 API 端点和参数
- 环境变量和配置文件支持

### 2. 多样化的测试数据
- 基于真实文档生成测试样本
- 涵盖不同类型的查询（事实性、推理性、比较性等）
- 支持自定义文档和查询分布

### 3. 全面的评估体系
- 多维度评估指标
- 样本级别和整体性能分析
- 详细的评估报告和统计信息

### 4. 易于扩展
- 模块化设计，易于添加新的评估指标
- 支持自定义评估逻辑
- 兼容多种 LLM 和 Embedding 服务

## 📊 评估结果示例

```
=== 评估结果 ===
faithfulness: 0.8571
answer_relevancy: 0.9234
context_precision: 0.7890
context_recall: 0.8456
answer_correctness: 0.8123

=== 样本级别结果 ===
样本 1:
  查询: Who proposed the theory of relativity...
  忠实性: 0.9000
  答案相关性: 0.9500
  上下文精确度: 0.8500
```

## 🔧 故障排除

### 常见问题

1. **LLM 服务连接失败**
   - 检查 `.env` 文件中的 API 端点和密钥
   - 确认服务是否正常运行

2. **依赖包安装问题**
   - 使用代理：`export https_proxy=http://127.0.0.1:7890`
   - 安装特定版本：`pip install ragas==0.3.0`

3. **评估过程中出错**
   - 检查测试数据格式是否正确
   - 确认 LLM 服务稳定性

## 🚀 下一步计划

- [ ] 支持更多评估指标
- [ ] 添加可视化评估报告
- [ ] 支持批量评估和比较
- [ ] 集成更多 LLM 和 Embedding 模型
- [ ] 添加评估结果的统计分析功能

## 📝 更新日志

### v1.0.0 (2025-07-18)
- ✅ 完成基础的测试数据生成功能
- ✅ 实现 RAG 系统评估框架
- ✅ 支持多种评估指标
- ✅ 生成 10 个高质量测试样本
- ✅ 提供完整的使用文档

---

**注意**：本系统基于 Ragas 0.3.0 版本开发，确保使用兼容的版本以获得最佳体验。
