"""
MixRAG 统一配置管理模块

🔧 核心功能：
- 环境变量统一管理
- 配置参数验证和类型转换
- 默认值和配置继承
- 运行时配置热更新支持

📋 配置分类：
- 数据库配置 (PostgreSQL, Redis, Neo4j)
- 存储配置 (MinIO, Milvus)
- 模型配置 (LLM, Embedding)
- 任务配置 (并发数, 超时时间)
- API配置 (端口, CORS, 限流)
"""

import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional, Dict, Any


@dataclass
class DatabaseConfig:
    """数据库配置"""
    # PostgreSQL配置
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "mixrag"
    postgres_user: str = "postgres"
    postgres_password: str = "password"
    postgres_pool_size: int = 10
    postgres_max_overflow: int = 20
    
    # Redis配置
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    redis_pool_size: int = 10
    
    # Neo4j配置
    neo4j_uri: str = "bolt://localhost:7687"
    neo4j_user: str = "neo4j"
    neo4j_password: str = "password"


@dataclass
class StorageConfig:
    """存储配置"""
    # MinIO配置
    minio_endpoint: str = "localhost:9000"
    minio_access_key: str = "minioadmin"
    minio_secret_key: str = "minioadmin"
    minio_secure: bool = False
    minio_bucket: str = "mixrag-documents"
    
    # Milvus配置
    milvus_host: str = "localhost"
    milvus_port: int = 19530
    milvus_collection: str = "mixrag_vectors"
    milvus_dimension: int = 1536


@dataclass
class ModelConfig:
    """模型配置"""
    llm_timeout: int = 60
    # LLM配置
    llm_model_name: str = "gpt-4o"
    llm_api_key: Optional[str] = None
    llm_base_url: Optional[str] = None
    llm_max_tokens: int = 16384
    llm_temperature: float = 0.1

    # Embedding配置
    embedding_model_name: str = "text-embedding-3-large"
    embedding_api_key: Optional[str] = None
    embedding_base_url: Optional[str] = None
    embedding_dimension: int = 768
    embedding_batch_size: int = 50


@dataclass
class TaskConfig:
    """任务配置"""
    # 并发控制
    max_concurrent_tasks: int = 10
    max_concurrent_pipelines: int = 5
    task_timeout: int = 300
    pipeline_timeout: int = 1800
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 清理配置
    cleanup_interval: int = 3600  # 1小时
    task_retention_days: int = 7


@dataclass
class APIConfig:
    """API配置"""
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    reload: bool = False
    
    # CORS配置
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    cors_methods: List[str] = field(default_factory=lambda: ["GET", "POST", "PUT", "DELETE", "OPTIONS"])
    cors_headers: List[str] = field(default_factory=lambda: ["*"])
    
    # 限流配置
    rate_limit_requests: int = 100
    rate_limit_window: int = 60
    
    # 文件上传配置
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: List[str] = field(default_factory=lambda: [
        "text/plain",
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/markdown",
        "application/msword"
    ])


@dataclass
class ProcessingConfig:
    """处理配置"""
    # 文本分块配置
    chunk_size: int = 1200
    chunk_overlap: int = 100
    
    # 实体抽取配置
    entity_types: List[str] = field(default_factory=lambda: [
        "organization", "person", "location", "event", "concept", "technology"
    ])
    max_gleaning_rounds: int = 1
    extract_max_retry: int = 3
    
    # 知识图谱配置
    min_entity_occurrence: int = 1
    min_relationship_weight: float = 0.1


class MixRAGConfig:
    """
    🔧 MixRAG统一配置管理器
    
    负责加载、验证和管理所有配置参数
    """
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            env_file: 环境变量文件路径
        """
        self._load_env_file(env_file)
        
        # 初始化各模块配置
        self.database = self._load_database_config()
        self.storage = self._load_storage_config()
        self.model = self._load_model_config()
        self.pipeline = self._load_pipeline_config()
        self.api = self._load_api_config()
        self.processing = self._load_processing_config()
    
    def _load_env_file(self, env_file: Optional[str] = None):
        """加载环境变量文件"""
        if env_file is None:
            # 查找项目根目录的.env文件
            current_dir = Path(__file__).parent
            env_file = current_dir.parent / ".env"
        
        if Path(env_file).exists():
            try:
                from dotenv import load_dotenv
                load_dotenv(env_file)
            except ImportError:
                pass  # 如果没有安装python-dotenv，忽略
    
    def _load_database_config(self) -> DatabaseConfig:
        """加载数据库配置"""
        return DatabaseConfig(
            postgres_host=os.getenv("POSTGRES_HOST", "localhost"),
            postgres_port=int(os.getenv("POSTGRES_PORT", "5432")),
            postgres_db=os.getenv("POSTGRES_DATABASE", os.getenv("POSTGRES_DB", "mixrag")),
            postgres_user=os.getenv("POSTGRES_USER", "postgres"),
            postgres_password=os.getenv("POSTGRES_PASSWORD", "password"),
            postgres_pool_size=int(os.getenv("POSTGRES_POOL_SIZE", "10")),
            postgres_max_overflow=int(os.getenv("POSTGRES_MAX_OVERFLOW", "20")),
            
            redis_host=os.getenv("REDIS_HOST", "localhost"),
            redis_port=int(os.getenv("REDIS_PORT", "6379")),
            redis_db=int(os.getenv("REDIS_DATABASE", os.getenv("REDIS_DB", "0"))),
            redis_password=os.getenv("REDIS_PASSWORD"),
            redis_pool_size=int(os.getenv("REDIS_POOL_SIZE", "10")),
            
            neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            neo4j_user=os.getenv("NEO4J_USERNAME", os.getenv("NEO4J_USER", "neo4j")),
            neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
        )
    
    def _load_storage_config(self) -> StorageConfig:
        """加载存储配置"""
        return StorageConfig(
            minio_endpoint=os.getenv("MINIO_ENDPOINT", "localhost:9000"),
            minio_access_key=os.getenv("MINIO_ACCESS_KEY", "minioadmin"),
            minio_secret_key=os.getenv("MINIO_SECRET_KEY", "minioadmin"),
            minio_secure=os.getenv("MINIO_SECURE", "false").lower() == "true",
            minio_bucket=os.getenv("MINIO_BUCKET", "mixrag-documents"),
            
            milvus_host=os.getenv("MILVUS_HOST", "localhost"),
            milvus_port=int(os.getenv("MILVUS_PORT", "19530")),
            milvus_collection=os.getenv("MILVUS_COLLECTION", "mixrag_vectors"),
            milvus_dimension=int(os.getenv("MILVUS_DIMENSION", "1536")),
        )
    
    def _load_model_config(self) -> ModelConfig:
        """加载模型配置"""
        return ModelConfig(
            llm_model_name=os.getenv("LLM_MODEL_NAME", "gpt-4o"),
            llm_api_key=os.getenv("LLM_API_KEY"),
            llm_base_url=os.getenv("LLM_BASE_URL"),
            llm_max_tokens=int(os.getenv("LLM_MAX_TOKENS", "16384")),
            llm_temperature=float(os.getenv("LLM_TEMPERATURE", "0.1")),
            llm_timeout=int(os.getenv("LLM_TIMEOUT", "60")),
            
            embedding_model_name=os.getenv("EMBEDDING_MODEL_NAME", "text-embedding-3-large"),
            embedding_api_key=os.getenv("EMBEDDING_API_KEY"),
            embedding_base_url=os.getenv("EMBEDDING_BASE_URL"),
            embedding_dimension=int(os.getenv("EMBEDDING_DIMENSION", "1536")),
            embedding_batch_size=int(os.getenv("EMBEDDING_BATCH_SIZE", "100")),
        )
    
    def _load_pipeline_config(self) -> TaskConfig:
        """加载任务配置"""
        return TaskConfig(
            max_concurrent_tasks=int(os.getenv("MAX_CONCURRENT_TASKS", "10")),
            max_concurrent_pipelines=int(os.getenv("MAX_CONCURRENT_PIPELINES", "5")),
            task_timeout=int(os.getenv("TASK_TIMEOUT", "300")),
            pipeline_timeout=int(os.getenv("PIPELINE_TIMEOUT", "1800")),
            
            max_retries=int(os.getenv("MAX_RETRIES", "3")),
            retry_delay=float(os.getenv("RETRY_DELAY", "1.0")),
            
            cleanup_interval=int(os.getenv("CLEANUP_INTERVAL", "3600")),
            task_retention_days=int(os.getenv("TASK_RETENTION_DAYS", "7")),
        )
    
    def _load_api_config(self) -> APIConfig:
        """加载API配置"""
        cors_origins = os.getenv("CORS_ORIGINS", "*").split(",")
        cors_methods = os.getenv("CORS_METHODS", "GET,POST,PUT,DELETE,OPTIONS").split(",")
        cors_headers = os.getenv("CORS_HEADERS", "*").split(",")
        
        allowed_types = os.getenv("ALLOWED_FILE_TYPES", 
                                "text/plain,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/markdown,application/msword").split(",")
        
        return APIConfig(
            host=os.getenv("API_HOST", "0.0.0.0"),
            port=int(os.getenv("API_PORT", "8000")),
            debug=os.getenv("API_DEBUG", "false").lower() == "true",
            reload=os.getenv("API_RELOAD", "false").lower() == "true",
            
            cors_origins=cors_origins,
            cors_methods=cors_methods,
            cors_headers=cors_headers,
            
            rate_limit_requests=int(os.getenv("RATE_LIMIT_REQUESTS", "100")),
            rate_limit_window=int(os.getenv("RATE_LIMIT_WINDOW", "60")),
            
            max_file_size=int(os.getenv("MAX_FILE_SIZE", str(50 * 1024 * 1024))),
            allowed_file_types=allowed_types,
        )
    
    def _load_processing_config(self) -> ProcessingConfig:
        """加载处理配置"""
        entity_types = os.getenv("ENTITY_TYPES", "organization,person,location,event,concept,technology").split(",")
        
        return ProcessingConfig(
            chunk_size=int(os.getenv("CHUNK_SIZE", "1200")),
            chunk_overlap=int(os.getenv("CHUNK_OVERLAP", "100")),
            
            entity_types=entity_types,
            max_gleaning_rounds=int(os.getenv("MAX_GLEANING_ROUNDS", "1")),
            extract_max_retry=int(os.getenv("EXTRACT_MAX_RETRY", "3")),
            
            min_entity_occurrence=int(os.getenv("MIN_ENTITY_OCCURRENCE", "1")),
            min_relationship_weight=float(os.getenv("MIN_RELATIONSHIP_WEIGHT", "0.1")),
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "database": self.database.__dict__,
            "storage": self.storage.__dict__,
            "model": self.model.__dict__,
            "task": self.pipeline.__dict__,
            "api": self.api.__dict__,
            "processing": self.processing.__dict__,
        }


# 全局配置实例
config = MixRAGConfig()
