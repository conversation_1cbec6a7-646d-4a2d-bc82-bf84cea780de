"""
MixRAG - 混合检索增强生成系统

这是一个高性能的知识图谱增强检索系统，包含四个核心步骤：
1. 文本分块 (Text Chunking) - 智能文档分割
2. 实体关系抽取 (Entity & Relationship Extraction) - 知识抽取
3. 知识图谱构建 (Knowledge Graph Building) - 图谱构建
4. 知识图谱检索 (Knowledge Graph Retrieval) - 智能检索

主要特性:
- 🚀 完整的端到端工作流
- 🔍 支持 local、global、mix 三种检索模式
- 💾 多种存储后端支持 (Redis, Neo4j, Milvus, PostgreSQL)
- 🧠 保留原始 MixRAG 核心算法
- 🔧 模块化设计，易于理解和扩展
- ⚡ 异步处理，高性能并发

使用示例:
```python
import asyncio
from src.utils import call_llm, create_embedding_func
from src.rag.pipeline import task_manager

async def main():
    # 初始化任务管理器
    await task_manager.initialize()

    # 提交文档处理任务
    task_id = await task_manager.submit_document_processing_chain(
        content="人工智能是计算机科学的一个分支。",
        doc_id="test_doc"
    )

    # 等待处理完成
    result = await task_manager.wait_for_chain_completion(task_id)
    print(f"处理结果: {result}")

    # 清理
    await task_manager.shutdown()

# 运行
asyncio.run(main())
```
"""

# 导入 RAG 核心模块
from src.rag import (
    # 文本分块
    DocumentChunker,
    DocumentChunk,
    chunking_by_token_size,

    # 实体关系抽取
    EntityExtractor,
    Entity,
    Relationship,
    extract_entities_from_chunks,

    # 基础设施
    BaseKVBaseStorage,
    BaseVectorBaseStorage,
    BaseGraphBaseStorage,

    # LLM
    call_llm,
    call_embedding,
    PROMPTS,

    # 工具函数
    logger,
    compute_mdhash_id,

    # 任务管理
    TaskType,
    PipelineTask,
)

# 版本信息
__version__ = "1.0.0"
__author__ = "MixRAG Team"
__description__ = "混合检索增强生成系统 - 高性能知识图谱RAG"

# 导出的公共 API
__all__ = [
    # 核心组件
    "DocumentChunker",
    "EntityExtractor",
    "DocumentChunk",

    # 数据结构
    "Entity",
    "Relationship",

    # 存储组件
    "BaseKVBaseStorage",
    "BaseVectorBaseStorage",
    "BaseGraphBaseStorage",

    # LLM 功能
    "call_llm",
    "call_embedding",
    "PROMPTS",

    # 工具函数
    "logger",
    "compute_mdhash_id",
    "chunking_by_token_size",
    "extract_entities_from_chunks",

    # 任务管理
    "TaskType",
    "PipelineTask",

    # 版本信息
    "__version__",
    "__author__",
    "__description__"
]

# 模块级别的日志信息
logger.info(f"🚀 MixRAG v{__version__} 初始化完成")
logger.info("📦 可用组件: DocumentChunker, EntityExtractor, KnowledgeGraphBuilder, KnowledgeGraphRetriever")
logger.info("💾 支持存储: Redis, Neo4j, Milvus, PostgreSQL MinIO")
logger.info("🔍 检索模式: local, global, mix")
