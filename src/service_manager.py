"""
MixRAG 系统服务管理器

负责管理所有系统服务的启动、停止和生命周期
"""

import asyncio
import signal
import sys
import traceback
from typing import Optional

import uvicorn

from src.api.app import app


class ServiceManager:
    """MixRAG 系统服务管理器"""

    def __init__(self):
        self.is_running = False
        self.uvicorn_server: Optional[uvicorn.Server] = None

    async def start(self, host: str = "0.0.0.0", port: int = 8000,
                    reload: bool = False, workers: int = 1):
        """启动所有服务"""
        if self.is_running:
            print("⚠️  服务已在运行中")
            return

        print("🚀 启动 MixRAG 系统服务...")
        print(f"   地址: http://{host}:{port}")
        print(f"   文档: http://{host}:{port}/docs")
        print(f"   API: http://{host}:{port}/api/v1")
        print("-" * 60)

        try:
            self.is_running = True

            # 配置 uvicorn 服务器
            config = uvicorn.Config(
                app=app,
                host=host,
                port=port,
                log_level="info",
                reload=reload,
                workers=1 if reload else workers,  # reload模式下只能单进程
            )

            self.uvicorn_server = uvicorn.Server(config)

            print("🌐 启动 FastAPI 服务器...")
            print("🎉 MixRAG 系统启动完成!")
            print("-" * 60)

            # 启动 uvicorn 服务器
            await self.uvicorn_server.serve()

        except Exception as e:
            print(f"💥 服务启动失败: {e}")
            traceback.print_exc()
            await self.stop()
            raise

    async def stop(self):
        """停止所有服务"""
        if not self.is_running:
            return

        print("\n🛑 停止 MixRAG 系统服务...")

        try:
            # 停止 uvicorn 服务器
            if self.uvicorn_server:
                print("   ⏹️ 停止 FastAPI 服务器...")
                self.uvicorn_server.should_exit = True
                if hasattr(self.uvicorn_server, 'force_exit'):
                    self.uvicorn_server.force_exit = True

        except Exception as e:
            print(f"💥 停止服务时出错: {e}")
            traceback.print_exc()
        finally:
            self.is_running = False

        print("👋 MixRAG 系统服务已停止")


# 全局服务管理器实例
service_manager = ServiceManager()


class SignalHandler:
    """信号处理器"""
    
    def __init__(self, manager: ServiceManager):
        self.manager = manager
    
    def setup(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n📡 收到信号 {signum}，正在关闭服务...")
            # 创建新的事件循环来处理关闭操作
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self.manager.stop())
            else:
                asyncio.run(self.manager.stop())

        # 注册信号处理器
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, signal_handler)