create table public.mixrag_documents
(
    doc_id              varchar(64)  not null
        primary key,
    original_filename   varchar(500) not null,
    file_extension      varchar(10),
    file_size           bigint,
    mime_type           varchar(100),
    minio_bucket        varchar(100) not null,
    minio_object_key    varchar(500) not null,
    content_hash        varchar(64),
    upload_status       varchar(20) default 'pending'::character varying,
    process_status      varchar(20) default 'pending'::character varying,
    created_at          timestamp   default CURRENT_TIMESTAMP,
    updated_at          timestamp   default CURRENT_TIMESTAMP,
    uploaded_by         varchar(100),
    error_message       text,
    chunks_count        integer     default 0,
    entities_count      integer     default 0,
    relationships_count integer     default 0,
    pipeline_id         varchar(50),
    kb_id               varchar(64)  not null
);

comment on column public.mixrag_documents.kb_id is '知识库id(文档归属的知识库)';

alter table public.mixrag_documents
    owner to root;

create index idx_documents_upload_status
    on public.mixrag_documents (upload_status);

create index idx_documents_process_status
    on public.mixrag_documents (process_status);

create index idx_documents_created_at
    on public.mixrag_documents (created_at);

create index idx_documents_minio_object_key
    on public.mixrag_documents (minio_object_key);

create index idx_documents_pipeline_id
    on public.mixrag_documents (pipeline_id);

create trigger update_documents_updated_at
    before update
    on public.mixrag_documents
    for each row
execute procedure public.update_updated_at_column();

create table public.mixrag_pipeline
(
    id            varchar(50)                                      not null
        primary key,
    name          varchar(200)                                     not null,
    description   text,
    status        varchar(20) default 'pending'::character varying not null,
    created_at    timestamp   default CURRENT_TIMESTAMP,
    started_at    timestamp,
    completed_at  timestamp,
    created_by    varchar(100),
    error_message text,
    doc_id        varchar(64)
);

alter table public.mixrag_pipeline
    owner to root;

create index idx_pipeline_status
    on public.mixrag_pipeline (status);

create index idx_pipelines_status
    on public.mixrag_pipeline (status);

create index idx_pipelines_created_at
    on public.mixrag_pipeline (created_at);

create table public.mixrag_pipeline_task
(
    id            varchar(50)                                      not null
        primary key,
    pipeline_id   varchar(50)                                      not null
        references public.mixrag_pipeline
            on delete cascade,
    task_type     varchar(50)                                      not null,
    status        varchar(20) default 'pending'::character varying not null,
    error_message text,
    retry_count   integer     default 0,
    max_retries   integer     default 3,
    step_order    integer     default 0                            not null,
    dependencies  text,
    created_at    timestamp   default CURRENT_TIMESTAMP,
    started_at    timestamp,
    completed_at  timestamp,
    input_data    text,
    output_data   text
);

alter table public.mixrag_pipeline_task
    owner to root;

create index idx_pipeline_tasks_pipeline_id
    on public.mixrag_pipeline_task (pipeline_id);

create index idx_pipeline_tasks_status_type
    on public.mixrag_pipeline_task (status, task_type);

create index idx_pipeline_tasks_step_order
    on public.mixrag_pipeline_task (pipeline_id, step_order);

create table public.mixrag_model_manage
(
    id          varchar(64)                                     not null
        constraint mixrag_model_manage_pk
            primary key,
    model_name  varchar(64)                                     not null,
    model_alias varchar(64),
    base_url    varchar(500)                                    not null,
    api_key     varchar(1000) default ''::character varying     not null,
    model_type  varchar(50)   default 'CHAT'::character varying not null
);

comment on table public.mixrag_model_manage is '模型管理';

comment on column public.mixrag_model_manage.id is 'id';

comment on column public.mixrag_model_manage.model_name is '模型名';

comment on column public.mixrag_model_manage.model_alias is '模型别名 ';

comment on column public.mixrag_model_manage.base_url is '模型地址';

comment on column public.mixrag_model_manage.api_key is '模型访问api_key';

comment on column public.mixrag_model_manage.model_type is '模型类别(CHAT 聊天模型, EMBBEDING 嵌入模型,RERANKER 重排序模型)';

alter table public.mixrag_model_manage
    owner to root;

create table public.mixrag_knowledge_base
(
    id          varchar(64)      not null
        constraint mixrag_knowledge_base_pk
            primary key,
    kb_name     varchar(100)     not null,
    kb_des      varchar(200),
    doc_count   bigint default 0 not null,
    create_time timestamp        not null
);

comment on table public.mixrag_knowledge_base is '知识库';

comment on column public.mixrag_knowledge_base.id is '主键';

comment on column public.mixrag_knowledge_base.kb_name is '知识库名';

comment on column public.mixrag_knowledge_base.kb_des is '知识库描述';

comment on column public.mixrag_knowledge_base.doc_count is '知识库中文档总数';

comment on column public.mixrag_knowledge_base.create_time is '创建时间';

alter table public.mixrag_knowledge_base
    owner to root;

