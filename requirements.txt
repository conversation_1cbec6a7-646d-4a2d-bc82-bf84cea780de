# FastAPI 和 Web 框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# 数据库连接器
psycopg2-binary>=2.9.7
neo4j>=5.0.0
redis>=5.0.0

# 向量数据库
pymilvus>=2.3.0

# 对象存储
minio>=7.0.0

# 机器学习和NLP
numpy>=1.24.0
pandas>=2.0.0
sentence-transformers>=2.2.0
transformers>=4.30.0
torch>=2.0.0
scikit-learn>=1.3.0

# 文档处理
pypdf2>=3.0.0
python-docx>=0.8.11
markdown>=3.5.0
beautifulsoup4>=4.12.0
python-magic>=0.4.27

# 任务队列和异步
celery>=5.3.0
aiofiles>=23.0.0
asyncio-mqtt>=0.16.0

# 配置和环境变量
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# HTTP客户端
httpx>=0.25.0
requests>=2.31.0
aiohttp>=3.8.0

# 日志和监控
loguru>=0.7.0

# 图形处理和可视化
networkx>=3.0
matplotlib>=3.7.0

# 文本处理
jieba>=0.42.1
spacy>=3.7.0

# 时间处理
python-dateutil>=2.8.0

# JSON和配置文件处理
jsonschema>=4.0.0
toml>=0.10.0

# API文档
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# 测试
pytest>=7.0.0
pytest-asyncio>=0.21.0 