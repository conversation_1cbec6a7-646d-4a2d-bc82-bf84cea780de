#!/bin/bash

# Mix RAG 系统服务启动脚本
# 用于快速启动 Mix RAG 系统服务

set -e

echo "🚀 启动 Mix RAG 系统服务..."

# 检查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "❌ 未找到 .env 配置文件"
    exit 1
fi

# 加载环境变量
echo "📋 加载环境变量..."
set -a
source .env
set +a

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p rag_storage

# 检查外部服务连接
echo "🔍 检查外部服务连接..."

# 使用 test 目录下的专用服务检查脚本
if [ -f "test/all_services_test.py" ]; then
    echo "🚀 运行服务检查脚本..."
    python3 test/all_services_test.py
    if [ $? -eq 0 ]; then
        echo "✅ 所有服务检查通过"
    else
        echo "⚠️  部分服务检查失败，请检查服务配置"
        echo "💡 可单独运行以下脚本进行调试："
        echo "   - Milvus: cd test && python3 milvus_test.py"
        echo "   - PostgreSQL: cd test && python3 postgresql_test.py"
        echo "   - Neo4j: cd test && python3 neo4j_test.py"
        echo "   - Redis: cd test && python3 redis_test.py"
        echo "   - MinIO: cd test && python3 minio_test.py"
    fi
else
    echo "⚠️  未找到服务检查脚本，跳过服务检查"
fi

echo ""

# 清理占用端口的进程
kill_port_processes() {
    local port=$1
    echo "🔍 检查端口 $port 是否被占用..."

    # 查找占用端口的进程ID
    local pids=$(lsof -ti:$port 2>/dev/null)

    if [ -n "$pids" ]; then
        echo "⚠️  发现端口 $port 被以下进程占用: $pids"
        echo "🔄 正在终止占用端口的进程..."

        # 尝试优雅关闭
        for pid in $pids; do
            if kill -TERM $pid 2>/dev/null; then
                echo "   发送 TERM 信号到进程 $pid"
            fi
        done

        # 等待2秒让进程优雅退出
        sleep 2

        # 检查是否还有进程占用端口
        local remaining_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$remaining_pids" ]; then
            echo "🔨 强制终止剩余进程..."
            for pid in $remaining_pids; do
                if kill -9 $pid 2>/dev/null; then
                    echo "   强制终止进程 $pid"
                fi
            done
        fi

        echo "✅ 端口 $port 已释放"
    else
        echo "✅ 端口 $port 未被占用"
    fi
}

# 清理旧的 Python 进程
cleanup_old_processes() {
    echo "🧹 清理旧的 MixRAG 进程..."
    
    # 查找并终止旧的 API 服务进程
    local api_pids=$(ps aux | grep -E "python.*start_api\.py|uvicorn.*api\.main:app" | grep -v grep | awk '{print $2}')
    if [ -n "$api_pids" ]; then
        echo "   终止旧的 API 服务进程: $api_pids"
        for pid in $api_pids; do
            kill -TERM $pid 2>/dev/null || true
        done
    fi
    
    # 查找并终止旧的流水线服务进程（独立运行的）
    local pipeline_pids=$(ps aux | grep -E "python.*pipeline_service" | grep -v grep | awk '{print $2}')
    if [ -n "$pipeline_pids" ]; then
        echo "   终止旧的独立流水线服务进程: $pipeline_pids"
        for pid in $pipeline_pids; do
            kill -TERM $pid 2>/dev/null || true
        done
    fi
    
    # 等待进程结束
    sleep 2
    echo "✅ 清理完成"
}

# 清理旧进程
cleanup_old_processes

# 清理 8000 端口
kill_port_processes 8000

# 启动 MixRAG 系统服务（统一启动入口）
echo ""
echo "🌟 启动 MixRAG 系统服务..."
echo "📖 API 文档地址: http://${HOST:-localhost}:${PORT:-8000}/docs"
echo "🔄 包含 FastAPI + 流水线调度器 + 任务管理器"
echo ""

# 取消代理设置
unset https_proxy http_proxy
echo '✅ 已取消代理设置'

# 启动 MixRAG 系统（前台运行，统一启动入口）
echo "🚀 正在启动 MixRAG 系统..."
#exec python src/main.py
exec python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
