/**
 * API相关类型定义模块
 * 
 * 定义了与后端API交互的所有数据类型：
 * 1. 查询相关类型
 * 2. 文档相关类型
 * 3. 系统状态类型
 * 4. 认证相关类型
 */

/**
 * 查询模式枚举
 * 
 * 指定检索模式：
 * - "naive": 执行基本搜索，不使用高级技术
 * - "local": 专注于上下文相关信息
 * - "global": 利用全局知识
 * - "mix": 结合本地和全局检索方法
 * - "mix": 集成知识图谱和向量检索
 * - "bypass": 绕过知识检索，直接使用LLM
 */
export type QueryMode = 'naive' | 'local' | 'global' | 'mix' | 'mix' | 'bypass';

/**
 * 消息类型
 */
export type Message = {
  role: 'user' | 'assistant' | 'system';
  content: string;
};

/**
 * 查询请求类型
 */
export type QueryRequest = {
  query: string;
  /** 指定检索模式 */
  mode: QueryMode;
  /** 如果为True，仅返回检索到的上下文，不生成响应 */
  only_need_context?: boolean;
  /** 如果为True，仅返回生成的提示，不产生响应 */
  only_need_prompt?: boolean;
  /** 定义响应格式。例如：'Multiple Paragraphs', 'Single Paragraph', 'Bullet Points' */
  response_type?: string;
  /** 如果为True，启用流式输出以实现实时响应 */
  stream?: boolean;
  /** 要检索的顶部项目数量。在'local'模式下表示实体，在'global'模式下表示关系 */
  top_k?: number;
  /** 每个检索文本块允许的最大token数量 */
  max_token_for_text_unit?: number;
  /** 在全局检索中为关系描述分配的最大token数量 */
  max_token_for_global_context?: number;
  /** 在本地检索中为实体描述分配的最大token数量 */
  max_token_for_local_context?: number;
  /**
   * 存储过去的对话历史以维护上下文
   * 格式：[{"role": "user/assistant", "content": "message"}]
   */
  conversation_history?: Message[];
  /** 在响应上下文中考虑的完整对话轮次数（用户-助手对） */
  history_turns?: number;
  /** 用户提供的查询提示。如果提供，将使用此提示而不是提示模板的默认值 */
  user_prompt?: string;
};

/**
 * 查询响应类型
 */
export type QueryResponse = {
  response: string;
};

/**
 * 文档状态枚举
 */
export type DocStatus = 'pending' | 'processing' | 'completed' | 'failed';

/**
 * 文档操作响应类型
 */
export type DocActionResponse = {
  status: 'success' | 'partial_success' | 'failure' | 'duplicated';
  message: string;
};

/**
 * 文档状态响应类型
 */
export type DocStatusResponse = {
  id: string;
  content_summary: string;
  content_length: number;
  status: DocStatus;
  created_at: string;
  updated_at: string;
  chunks_count?: number;
  error?: string;
  file_path: string;
};

/**
 * 文档状态列表响应类型
 */
export type DocsStatusesResponse = {
  statuses: Record<DocStatus, DocStatusResponse[]>;
};

/**
 * 系统状态类型
 */
export type SystemStatus = {
  status: 'healthy' | 'error';
  working_directory?: string;
  input_directory?: string;
  configuration?: {
    llm_binding: string;
    llm_binding_host: string;
    llm_model: string;
    embedding_binding: string;
    embedding_binding_host: string;
    embedding_model: string;
    max_tokens: number;
    kv_storage: string;
    doc_status_storage: string;
    graph_storage: string;
    vector_storage: string;
  };
  update_status?: Record<string, any>;
  core_version?: string;
  api_version?: string;
  auth_mode?: 'enabled' | 'disabled';
  pipeline_busy: boolean;
  webui_title?: string;
  webui_description?: string;
  message?: string;
};

/**
 * 文档扫描进度类型
 */
export type DocumentsScanProgress = {
  is_scanning: boolean;
  current_file: string;
  indexed_count: number;
  total_files: number;
  progress: number;
};

/**
 * 认证状态响应类型
 */
export type AuthStatusResponse = {
  auth_configured: boolean;
  access_token?: string;
  token_type?: string;
  auth_mode?: 'enabled' | 'disabled';
  message?: string;
  core_version?: string;
  api_version?: string;
  webui_title?: string;
  webui_description?: string;
};

/**
 * 图统计信息类型
 */
export type GraphStats = {
  node_count: number;
  edge_count: number;
  label_count: number;
  avg_degree: number;
  max_degree: number;
  density: number;
};

/**
 * 任务状态枚举
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

/**
 * 任务类型
 */
export type PipelineTask = {
  id: string;
  type: string;
  status: TaskStatus;
  progress: number;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
};

/**
 * 流水线状态枚举
 */
export type PipelineStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

/**
 * 流水线类型
 */
export type Pipeline = {
  id: string;
  name: string;
  status: PipelineStatus;
  progress: number;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  tasks: PipelineTask[];
};

/**
 * 分页响应类型
 */
export type PaginatedResponse<T> = {
  records: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
};

/**
 * API错误响应类型
 */
export type ApiErrorResponse = {
  error: string;
  detail?: string;
  timestamp: string;
};
