/**
 * 图数据类型定义模块
 *
 * 定义了图可视化相关的所有数据类型和接口：
 * 1. 节点和边的数据结构
 * 2. 图数据管理类
 * 3. 图操作相关的接口
 */

import { DirectedGraph } from 'graphology'

/**
 * 原始节点数据类型
 *
 * 支持NetworkX和Neo4j两种数据源：
 * - NetworkX: id与properties['entity_id']相同
 * - Neo4j: id是节点的唯一标识符
 */
export type RawNodeType = {
  id: string                        // 节点唯一标识
  labels: string[]                  // 节点标签列表
  properties: Record<string, any>   // 节点属性

  // 可视化相关属性
  size: number                      // 节点大小
  x: number                         // X坐标
  y: number                         // Y坐标
  color: string                     // 节点颜色

  // 图结构属性
  degree: number                    // 节点度数
}

/**
 * 原始边数据类型
 *
 * 支持NetworkX和Neo4j两种数据源：
 * - NetworkX: id格式为"source-target"
 * - Neo4j: id是边的唯一标识符
 */
export type RawEdgeType = {
  id: string                        // 边唯一标识
  source: string                    // 源节点ID
  target: string                    // 目标节点ID
  type?: string                     // 边类型
  properties: Record<string, any>   // 边属性
  dynamicId: string                 // SigmaGraph使用的动态ID
}

/**
 * 边更新跟踪接口
 *
 * 用于跟踪节点ID变更时需要更新的边
 */
export interface EdgeToUpdate {
  originalDynamicId: string         // 原始动态ID
  newEdgeId: string                 // 新的边ID
  edgeIndex: number                 // 边在数组中的索引
}

/**
 * 原始图数据管理类
 *
 * 提供图数据的存储和快速查询功能：
 * 1. 节点和边的数组存储
 * 2. ID到索引的映射表
 * 3. 快速查询方法
 */
export class RawGraph {
  // 数据存储
  nodes: RawNodeType[] = []
  edges: RawEdgeType[] = []

  // 索引映射表
  nodeIdMap: Record<string, number> = {}        // 节点ID到索引的映射
  edgeIdMap: Record<string, number> = {}        // 边ID到索引的映射
  edgeDynamicIdMap: Record<string, number> = {} // 边动态ID到索引的映射

  /**
   * 根据节点ID获取节点数据
   * @param nodeId - 节点ID
   * @returns 节点数据或undefined
   */
  getNode = (nodeId: string): RawNodeType | undefined => {
    const nodeIndex = this.nodeIdMap[nodeId]
    if (nodeIndex !== undefined) {
      return this.nodes[nodeIndex]
    }
    return undefined
  }

  /**
   * 根据边ID获取边数据
   * @param edgeId - 边ID
   * @param dynamicId - 是否使用动态ID查询
   * @returns 边数据或undefined
   */
  getEdge = (edgeId: string, dynamicId: boolean = true): RawEdgeType | undefined => {
    const edgeIndex = dynamicId ? this.edgeDynamicIdMap[edgeId] : this.edgeIdMap[edgeId]
    if (edgeIndex !== undefined) {
      return this.edges[edgeIndex]
    }
    return undefined
  }

  /**
   * 构建边的动态ID映射表
   *
   * 在边数据发生变化后需要调用此方法重建映射表
   */
  buildDynamicMap = () => {
    this.edgeDynamicIdMap = {}
    for (let i = 0; i < this.edges.length; i++) {
      const edge = this.edges[i]
      this.edgeDynamicIdMap[edge.dynamicId] = i
    }
  }
}