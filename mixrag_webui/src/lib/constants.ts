/**
 * 应用常量定义模块
 *
 * 集中管理应用中使用的所有常量：
 * 1. API和URL配置
 * 2. UI组件样式常量
 * 3. 图可视化相关常量
 * 4. 业务逻辑常量
 * 5. 系统配置常量
 */

import { ButtonVariantType } from '@/components/ui/Button'

// ==================== API和URL配置 ====================
export const backendBaseUrl = ''
export const webuiPrefix = '/webui/'

// ==================== UI组件样式常量 ====================
export const controlButtonVariant: ButtonVariantType = 'ghost'

// ==================== 图可视化颜色配置 ====================
// 标签颜色
export const labelColorDarkTheme = '#B2EBF2'
export const LabelColorHighlightedDarkTheme = '#000'

// 节点颜色
export const nodeColorDisabled = '#E2E2E2'
export const nodeBorderColor = '#EEEEEE'
export const nodeBorderColorSelected = '#F57F17'

// 边颜色
export const edgeColorDarkTheme = '#969696'
export const edgeColorSelected = '#F57F17'
export const edgeColorHighlighted = '#B2EBF2'

// ==================== 图可视化尺寸配置 ====================
export const minNodeSize = 4
export const maxNodeSize = 20

// ==================== 业务逻辑常量 ====================
export const searchResultLimit = 50
export const labelListLimit = 100

// ==================== 系统配置常量 ====================
export const healthCheckInterval = 15 // 健康检查间隔（秒）

// ==================== 分页配置 ====================
export const DEFAULT_PAGE_SIZE = 20
export const MAX_PAGE_SIZE = 100

// ==================== 文件上传配置 ====================
export const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
export const ALLOWED_FILE_TYPES = ['txt', 'pdf', 'docx', 'md', 'doc']

// ==================== 查询模式配置 ====================
export const QUERY_MODES = {
  NAIVE: 'naive',
  LOCAL: 'local',
  GLOBAL: 'global',
  mix: 'mix',
  MIX: 'mix',
  BYPASS: 'bypass'
} as const

// ==================== 任务状态配置 ====================
export const TASK_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const

// ==================== 流水线状态配置 ====================
export const PIPELINE_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const

export const defaultQueryLabel = '*'

// reference: https://developer.mozilla.org/en-US/docs/Web/HTTP/MIME_types/Common_types
export const supportedFileTypes = {
  'text/plain': [
    '.txt',
    '.md',
    '.html',
    '.htm',
    '.tex',
    '.json',
    '.xml',
    '.yaml',
    '.yml',
    '.rtf',
    '.odt',
    '.epub',
    '.csv',
    '.log',
    '.conf',
    '.ini',
    '.properties',
    '.sql',
    '.bat',
    '.sh',
    '.c',
    '.cpp',
    '.py',
    '.java',
    '.js',
    '.ts',
    '.swift',
    '.go',
    '.rb',
    '.php',
    '.css',
    '.scss',
    '.less'
  ],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']
}

export const SiteInfo = {
  name: 'MixRAG',
  home: '/',
  github: 'https://github.com/HKUDS/MixRAG'
}
