import React from 'react'
import { Routes, Route } from 'react-router-dom'

// 页面组件导入
import HomePage from '../pages/Home'
import TrainingPage from '../pages/Training'
import EvaluationPage from '../pages/Evaluation'
import ModelFineTuningPage from '../pages/ModelFineTuning'
import KnowledgeBasePage from '../pages/KnowledgeBase'
import DocumentPage from '../pages/Document'
import ChunkPage from '../pages/Chunk'
import GraphPage from '../pages/Graph'
import RetrievalPage from '../pages/Retrieval'
import TaskManagementPage from '../pages/TaskManagement'
import RAGEvaluationPage from '../pages/RAGEvaluation'
import ModelManagementPage from '../pages/ModelManagement'
import APIDocsPage from '../pages/APIDocs'

import KnowledgeBaseDetailPage from '../pages/KnowledgeBase/Detail';

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/training" element={<TrainingPage />} />
      <Route path="/evaluation" element={<EvaluationPage />} />
      <Route path="/model-finetuning" element={<ModelFineTuningPage />} />
      <Route path="/knowledge-base" element={<KnowledgeBasePage />} />
      <Route path="/knowledge-base/:id" element={<KnowledgeBaseDetailPage />} />
      <Route path="/retrieval" element={<RetrievalPage />} />
      <Route path="/task-management" element={<TaskManagementPage />} />
      <Route path="/rag-evaluation" element={<RAGEvaluationPage />} />
      <Route path="/model-management" element={<ModelManagementPage />} />
      <Route path="/api-docs" element={<APIDocsPage />} />
    </Routes>
  )
}

export default AppRoutes 