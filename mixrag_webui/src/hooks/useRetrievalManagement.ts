import { useState, useCallback, useRef, useEffect } from 'react'
import { message } from 'antd'
import {
  queryText,
  queryTextStream,
  getRetrievalHistory,
  saveRetrievalHistory,
  clearRetrievalHistory
} from '../api/retrievalApi'
import { useStreamResponse } from '../components/Retrieval/StreamHandler'

// 生成唯一ID的辅助函数
const generateUniqueId = () => {
  if (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') {
    return crypto.randomUUID()
  }
  return `id-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
}

export const useRetrievalManagement = () => {
  const [messages, setMessages] = useState([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [inputError, setInputError] = useState('')
  const [querySettings, setQuerySettings] = useState({
    mode: 'naive',
    response_format: '多段落',
    top_k: 10,
    similarity_threshold: 0.75,
    text_unit_max_tokens: 4000,
    global_context_max_tokens: 4000,
    history_turns: 3,
    user_prompt: '',
    context_only: false,
    prompt_only: false,
    stream: false
  })

  // 滚动相关的refs
  const shouldFollowScrollRef = useRef(true)
  const isFormInteractionRef = useRef(false)
  const programmaticScrollRef = useRef(false)
  const isReceivingResponseRef = useRef(false)
  const messagesEndRef = useRef(null)
  const messagesContainerRef = useRef(null)

  // 加载历史消息
  useEffect(() => {
    const loadHistory = async () => {
      try {
        const history = await getRetrievalHistory()
        if (Array.isArray(history) && history.length > 0) {
          const formattedHistory = history.map((msg, index) => ({
            ...msg,
            id: msg.id || `hist-${Date.now()}-${index}`,
            mermaidRendered: msg.mermaidRendered ?? true
          }))
          setMessages(formattedHistory)
        }
      } catch (error) {
        console.error('加载历史消息失败:', error)
      }
    }

    loadHistory()
  }, [])

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    programmaticScrollRef.current = true
    requestAnimationFrame(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'auto' })
      }
    })
  }, [])

  // 处理查询提交
  const handleSubmit = useCallback(
    async (e) => {
      e?.preventDefault()
      if (!inputValue.trim() || isLoading) return

      // 解析查询模式前缀
      const allowedModes = ['naive', 'global', 'mix']
      const prefixMatch = inputValue.match(/^\/(\w+)\s+(.+)/)
      let modeOverride = undefined
      let actualQuery = inputValue

      // 验证模式前缀
      if (/^\/\S+/.test(inputValue) && !prefixMatch) {
        setInputError('查询模式前缀格式无效，请使用: /模式 查询内容')
        return
      }

      if (prefixMatch) {
        const mode = prefixMatch[1]
        const query = prefixMatch[2]
        if (!allowedModes.includes(mode)) {
          setInputError(`不支持的查询模式。支持的模式: ${allowedModes.join(', ')}`)
          return
        }
        modeOverride = mode
        actualQuery = query
      }

      // 清除错误信息
      setInputError('')

      // 创建消息
      const userMessage = {
        id: generateUniqueId(),
        content: inputValue,
        role: 'user'
      }

      const assistantMessage = {
        id: generateUniqueId(),
        content: '',
        role: 'assistant',
        mermaidRendered: false
      }

      const prevMessages = [...messages]

      // 添加消息到聊天框
      setMessages([...prevMessages, userMessage, assistantMessage])

      // 重置滚动跟随状态
      shouldFollowScrollRef.current = true
      isReceivingResponseRef.current = true

      // 强制滚动到底部
      setTimeout(() => {
        scrollToBottom()
      }, 0)

      // 清空输入并设置加载状态
      setInputValue('')
      setIsLoading(true)

      // 更新助手消息的函数
      const updateAssistantMessage = (chunk, isError) => {
        assistantMessage.content += chunk

        // 检测Mermaid代码块
        const mermaidBlockRegex = /```mermaid\s+([\s\S]+?)```/g
        let mermaidRendered = false
        let match
        while ((match = mermaidBlockRegex.exec(assistantMessage.content)) !== null) {
          if (match[1] && match[1].trim().length > 10) {
            mermaidRendered = true
            break
          }
        }
        assistantMessage.mermaidRendered = mermaidRendered

        setMessages((prev) => {
          const newMessages = [...prev]
          const lastMessage = newMessages[newMessages.length - 1]
          if (lastMessage.role === 'assistant') {
            lastMessage.content = assistantMessage.content
            lastMessage.isError = isError
            lastMessage.mermaidRendered = assistantMessage.mermaidRendered
          }
          return newMessages
        })

        // 自动滚动
        if (shouldFollowScrollRef.current) {
          setTimeout(() => {
            scrollToBottom()
          }, 30)
        }
      }

      // 准备查询参数
      const queryParams = {
        ...querySettings,
        query: actualQuery,
        conversation_history: prevMessages
          .filter((m) => m.isError !== true)
          .slice(-(querySettings.history_turns || 0) * 2)
          .map((m) => ({ role: m.role, content: m.content })),
        ...(modeOverride ? { mode: modeOverride } : {})
      }

      try {
        // 执行查询
        if (querySettings.stream) {
          let errorMessage = ''
          await queryTextStream(queryParams, updateAssistantMessage, (error) => {
            errorMessage += error
          })
          if (errorMessage) {
            if (assistantMessage.content) {
              errorMessage = assistantMessage.content + '\n' + errorMessage
            }
            updateAssistantMessage(errorMessage, true)
          }
        } else {
          const response = await queryText(queryParams)
          updateAssistantMessage(response.data?.response || response.response || response.content || '没有收到回复')
        }
      } catch (err) {
        updateAssistantMessage(`查询失败\n${err.message}`, true)
        message.error('查询失败: ' + err.message)
      } finally {
        setIsLoading(false)
        isReceivingResponseRef.current = false
        
        // 保存历史记录
        const finalMessages = [...prevMessages, userMessage, assistantMessage]
        setMessages(finalMessages)
        saveRetrievalHistory(finalMessages)
      }
    },
    [inputValue, isLoading, messages, querySettings, scrollToBottom]
  )

  // 清除消息
  const clearMessages = useCallback(async () => {
    setMessages([])
    try {
      await clearRetrievalHistory()
      message.success('聊天记录已清除')
    } catch (error) {
      console.error('清除历史记录失败:', error)
    }
  }, [])

  // 处理设置变更
  const handleSettingsChange = useCallback((newSettings) => {
    setQuerySettings(newSettings)
  }, [])

  return {
    // 状态
    messages,
    inputValue,
    isLoading,
    inputError,
    querySettings,

    // Refs
    messagesEndRef,
    messagesContainerRef,
    shouldFollowScrollRef,
    isFormInteractionRef,
    programmaticScrollRef,
    isReceivingResponseRef,

    // 方法
    setInputValue,
    setInputError,
    setMessages,
    handleSubmit,
    clearMessages,
    handleSettingsChange,
    scrollToBottom
  }
} 