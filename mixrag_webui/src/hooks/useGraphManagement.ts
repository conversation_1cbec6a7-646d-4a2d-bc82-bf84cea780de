import { useState, useCallback, useEffect } from 'react'
import { message } from 'antd'
import {
  fetchGraphStats,
  checkGraphHealth,
  fetchSubgraph
} from '../api/graphApi'

// 图管理主Hook
export const useGraphManagement = () => {
  const [loading, setLoading] = useState(false)
  const [graphStats, setGraphStats] = useState(null)
  const [healthStatus, setHealthStatus] = useState(null)

  // 获取图统计信息
  const getGraphStats = useCallback(async (silent = false) => {
    try {
      setLoading(true)
      const response = await fetchGraphStats()
      if (response.success) {
        setGraphStats(response.data)
        if (!silent) {
          message.success('获取图统计信息成功')
        }
      } else {
        message.error(response.message || '获取图统计信息失败')
      }
      return response
    } catch (error) {
      console.error('获取图统计信息失败:', error)
      message.error('获取图统计信息失败: ' + error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }, [])

  // 检查健康状态
  const checkHealth = useCallback(async () => {
    try {
      const response = await checkGraphHealth()
      setHealthStatus(response)
      return response
    } catch (error) {
      console.error('健康检查失败:', error)
      setHealthStatus({ status: 'unhealthy', message: error.message })
      return { status: 'unhealthy', message: error.message }
    }
  }, [])

  // 初始化时检查健康状态
  useEffect(() => {
    checkHealth()
  }, [checkHealth])

  return {
    loading,
    graphStats,
    healthStatus,
    getGraphStats,
    checkHealth
  }
}

// 子图查询Hook - 为GraphVisualizer组件提供支持
export const useSubgraphQuery = () => {
  const [loading, setLoading] = useState(false)
  const [subgraph, setSubgraph] = useState(null)

  const getSubgraph = useCallback(async (params) => {
    try {
      setLoading(true)
      const response = await fetchSubgraph(params)
      if (response.success) {
        setSubgraph(response.data)
        message.success(`获取子图成功，包含 ${response.data?.node_count || 0} 个节点`)
      } else {
        message.error(response.message || '获取子图失败')
        setSubgraph(null)
      }
      return response
    } catch (error) {
      console.error('获取子图失败:', error)
      message.error('获取子图失败: ' + error.message)
      setSubgraph(null)
      throw error
    } finally {
      setLoading(false)
    }
  }, [])

  const clearSubgraph = useCallback(() => {
    setSubgraph(null)
  }, [])

  return {
    loading,
    subgraph,
    getSubgraph,
    clearSubgraph
  }
} 