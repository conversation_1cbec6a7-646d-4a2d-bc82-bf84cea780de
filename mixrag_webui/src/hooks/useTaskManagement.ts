import { useState, useEffect } from 'react'
import { message } from 'antd'
import { 
  fetchTasks, 
  fetchTaskDetail, 
  createTask, 
  cancelTask 
} from '../api/taskApi'
import { parsePaginationResponse } from '../utils/pagination'

export const useTaskManagement = () => {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(false)
  const [createLoading, setCreateLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [statusFilter, setStatusFilter] = useState(null)
  const [typeFilter, setTypeFilter] = useState(null)
  const [selectedTask, setSelectedTask] = useState(null)

  // 获取任务列表
  const loadTasks = async () => {
    setLoading(true)
    try {
      const response = await fetchTasks({
        page: currentPage,
        pageSize,
        status: statusFilter,
        taskType: typeFilter
      })
      
      // 使用工具函数解析分页响应
      const { data, pagination } = parsePaginationResponse(response)
      setTasks(data)
      setTotal(pagination.total)
    } catch (error) {
      message.error(error.message || '获取任务列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 查看任务详情
  const loadTaskDetail = async (taskId) => {
    try {
      const data = await fetchTaskDetail(taskId)
      setSelectedTask(data)
      return data
    } catch (error) {
      message.error(error.message || '获取任务详情失败')
      throw error
    }
  }

  // 创建任务
  const handleCreateTask = async (taskData) => {
    setCreateLoading(true)
    try {
      const data = await createTask(taskData)
      message.success(`任务创建成功，ID: ${data.id}`)
      loadTasks() // 刷新列表
      return data
    } catch (error) {
      message.error(error.message || '创建任务失败')
      throw error
    } finally {
      setCreateLoading(false)
    }
  }

  // 取消任务
  const handleCancelTask = async (taskId) => {
    try {
      await cancelTask(taskId)
      message.success('任务取消成功')
      loadTasks() // 刷新列表
    } catch (error) {
      message.error(error.message || '取消任务失败')
    }
  }

  // 当筛选条件或分页参数变化时重新加载
  useEffect(() => {
    loadTasks()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, pageSize, statusFilter, typeFilter])

  return {
    // 数据状态
    tasks,
    loading,
    createLoading,
    total,
    currentPage,
    pageSize,
    statusFilter,
    typeFilter,
    selectedTask,
    
    // 操作方法
    loadTasks,
    loadTaskDetail,
    handleCreateTask,
    handleCancelTask,
    
    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setStatusFilter,
    setTypeFilter,
    setSelectedTask
  }
} 