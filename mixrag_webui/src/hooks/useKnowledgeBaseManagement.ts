import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import {
  fetchKnowledgeBases,
  fetchKnowledgeBaseDetail,
  createKnowledgeBase,
  updateKnowledgeBase,
  deleteKnowledgeBase,
  fetchKnowledgeBaseStats,
  searchKnowledgeBases,
  KnowledgeBase,
  KnowledgeBaseDetail,
  KnowledgeBaseStats,
  CreateKnowledgeBaseRequest,
  UpdateKnowledgeBaseRequest
} from '../api/knowledgeBaseApi'
import { parsePaginationResponse } from '../utils/pagination'

export const useKnowledgeBaseManagement = () => {
  // 基础状态
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(8) // 4x2 卡片展示，每页8个
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBaseDetail | null>(null)
  const [stats, setStats] = useState<KnowledgeBaseStats | null>(null)
  
  // 搜索和过滤状态
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('create_time')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  
  // 操作状态
  const [createLoading, setCreateLoading] = useState(false)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)

  // 获取统计信息
  const loadStats = useCallback(async () => {
    console.log('📊 Hook: loadStats 被调用')
    try {
      console.log('📡 Hook: 调用API fetchKnowledgeBaseStats...')
      const response = await fetchKnowledgeBaseStats()
      console.log('✅ Hook: 统计API调用成功', response)
      // 总是使用data字段，即使success为false也可能有默认数据
      const data = response?.data || response
      console.log('📈 Hook: 设置统计数据', data)
      setStats(data)
    } catch (error: any) {
      console.error('❌ Hook: 统计API调用失败', error)
      message.error(error.message || '获取统计信息失败')
    }
  }, [])

  // 获取知识库列表
  const loadKnowledgeBases = useCallback(async () => {
    console.log('📋 Hook: loadKnowledgeBases 被调用')
    setLoading(true)
    try {
      const params = {
        page: currentPage,
        pageSize,
        search: searchQuery,
        sortBy,
        sortOrder
      }
      console.log('📡 Hook: 调用API fetchKnowledgeBases...', params)
      const response = await fetchKnowledgeBases(params)
      console.log('✅ Hook: 知识库列表API调用成功', response)

      // 使用工具函数解析分页响应
      const { data, pagination } = parsePaginationResponse(response)
      console.log('📊 Hook: 解析后的数据', { data, pagination })
      setKnowledgeBases(data)
      setTotal(pagination.total)
    } catch (error: any) {
      console.error('❌ Hook: 知识库列表API调用失败', error)
      message.error(error.message || '获取知识库列表失败')
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchQuery, sortBy, sortOrder])

  // 获取知识库详情
  const loadKnowledgeBaseDetail = useCallback(async (id: string) => {
    try {
      const response = await fetchKnowledgeBaseDetail(id)
      // 提取响应中的data字段
      const data = response?.data || response
      setSelectedKnowledgeBase(data)
      return data
    } catch (error: any) {
      message.error(error.message || '获取知识库详情失败')
      throw error
    }
  }, [])

  // 创建知识库
  const handleCreateKnowledgeBase = useCallback(async (data: CreateKnowledgeBaseRequest) => {
    setCreateLoading(true)
    try {
      const response = await createKnowledgeBase(data)
      message.success('知识库创建成功')
      setCurrentPage(1) // 返回第一页
      loadKnowledgeBases() // 刷新列表
      loadStats() // 刷新统计
      return response
    } catch (error: any) {
      message.error(error.message || '创建知识库失败')
      throw error
    } finally {
      setCreateLoading(false)
    }
  }, [loadKnowledgeBases, loadStats])

  // 更新知识库
  const handleUpdateKnowledgeBase = useCallback(async (id: string, data: UpdateKnowledgeBaseRequest) => {
    setUpdateLoading(true)
    try {
      const response = await updateKnowledgeBase(id, data)
      message.success('知识库更新成功')
      loadKnowledgeBases() // 刷新列表
      return response
    } catch (error: any) {
      message.error(error.message || '更新知识库失败')
      throw error
    } finally {
      setUpdateLoading(false)
    }
  }, [loadKnowledgeBases])

  // 删除知识库
  const handleDeleteKnowledgeBase = useCallback(async (id: string) => {
    setDeleteLoading(true)
    try {
      await deleteKnowledgeBase(id)
      message.success('知识库删除成功')
      await loadKnowledgeBases() // 刷新列表
      await loadStats() // 刷新统计
    } catch (error: any) {
      message.error(error.message || '删除知识库失败')
      throw error
    } finally {
      setDeleteLoading(false)
    }
  }, [loadKnowledgeBases])



  // 搜索知识库
  const handleSearch = useCallback(async (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1) // 重置到第一页
  }, [])

  // 重置搜索
  const resetSearch = useCallback(() => {
    setSearchQuery('')
    setCurrentPage(1)
  }, [])

  // 页面变化时重新加载数据
  useEffect(() => {
    loadKnowledgeBases()
  }, [loadKnowledgeBases])

  return {
    // 数据状态
    knowledgeBases,
    loading,
    total,
    currentPage,
    pageSize,
    selectedKnowledgeBase,
    stats,
    
    // 搜索和过滤状态
    searchQuery,
    sortBy,
    sortOrder,
    
    // 操作状态
    createLoading,
    updateLoading,
    deleteLoading,
    
    // 操作方法
    loadKnowledgeBases,
    loadKnowledgeBaseDetail,
    handleCreateKnowledgeBase,
    handleUpdateKnowledgeBase,
    handleDeleteKnowledgeBase,
    loadStats,
    handleSearch,
    resetSearch,
    
    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setSelectedKnowledgeBase,
    setSortBy,
    setSortOrder
  }
}
