import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import { 
  getPipelineList, 
  getPipelineDetail, 
  getPipelineTasks,
  cancelPipeline,
  getPipelineStatistics,
  getExecutorStatus,
  cleanupCompletedPipelines,
  restartPipeline,
  resumePipeline,
  refreshExecutor,
  getPipelineLogs
} from '../api/pipelineApi'

export const usePipelineManagement = () => {
  // 数据状态
  const [pipelines, setPipelines] = useState([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [statusFilter, setStatusFilter] = useState('')
  const [createdByFilter, setCreatedByFilter] = useState('')
  const [selectedPipeline, setSelectedPipeline] = useState(null)
  const [pipelineTasks, setPipelineTasks] = useState([])
  const [statistics, setStatistics] = useState({})
  const [executorStatus, setExecutorStatus] = useState({})

  // 操作状态
  const [cancelLoading, setCancelLoading] = useState(false)
  const [detailLoading, setDetailLoading] = useState(false)
  const [tasksLoading, setTasksLoading] = useState(false)
  const [cleanupLoading, setCleanupLoading] = useState(false)
  const [restartLoading, setRestartLoading] = useState(false)
  const [refreshLoading, setRefreshLoading] = useState(false)
  const [logsLoading, setLogsLoading] = useState(false)

  // 加载流水线列表
  const loadPipelines = async () => {
    setLoading(true)
    try {
      const response = await getPipelineList({
        status: statusFilter || undefined,
        created_by: createdByFilter || undefined,
        page: currentPage,
        page_size: pageSize
      })

      if (response.success) {
        setPipelines(response.data.records || [])
        setTotal(response.data.pagination?.total || 0)
      } else {
        message.error(response.message || '获取流水线列表失败')
      }
    } catch (error) {
      console.error('获取流水线列表失败:', error)
      message.error('获取流水线列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载流水线详情
  const loadPipelineDetail = async (pipelineId) => {
    setDetailLoading(true)
    try {
      const response = await getPipelineDetail(pipelineId)
      if (response.success) {
        setSelectedPipeline(response.data)
        return response.data
      } else {
        message.error(response.message || '获取流水线详情失败')
        return null
      }
    } catch (error) {
      console.error('获取流水线详情失败:', error)
      message.error('获取流水线详情失败')
      return null
    } finally {
      setDetailLoading(false)
    }
  }

  // 加载流水线任务列表
  const loadPipelineTasks = async (pipelineId) => {
    setTasksLoading(true)
    try {
      const response = await getPipelineTasks(pipelineId)
      if (response.success) {
        setPipelineTasks(response.data.tasks || [])
        return response.data.tasks || []
      } else {
        message.error(response.message || '获取任务列表失败')
        return []
      }
    } catch (error) {
      console.error('获取任务列表失败:', error)
      message.error('获取任务列表失败')
      return []
    } finally {
      setTasksLoading(false)
    }
  }

  // 取消流水线
  const handleCancelPipeline = async (pipelineId) => {
    setCancelLoading(true)
    try {
      const response = await cancelPipeline(pipelineId)
      if (response.success) {
        message.success('流水线已取消')
        await loadPipelines() // 重新加载列表
        return true
      } else {
        message.error(response.message || '取消流水线失败')
        return false
      }
    } catch (error) {
      console.error('取消流水线失败:', error)
      message.error('取消流水线失败')
      return false
    } finally {
      setCancelLoading(false)
    }
  }

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await getPipelineStatistics()
      if (response.success) {
        setStatistics(response.data || {})
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  // 加载执行器状态
  const loadExecutorStatus = async () => {
    try {
      const response = await getExecutorStatus()
      if (response.success) {
        setExecutorStatus(response.data || {})
      }
    } catch (error) {
      console.error('获取执行器状态失败:', error)
    }
  }

  // 重启流水线
  const handleRestartPipeline = async (pipelineId) => {
    setRestartLoading(true)
    try {
      const response = await restartPipeline(pipelineId)
      if (response.success) {
        message.success(response.message || '流水线已重启')
        await loadPipelines() // 重新加载列表
        return true
      } else {
        message.error(response.message || '重启流水线失败')
        return false
      }
    } catch (error) {
      console.error('重启流水线失败:', error)
      message.error('重启流水线失败')
      return false
    } finally {
      setRestartLoading(false)
    }
  }

  // 恢复流水线
  const [resumeLoading, setResumeLoading] = useState(false)
  const handleResumePipeline = async (pipelineId) => {
    setResumeLoading(true)
    try {
      const response = await resumePipeline(pipelineId)
      if (response.success) {
        message.success(response.message || '流水线已恢复')
        await loadPipelines() // 重新加载列表
        return true
      } else {
        message.error(response.message || '恢复流水线失败')
        return false
      }
    } catch (error) {
      console.error('恢复流水线失败:', error)
      message.error('恢复流水线失败')
      return false
    } finally {
      setResumeLoading(false)
    }
  }

  // 刷新执行器
  const handleRefreshExecutor = async () => {
    setRefreshLoading(true)
    try {
      const response = await refreshExecutor()
      if (response.success) {
        message.success(response.message || '执行器已刷新')
        await loadExecutorStatus() // 重新加载执行器状态
        return true
      } else {
        message.error(response.message || '刷新执行器失败')
        return false
      }
    } catch (error) {
      console.error('刷新执行器失败:', error)
      message.error('刷新执行器失败')
      return false
    } finally {
      setRefreshLoading(false)
    }
  }

  // 获取流水线日志
  const loadPipelineLogs = useCallback(async (pipelineId) => {
    setLogsLoading(true)
    try {
      const response = await getPipelineLogs(pipelineId)
      if (response.success) {
        return response.data.logs || []
      } else {
        message.error(response.message || '获取日志失败')
        return []
      }
    } catch (error) {
      console.error('获取日志失败:', error)
      message.error('获取日志失败')
      return []
    } finally {
      setLogsLoading(false)
    }
  }, [])

  // 清理已完成的流水线
  const handleCleanupCompleted = async (keepDays = 7) => {
    setCleanupLoading(true)
    try {
      const response = await cleanupCompletedPipelines(keepDays)
      if (response.success) {
        message.success(`发现 ${response.data.cleaned_count} 个可清理的流水线`)
        await loadPipelines() // 重新加载列表
        return true
      } else {
        message.error(response.message || '清理失败')
        return false
      }
    } catch (error) {
      console.error('清理失败:', error)
      message.error('清理失败')
      return false
    } finally {
      setCleanupLoading(false)
    }
  }

  // 监听筛选条件变化，重新加载数据
  useEffect(() => {
    loadPipelines()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, pageSize, statusFilter, createdByFilter])

  // 初始化时加载统计信息和执行器状态
  useEffect(() => {
    loadStatistics()
    loadExecutorStatus()
  }, [])

  return {
    // 数据状态
    pipelines,
    loading,
    total,
    currentPage,
    pageSize,
    statusFilter,
    createdByFilter,
    selectedPipeline,
    pipelineTasks,
    statistics,
    executorStatus,

    // 操作状态
    cancelLoading,
    detailLoading,
    tasksLoading,
    cleanupLoading,
    restartLoading,
    resumeLoading,
    refreshLoading,
    logsLoading,

    // 操作方法
    loadPipelines,
    loadPipelineDetail,
    loadPipelineTasks,
    handleCancelPipeline,
    handleRestartPipeline,
    handleResumePipeline,
    handleRefreshExecutor,
    loadPipelineLogs,
    loadStatistics,
    loadExecutorStatus,
    handleCleanupCompleted,

    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setStatusFilter,
    setCreatedByFilter,
    setSelectedPipeline,
    setPipelineTasks
  }
} 