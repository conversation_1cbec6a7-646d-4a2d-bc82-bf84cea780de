/**
 * Pipeline API 客户端
 */
import { request } from './index'

// 获取流水线列表
export const getPipelineList = async (params = {}) => {
  const { status, created_by, page = 1, page_size = 20 } = params
  
  const queryParams = new URLSearchParams()
  if (status) queryParams.append('status', status)
  if (created_by) queryParams.append('created_by', created_by)
  queryParams.append('page', page)
  queryParams.append('page_size', page_size)
  
  return await request(`/pipelines/list?${queryParams.toString()}`)
}

// 获取流水线详情
export const getPipelineDetail = async (pipelineId) => {
  return await request(`/pipelines/${pipelineId}`)
}

// 获取流水线任务列表
export const getPipelineTasks = async (pipelineId) => {
  return await request(`/pipelines/${pipelineId}/tasks`)
}

// 取消流水线
export const cancelPipeline = async (pipelineId) => {
  return await request(`/pipelines/${pipelineId}/cancel`, {
    method: 'POST'
  })
}

// 获取流水线统计信息
export const getPipelineStatistics = async () => {
  return await request('/pipelines/statistics/overview')
}

// 获取执行器状态
export const getExecutorStatus = async () => {
  return await request('/pipelines/executor/status')
}

// 健康检查
export const healthCheck = async () => {
  return await request('/pipelines/executor/health')
}

// 重启流水线
export const restartPipeline = async (pipelineId) => {
  return await request(`/pipelines/${pipelineId}/restart`, {
    method: 'POST'
  })
}

// 恢复流水线
export const resumePipeline = async (pipelineId) => {
  return await request(`/pipelines/${pipelineId}/resume`, {
    method: 'POST'
  })
}

// 刷新执行器
export const refreshExecutor = async () => {
  return await request('/pipelines/executor/refresh', {
    method: 'POST'
  })
}

// 获取流水线日志
export const getPipelineLogs = async (pipelineId) => {
  return await request(`/pipelines/${pipelineId}/logs`)
}

// 清理已完成的流水线
export const cleanupCompletedPipelines = async (keepDays = 7) => {
  return await request(`/pipelines/cleanup/completed?keep_days=${keepDays}`, {
    method: 'POST'
  })
}

// 流水线状态常量
export const PIPELINE_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running', 
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
}

// 流水线状态中文映射
export const PIPELINE_STATUS_MAP = {
  [PIPELINE_STATUS.PENDING]: '等待中',
  [PIPELINE_STATUS.RUNNING]: '运行中',
  [PIPELINE_STATUS.COMPLETED]: '已完成',
  [PIPELINE_STATUS.FAILED]: '失败',
  [PIPELINE_STATUS.CANCELLED]: '已取消'
}

// 任务状态常量
export const TASK_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed', 
  FAILED: 'failed',
  CANCELLED: 'cancelled'
}

// 任务状态中文映射
export const TASK_STATUS_MAP = {
  [TASK_STATUS.PENDING]: '等待中',
  [TASK_STATUS.RUNNING]: '运行中',
  [TASK_STATUS.COMPLETED]: '已完成',
  [TASK_STATUS.FAILED]: '失败',
  [TASK_STATUS.CANCELLED]: '已取消'
} 