import { apiRequest } from './index.js'

// 获取任务列表
export const fetchTasks = async (params = {}) => {
  const searchParams = new URLSearchParams()
  
  // 添加分页参数
  if (params.page) searchParams.append('page', params.page)
  if (params.pageSize) searchParams.append('page_size', params.pageSize)
  
  // 添加筛选参数
  if (params.status) searchParams.append('status', params.status)
  if (params.taskType) searchParams.append('task_type', params.taskType)
  
  const queryString = searchParams.toString()
  const url = queryString ? `/tasks?${queryString}` : '/tasks'
  
  return await apiRequest(url)
}

// 获取任务详情
export const fetchTaskDetail = async (taskId) => {
  return await apiRequest(`/tasks/${taskId}`)
}

// 创建任务
export const createTask = async (taskData) => {
  return await apiRequest('/tasks', {
    method: 'POST',
    body: JSON.stringify(taskData)
  })
}

// 取消任务
export const cancelTask = async (taskId) => {
  return await apiRequest(`/tasks/${taskId}`, {
    method: 'DELETE'
  })
}

// 重试任务
export const retryTask = async (taskId) => {
  return await apiRequest(`/tasks/${taskId}/retry`, {
    method: 'POST'
  })
}

export default {
  fetchTasks,
  fetchTaskDetail,
  createTask,
  cancelTask,
  retryTask
} 