/**
 * API 基础类
 * 
 * 提供通用的 CRUD 操作和错误处理
 */

import { apiRequest, ApiResponse, RequestOptions } from './index'
import { buildPaginationParams } from '../utils/pagination'

export abstract class BaseApi {
  protected baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  /**
   * GET 请求
   */
  protected async get<T>(
    endpoint: string = '', 
    params?: Record<string, any>
  ): Promise<T> {
    const url = this.buildUrl(endpoint)
    return apiRequest<T>(url, { method: 'GET', params })
  }

  /**
   * POST 请求
   */
  protected async post<T>(
    endpoint: string = '', 
    data?: any, 
    options?: RequestOptions
  ): Promise<T> {
    const url = this.buildUrl(endpoint)
    return apiRequest<T>(url, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options
    })
  }

  /**
   * PUT 请求
   */
  protected async put<T>(
    endpoint: string = '', 
    data?: any, 
    options?: RequestOptions
  ): Promise<T> {
    const url = this.buildUrl(endpoint)
    return apiRequest<T>(url, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options
    })
  }

  /**
   * DELETE 请求
   */
  protected async delete<T>(
    endpoint: string = '', 
    options?: RequestOptions
  ): Promise<T> {
    const url = this.buildUrl(endpoint)
    return apiRequest<T>(url, { method: 'DELETE', ...options })
  }

  /**
   * 文件上传
   */
  protected async upload<T>(
    endpoint: string = '', 
    formData: FormData, 
    options?: RequestOptions
  ): Promise<T> {
    const url = this.buildUrl(endpoint)
    return apiRequest<T>(url, {
      method: 'POST',
      body: formData,
      headers: {
        // 不设置 Content-Type，让浏览器自动设置
      },
      ...options
    })
  }

  /**
   * 构建完整的 URL
   */
  private buildUrl(endpoint: string): string {
    if (!endpoint) return this.baseUrl
    return endpoint.startsWith('/') ? `${this.baseUrl}${endpoint}` : `${this.baseUrl}/${endpoint}`
  }

  /**
   * 构建分页参数 - 使用统一的分页工具函数
   */
  protected buildPaginationParams(params: {
    page?: number;
    pageSize?: number;
    filters?: Record<string, any>;
  }) {
    return buildPaginationParams(params)
  }
}

/**
 * 通用的列表响应类型
 */
export interface ListResponse<T> extends ApiResponse<T[]> {
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 通用的分页参数类型
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

/**
 * 通用的过滤参数类型
 */
export interface FilterParams {
  [key: string]: any;
}
