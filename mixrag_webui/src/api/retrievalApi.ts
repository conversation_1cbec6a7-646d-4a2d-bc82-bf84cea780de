import { apiRequest } from './index'

// 检索查询（非流式）
export const queryText = async (params) => {
  return await apiRequest('/query', {
    method: 'POST',
    body: JSON.stringify(params)
  })
}

// 检索查询（流式）
export const queryTextStream = async (params, onChunk, onError) => {
  try {
    const response = await fetch('/api/v1/query/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `请求失败: ${response.status}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.error) {
                onError?.(data.error)
              } else if (data.content) {
                onChunk?.(data.content)
              } else if (data.delta) {
                onChunk?.(data.delta)
              }
            } catch (e) { // 忽略JSON解析错误
              console.warn('Failed to parse SSE data:', line, e)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  } catch (error) {
    console.error('Stream request failed:', error)
    onError?.(error.message)
    throw error
  }
}

// 获取检索历史
export const getRetrievalHistory = async () => {
  try {
    return await apiRequest('/retrieval/history', {
      method: 'GET'
    })
  } catch (error) {
    console.warn('Failed to get retrieval history:', error)
    return []
  }
}

// 保存检索历史
export const saveRetrievalHistory = async (history) => {
  try {
    return await apiRequest('/retrieval/history', {
      method: 'POST',
      body: JSON.stringify({ history })
    })
  } catch (error) {
    console.warn('Failed to save retrieval history:', error)
  }
}

// 清除检索历史
export const clearRetrievalHistory = async () => {
  try {
    return await apiRequest('/retrieval/history', {
      method: 'DELETE'
    })
  } catch (error) {
    console.warn('Failed to clear retrieval history:', error)
  }
} 