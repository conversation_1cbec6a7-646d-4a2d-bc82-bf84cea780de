import { request } from './index'

// 获取chunk列表
export const getChunkList = async (params = {}) => {
  return await request('/chunks', {
    method: 'GET',
    params: params
  })
}

// 获取chunk统计信息
export const getChunkStats = () => {
  return request('/chunks/stats', {
    method: 'GET'
  })
}

// 按文档ID获取chunk列表
export const getChunksByDocument = (docId, params = {}) => {
  return request(`/chunks/by-document/${docId}`, {
    method: 'GET',
    params
  })
}

// 相似度搜索chunk
export const searchChunksBySimilarity = (data) => {
  return request('/chunks/similarity-search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
}

// 关键词搜索chunk
export const searchChunksByKeywords = (data) => {
  return request('/chunks/keyword-search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
} 