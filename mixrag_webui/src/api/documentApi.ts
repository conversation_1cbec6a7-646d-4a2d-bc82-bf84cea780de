/**
 * 文档管理API模块
 *
 * 提供以下功能：
 * 1. 文档列表查询和分页
 * 2. 文档上传和下载
 * 3. 文档处理状态管理
 * 4. 文档统计信息获取
 * 5. 文档删除和批量操作
 */

import { BaseApi, ListResponse, PaginationParams } from './base'

// 类型定义
export interface Document {
  doc_id: string;
  original_filename: string;
  file_size: number;
  upload_time: string;
  upload_status: 'pending' | 'uploaded' | 'failed';
  process_status: 'pending' | 'processing' | 'completed' | 'failed';
  uploaded_by?: string;
  file_type?: string;
  chunks_count?: number;
}

export interface DocumentListParams extends PaginationParams {
  uploadStatus?: string;
  processStatus?: string;
}

export interface DocumentStats {
  total_documents: number;
  total_size: number;
  upload_success_count: number;
  upload_failed_count: number;
  process_success_count: number;
  process_failed_count: number;
}

/**
 * 文档 API 类
 */
class DocumentApi extends BaseApi {
  constructor() {
    super('/documents')
  }

  /**
   * 获取文档列表
   */
  async getDocuments(params: DocumentListParams = {}): Promise<ListResponse<Document>> {
    const queryParams = this.buildPaginationParams({
      page: params.page,
      pageSize: params.pageSize,
      filters: {
        upload_status: params.uploadStatus,
        process_status: params.processStatus,
        kb_id: params.kb_id
      }
    })

    return this.get<ListResponse<Document>>('/list', queryParams)
  }

  /**
   * 获取文档详情
   */
  async getDocument(docId: string): Promise<Document> {
    return this.get<Document>(`/${docId}`)
  }

  /**
   * 上传文档
   */
  async uploadDocument(file: File, uploadedBy?: string): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    if (uploadedBy) {
      formData.append('uploaded_by', uploadedBy)
    }

    return this.upload('/upload', formData)
  }

  /**
   * 删除文档
   */
  async deleteDocument(docId: string): Promise<any> {
    return this.delete(`/${docId}`)
  }

  /**
   * 处理文档
   */
  async processDocument(docId: string, forceReprocess = false): Promise<any> {
    return this.post('/process', {
      doc_id: docId,
      force_reprocess: forceReprocess
    })
  }

  /**
   * 获取文档统计
   */
  async getStats(): Promise<DocumentStats> {
    return this.get<DocumentStats>('/stats')
  }

  /**
   * 获取文档处理统计
   */
  async getProcessingStats(docId: string): Promise<any> {
    return this.get(`/${docId}/stats`)
  }

  /**
   * 下载文档
   */
  async downloadDocument(docId: string, originalFilename?: string): Promise<{ success: boolean; filename: string }> {
    try {
      const response = await fetch(`/api/v1/upload/${docId}/download`, {
        method: 'GET'
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `下载失败: ${response.status}`)
      }

      // 获取文件名，优先使用响应头中的文件名
      let filename = originalFilename || 'download'
      const contentDisposition = response.headers.get('Content-Disposition')
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }

      // 获取文件blob
      const blob = await response.blob()

      // 创建下载链接并自动点击
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename

      // 添加到DOM，点击，然后移除
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)

      return { success: true, filename }
    } catch (error) {
      console.error('文档下载失败:', error)
      throw error
    }
  }
}

// 创建实例
const documentApi = new DocumentApi()

// 导出兼容的函数接口
export const fetchDocuments = (params?: DocumentListParams) => documentApi.getDocuments(params)
export const fetchDocumentDetail = (docId: string) => documentApi.getDocument(docId)
export const uploadDocument = (file: File, uploadedBy?: string) => documentApi.uploadDocument(file, uploadedBy)
export const deleteDocument = (docId: string) => documentApi.deleteDocument(docId)
export const processDocument = (docId: string, forceReprocess?: boolean) => documentApi.processDocument(docId, forceReprocess)
export const fetchDocumentStats = () => documentApi.getStats()
export const fetchDocumentProcessingStats = (docId: string) => documentApi.getProcessingStats(docId)
export const downloadDocument = (docId: string, originalFilename?: string) => documentApi.downloadDocument(docId, originalFilename)

// 默认导出
export default documentApi