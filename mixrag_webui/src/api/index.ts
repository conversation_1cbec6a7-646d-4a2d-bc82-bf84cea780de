/**
 * 统一的HTTP客户端模块
 *
 * 提供以下功能：
 * 1. 统一的API请求封装
 * 2. 请求和响应拦截器
 * 3. 错误处理和重试机制
 * 4. 请求参数序列化
 * 5. 认证token管理
 */

// 类型定义
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  success?: boolean;
  total?: number;
  page?: number;
  pageSize?: number;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export interface RequestOptions extends RequestInit {
  params?: Record<string, any>;
  timeout?: number;
  retries?: number;
}

// API基础配置
const API_BASE_URL = '/api/v1'

/**
 * 序列化URL参数
 * @param params - 参数对象
 * @returns 序列化后的查询字符串
 */
const serializeParams = (params: Record<string, any>): string => {
  if (!params || typeof params !== 'object') {
    return ''
  }

  const searchParams = new URLSearchParams()

  Object.keys(params).forEach(key => {
    const value = params[key]
    // 只添加非空值，并确保值被正确转换为字符串
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value))
    }
  })

  const paramString = searchParams.toString()
  return paramString ? `?${paramString}` : ''
}

/**
 * 获取认证headers
 * @returns 认证相关的headers
 */
const getAuthHeaders = (): Record<string, string> => {
  const headers: Record<string, string> = {}

  // 从localStorage获取token
  const token = localStorage.getItem('MIXRAG-API-TOKEN')
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  // 从localStorage获取API Key
  const apiKey = localStorage.getItem('API_KEY')
  if (apiKey) {
    headers['X-API-Key'] = apiKey
  }

  return headers
}

/**
 * 统一的API请求函数
 * @param url - 请求URL
 * @param options - 请求选项
 * @returns 请求结果
 */
export const apiRequest = async <T = any>(
  url: string,
  options: RequestOptions = {}
): Promise<T> => {
  // 导入日志和错误处理工具
  const { createLogger } = await import('../utils/logger')
  const { handleApiError } = await import('../utils/errorHandler')

  const logger = createLogger('API')

  try {
    let requestUrl = `${API_BASE_URL}${url}`

    // 处理GET请求的参数
    if ((!options.method || options.method === 'GET') && options.params) {
      const paramString = serializeParams(options.params)
      requestUrl += paramString
    }

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders(),
        ...options.headers
      },
      method: options.method || 'GET',
      ...options
    }

    // 删除自定义属性，避免fetch处理时出错
    delete (config as any).params
    delete (config as any).timeout
    delete (config as any).retries

    logger.debug('API请求开始:', config.method, requestUrl)

    const response = await fetch(requestUrl, config)

    // 处理认证错误
    if (response.status === 401) {
      logger.warn('认证失败，可能需要重新登录')
      // 可以在这里触发登录流程
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.detail || errorData.error || `请求失败: ${response.status}`
      const error = new Error(`${response.status} ${errorMessage}`) as ApiError
      error.status = response.status

      // 使用统一错误处理
      handleApiError(error, {
        context: {
          url: requestUrl,
          method: config.method,
          status: response.status
        }
      })

      throw error
    }

    const data = await response.json()

    // 检查业务逻辑是否成功
    if (data.success === false) {
      const errorMessage = data.message || '操作失败，但未提供具体错误信息。'
      const error = new Error(errorMessage) as ApiError
      error.status = response.status // 可以保留HTTP状态码
      error.details = data.data // 可以将响应的data部分作为错误的详情

      handleApiError(error, {
        context: {
          url: requestUrl,
          method: config.method,
          status: response.status
        }
      })

      throw error
    }

    logger.debug('API请求成功:', config.method, requestUrl)
    return data
  } catch (error) {
    // 如果不是HTTP错误，也进行统一处理
    if (!(error as ApiError).status) {
      handleApiError(error as Error, {
        context: {
          url: `${API_BASE_URL}${url}`,
          method: options.method || 'GET'
        }
      })
    }
    throw error
  }
}

/**
 * 通用请求函数（与apiRequest相同，提供不同的导出名称以保持兼容性）
 */
export const request = apiRequest

// 默认导出
export default {
  apiRequest,
  request
}