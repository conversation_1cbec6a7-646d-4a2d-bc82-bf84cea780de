/**
 * 图相关API模块
 *
 * 提供以下功能：
 * 1. 图数据查询和获取
 * 2. 图统计信息获取
 * 3. 图标签管理
 * 4. 图健康检查
 * 5. 子图查询
 */

import { apiRequest } from './index'

// 类型定义
export interface GraphNode {
  id: string;
  label: string;
  properties: Record<string, any>;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label: string;
  properties: Record<string, any>;
}

export interface GraphData {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

export interface GraphStats {
  total_nodes: number;
  total_edges: number;
  node_types: Record<string, number>;
  edge_types: Record<string, number>;
}

export interface GraphQueryParams {
  label?: string;
  max_depth?: number;
  max_nodes?: number;
}

export interface NodeListParams {
  page?: number;
  page_size?: number;
  node_type?: string;
  property_filters?: Record<string, any>;
  search_text?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface EdgeListParams {
  page?: number;
  page_size?: number;
  edge_type?: string;
  source_id?: string;
  target_id?: string;
  property_filters?: Record<string, any>;
  search_text?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: {
    records: T[];
    pagination: {
      page: number;
      page_size: number;
      total: number;
      total_pages: number;
    };
  };
}

/**
 * 获取图统计信息
 * @returns {Promise} 图统计数据
 */
export const fetchGraphStats = async () => {
  return await apiRequest('/graphs/stats')
}

/**
 * 图健康检查
 * @returns {Promise} 健康状态
 */
export const checkGraphHealth = async () => {
  return await apiRequest('/graphs/health')
}

/**
 * 查询图数据
 * @param {Object} params - 查询参数
 * @param {string} params.label - 标签
 * @param {number} params.max_depth - 最大深度
 * @param {number} params.max_nodes - 最大节点数
 * @returns {Promise} 图数据
 */
export const fetchGraphData = async (params: any = {}) => {
  try {
    const { label = '*', max_depth = 4, max_nodes = 1000 } = params

    const searchParams = new URLSearchParams({
      label: label,
      max_depth: max_depth.toString(),
      max_nodes: max_nodes.toString()
    })

    const response = await fetch(`/api/v1/graph/graphs?${searchParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`获取图数据失败: ${response.status}`)
    }

    const graphData = (await response.json()).data
    console.log('graphData')
    return graphData
  } catch (error) {
    console.error('❌ API Error: fetchGraphData', error)
    throw error
  }
}

/**
 * 获取图标签列表
 * @returns {Promise} 标签列表
 */
export const fetchGraphLabels = async () => {
  try {
    const response = await fetch('/api/v1/graph/labels', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`获取标签列表失败: ${response.status}`)
    }

    const labels = (await response.json()).data
    console.log(labels)
    return labels
  } catch (error) {
    console.error('❌ API Error: fetchGraphLabels', error)
    throw error
  }
}

/**
 * 查询子图
 * @param {Object} params - 查询参数
 * @param {string} params.node_id - 节点ID
 * @param {number} params.depth - 深度
 * @param {number} params.max_nodes - 最大节点数
 * @returns {Promise} 子图数据
 */
export const fetchSubgraph = async (params: any = {}) => {
  const { node_id, depth = 2, max_nodes = 100 } = params
  return await apiRequest('/graphs/subgraph', {
    method: 'GET',
    params: {
      node_id,
      depth,
      max_nodes
    }
  })
}

// 获取节点信息
export const fetchNode = async (nodeId: string) => {
  return await apiRequest(`/graphs/node/${encodeURIComponent(nodeId)}`)
}

// 获取节点的边
export const fetchNodeEdges = async (nodeId: string) => {
  return await apiRequest(`/graphs/node/${encodeURIComponent(nodeId)}/edges`)
}

// 获取节点邻居
export const fetchNodeNeighbors = async (params: any) => {
  return await apiRequest('/graphs/node/neighbors', {
    method: 'POST',
    body: JSON.stringify({
      node_id: params.nodeId,
      max_depth: params.maxDepth || 1,
      limit: params.limit || 50
    })
  })
}

// 获取边信息
export const fetchEdge = async (sourceId: string, targetId: string) => {
  return await apiRequest('/graphs/edge', {
    method: 'POST',
    body: JSON.stringify({
      source_id: sourceId,
      target_id: targetId
    })
  })
}

// 查找路径
export const findPaths = async (params: any) => {
  return await apiRequest('/graphs/path', {
    method: 'POST',
    body: JSON.stringify({
      source_id: params.sourceId,
      target_id: params.targetId,
      max_depth: params.maxDepth || 3,
      limit: params.limit || 10
    })
  })
}

// 图遍历
export const traverseGraph = async (params: any) => {
  return await apiRequest('/graphs/traversal', {
    method: 'POST',
    body: JSON.stringify({
      start_node_id: params.startNodeId,
      traversal_type: params.traversalType || 'bfs',
      max_depth: params.maxDepth || 2,
      limit: params.limit || 100
    })
  })
}

// 图搜索
export const searchGraph = async (params: any) => {
  return await apiRequest('/graphs/search', {
    method: 'POST',
    body: JSON.stringify({
      query: params.query,
      search_type: params.searchType || 'node',
      limit: params.limit || 20
    })
  })
}

// 分页查询节点列表
export const fetchNodesList = async (params: NodeListParams): Promise<PaginatedResponse<GraphNode>> => {
  return await apiRequest('/graphs/nodes/list', {
    method: 'POST',
    body: JSON.stringify({
      page: params.page || 1,
      page_size: params.page_size || 20,
      node_type: params.node_type,
      property_filters: params.property_filters,
      search_text: params.search_text,
      sort_by: params.sort_by || 'id',
      sort_order: params.sort_order || 'asc'
    })
  })
}

// 分页查询边列表
export const fetchEdgesList = async (params: EdgeListParams): Promise<PaginatedResponse<GraphEdge>> => {
  return await apiRequest('/graphs/edges/list', {
    method: 'POST',
    body: JSON.stringify({
      page: params.page || 1,
      page_size: params.page_size || 20,
      edge_type: params.edge_type,
      source_id: params.source_id,
      target_id: params.target_id,
      property_filters: params.property_filters,
      search_text: params.search_text,
      sort_by: params.sort_by || 'source_id',
      sort_order: params.sort_order || 'asc'
    })
  })
}

export default {
  fetchGraphStats,
  checkGraphHealth,
  fetchGraphData,
  fetchGraphLabels,
  fetchSubgraph,
  fetchNode,
  fetchNodeEdges,
  fetchNodeNeighbors,
  fetchEdge,
  findPaths,
  traverseGraph,
  searchGraph,
  fetchNodesList,
  fetchEdgesList
}