/* CSS变量 */
:root {
  --radius: 0.5rem;
  
  /* Tailwind CSS 设计系统变量 */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

/* Sigma.js 相关样式 */
.sigma-container {
  width: 100% !important;
  height: 100% !important;
  background: #ffffff !important;
}

/* 图可视化相关样式 */
.graph-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 控制面板样式 */
.graph-controls {
  position: absolute;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  backdrop-filter: blur(12px);
  padding: 8px;
}

.graph-controls button {
  margin: 2px;
  border: none;
  background: transparent;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s;
}

.graph-controls button:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* 搜索框样式 */
.graph-search {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  border: 1px solid #d1d5db;
  backdrop-filter: blur(8px);
}

/* 属性面板样式 */
.properties-panel {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid #d1d5db;
  backdrop-filter: blur(12px);
  max-width: 300px;
  max-height: 500px;
  overflow-y: auto;
}

/* 图例样式 */
.graph-legend {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid #d1d5db;
  backdrop-filter: blur(12px);
  padding: 12px;
}

/* 加载动画 */
.loading-spinner {
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 自定义工具类 - 补充 Tailwind CSS 未覆盖的样式 */


// 全局变量
@body-bg: #f0f2f5;
@content-bg: #fff;
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
              'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
              sans-serif;

// 全局样式重置
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: @font-family;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: @body-bg;
}

#root {
  min-height: 100vh;
}

// 覆盖一些antd默认样式
.ant-layout {
  background: @body-bg;
  
  &-sider-light {
    background: @content-bg;
  }
} 