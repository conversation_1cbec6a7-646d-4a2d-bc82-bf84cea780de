export {
  DatabaseOutlined,
  RobotOutlined,
  DeploymentUnitOutlined,
  SettingOutlined,
  ApiOutlined,
  ExperimentOutlined
} from '@ant-design/icons'

// 菜单项配置 - 不包含JSX，在使用时动态创建
export const menuConfig = [
  {
    key: 'dataset',
    icon: 'DatabaseOutlined',
    label: '数据集管理',
    children: [
      {
        key: '/training',
        label: '训练数据集'
      },
      {
        key: '/evaluation',
        label: '评测数据集'
      }
    ]
  },
  {
    key: '/model-finetuning',
    icon: 'RobotOutlined',
    label: '模型微调'
  },
  {
    key: '/model-management',
    icon: 'SettingOutlined',
    label: '模型管理'
  },
  {
    key: 'mixrag',
    icon: 'DeploymentUnitOutlined',
    label: 'MIXRAG',
    children: [
      {
        key: '/knowledge-base',
        label: '知识库'
      },
      {
        key: '/document',
        label: 'Document'
      },
      {
        key: '/chunk',
        label: 'Chunk'
      },
      {
        key: '/graph',
        label: 'Graph'
      },
      {
        key: '/retrieval',
        label: 'Retrieval'
      },
      {
        key: '/task-management',
        label: '任务管理'
      }
    ]
  },
  {
    key: '/rag-evaluation',
    icon: 'ExperimentOutlined',
    label: 'RAG评估'
  },
  {
    key: '/api-docs',
    icon: 'ApiOutlined',
    label: 'API文档'
  }
]

// 默认展开的菜单项
export const defaultOpenKeys = ['dataset', 'mixrag']

// 所有可导航的路由key
export const navigableKeys = [
  '/training',
  '/evaluation',
  '/model-finetuning',
  '/knowledge-base',
  '/retrieval',
  '/task-management',
  '/rag-evaluation',
  '/model-management',
  '/api-docs'
]