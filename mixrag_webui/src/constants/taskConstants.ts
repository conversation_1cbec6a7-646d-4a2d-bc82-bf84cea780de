// 任务类型映射
export const TASK_TYPES = {
  'document_chunking': '文档分块',
  'entity_extraction': '实体提取',
  'graph_building': '图谱构建',
  'vector_embedding': '向量嵌入',
  'graph_storage': '图谱存储',
  'query_processing': '查询处理'
}

// 任务状态映射
export const TASK_STATUS = {
  'pending': { text: '待处理', color: 'default' },
  'running': { text: '运行中', color: 'processing' },
  'completed': { text: '已完成', color: 'success' },
  'failed': { text: '失败', color: 'error' },
  'cancelled': { text: '已取消', color: 'warning' },
  'retry': { text: '重试中', color: 'orange' }
}

// 可取消的任务状态
export const CANCELABLE_STATUSES = ['pending', 'running', 'retry']

export default {
  TASK_TYPES,
  TASK_STATUS,
  CANCELABLE_STATUSES
} 