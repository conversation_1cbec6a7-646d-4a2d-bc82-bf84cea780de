.retrieval-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 16px;
  padding: 0;
  overflow: hidden;

  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;

    .chat-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .ant-card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0;
        min-height: 0;
      }

      .messages-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: #fafafa;
        border-radius: 8px 8px 0 0;
        position: relative;

        .messages-list {
          flex: 1;
          overflow-y: auto;
          padding: 16px;
          display: flex;
          flex-direction: column;
          
          &::-webkit-scrollbar {
            width: 6px;
          }
          
          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }
          
          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
            
            &:hover {
              background: #a8a8a8;
            }
          }

          .message-wrapper {
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .empty-state {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px 20px;
          }

          .messages-end {
            padding-bottom: 4px;
          }
        }
      }

      .input-area {
        background: #fff;
        border-top: 1px solid #f0f0f0;
        padding: 12px 16px;
        border-radius: 0 0 8px 8px;

        .input-wrapper {
          display: flex;
          align-items: flex-end;
          gap: 8px;

          .history-button,
          .clear-button {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #666;
            font-size: 13px;

            &:hover {
              color: #1890ff;
            }
          }

          .input-container {
            flex: 1;
            position: relative;

            .query-input {
              resize: none;
              border-radius: 6px;
              font-size: 14px;
              
              &:focus {
                border-color: #40a9ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }

            .input-error {
              position: absolute;
              top: 100%;
              left: 0;
              margin-top: 4px;
              z-index: 10;
            }
          }

          .send-button {
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;
            height: auto;
            min-height: 32px;
          }
        }
      }
    }
  }

  .settings-area {
    flex-shrink: 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .retrieval-container {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .retrieval-container {
    flex-direction: column;
    height: auto;
    min-height: calc(100vh - 120px);

    .chat-area {
      height: 60vh;
      min-height: 400px;
    }

    .settings-area {
      height: auto;
      max-height: 40vh;
      overflow-y: auto;
    }
  }
}

@media (max-width: 576px) {
  .retrieval-container {
    padding: 0 8px;
    gap: 8px;

    .chat-area {
      .chat-card {
        .input-area {
          padding: 8px 12px;

          .input-wrapper {
            gap: 6px;

            .clear-button {
              font-size: 12px;
            }

            .send-button {
              font-size: 12px;
              min-height: 28px;
            }
          }
        }
      }
    }
  }
} 