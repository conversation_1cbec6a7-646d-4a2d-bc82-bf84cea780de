import React, { useEffect, useState } from 'react'
import { Input, Button, Card, Empty, Typography } from 'antd'
import { DeleteOutlined, SendOutlined, MessageOutlined, HistoryOutlined } from '@ant-design/icons'
import PageWrapper from '../../components/common/PageWrapper'
import { ChatMessage, QuerySettings, HistoryManager } from '../../components/Retrieval'
import { useRetrievalManagement } from '../../hooks/useRetrievalManagement'
import { throttle } from '../../lib/utils'
import './index.less'

const { TextArea } = Input
const { Text } = Typography

const RetrievalPage = () => {
  const [historyVisible, setHistoryVisible] = useState(false)

  const {
    // 状态
    messages,
    inputValue,
    isLoading,
    inputError,
    querySettings,

    // Refs
    messagesEndRef,
    messagesContainerRef,
    shouldFollowScrollRef,
    isFormInteractionRef,
    programmaticScrollRef,
    isReceivingResponseRef,

    // 方法
    setInputValue,
    setInputError,
    handleSubmit,
    clearMessages,
    handleSettingsChange,
    scrollToBottom,
    setMessages
  } = useRetrievalManagement()

  // 加载历史记录
  const handleLoadHistory = (history) => {
    setMessages(history)
    scrollToBottom()
  }

  // 添加滚动事件监听器
  useEffect(() => {
    const container = messagesContainerRef.current
    if (!container) return

    // 处理滚轮事件
    const handleWheel = (e) => {
      if (Math.abs(e.deltaY) > 10 && !isFormInteractionRef.current) {
        shouldFollowScrollRef.current = false
      }
    }

    // 处理滚动事件
    const handleScroll = throttle(() => {
      if (programmaticScrollRef.current) {
        programmaticScrollRef.current = false
        return
      }

      const container = messagesContainerRef.current
      if (container) {
        const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 20

        if (isAtBottom) {
          shouldFollowScrollRef.current = true
        } else if (!isFormInteractionRef.current && !isReceivingResponseRef.current) {
          shouldFollowScrollRef.current = false
        }
      }
    }, 30)

    container.addEventListener('wheel', handleWheel)
    container.addEventListener('scroll', handleScroll)

    return () => {
      container.removeEventListener('wheel', handleWheel)
      container.removeEventListener('scroll', handleScroll)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 处理表单区域交互
  useEffect(() => {
    const form = document.querySelector('.retrieval-form')
    if (!form) return

    const handleFormMouseDown = () => {
      isFormInteractionRef.current = true
      setTimeout(() => {
        isFormInteractionRef.current = false
      }, 500)
    }

    form.addEventListener('mousedown', handleFormMouseDown)

    return () => {
      form.removeEventListener('mousedown', handleFormMouseDown)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 当消息变化时自动滚动
  useEffect(() => {
    if (shouldFollowScrollRef.current) {
      scrollToBottom()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages])

  const handleInputChange = (e) => {
    setInputValue(e.target.value)
    if (inputError) setInputError('')
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <PageWrapper>
      <div className="retrieval-container">
        {/* 设置面板 */}
        <div className="settings-area">
          <QuerySettings
              onSettingsChange={handleSettingsChange}
              initialSettings={querySettings}
          />
        </div>

        {/* 聊天区域 */}
        <div className="chat-area">
          <Card className="chat-card">
            <div
                ref={messagesContainerRef}
                className="messages-container"
                onClick={() => {
                  if (shouldFollowScrollRef.current) {
                    shouldFollowScrollRef.current = false
                  }
                }}
            >
              <div className="messages-list">
                {messages.length === 0 ? (
                    <div className="empty-state">
                      <Empty
                          image={<MessageOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
                          description={
                            <div>
                              <Text type="secondary" style={{ fontSize: 16 }}>
                                开始您的智能检索之旅
                              </Text>
                              <br />
                              <Text type="secondary" style={{ fontSize: 14 }}>
                                您可以使用前缀来指定检索模式，例如：/mix 人工智能的发展历程
                              </Text>
                            </div>
                          }
                      />
                    </div>
                ) : (
                    messages.map((message) => (
                        <div
                            key={message.id}
                            className={`message-wrapper ${message.role === 'user' ? 'user-message' : 'assistant-message'}`}
                        >
                          <ChatMessage message={message} />
                        </div>
                    ))
                )}
                <div ref={messagesEndRef} className="messages-end" />
              </div>
            </div>

            {/* 输入区域 */}
            <div className="input-area retrieval-form">
              <div className="input-wrapper">
                <Button
                    icon={<HistoryOutlined />}
                    onClick={() => setHistoryVisible(true)}
                    disabled={isLoading}
                    className="history-button"
                    title="历史记录"
                >
                  历史
                </Button>

                <Button
                    icon={<DeleteOutlined />}
                    onClick={clearMessages}
                    disabled={isLoading}
                    className="clear-button"
                    title="清除聊天记录"
                >
                  清除
                </Button>

                <div className="input-container">
                  <TextArea
                      value={inputValue}
                      onChange={handleInputChange}
                      onKeyPress={handleKeyPress}
                      placeholder="输入您的查询内容...&#10;支持模式前缀：/naive, /local, /global, /mix, /mix, /bypass"
                      disabled={isLoading}
                      autoSize={{ minRows: 1, maxRows: 1 }}
                      className="query-input"
                  />
                  {inputError && (
                      <div className="input-error">
                        <Text type="danger" style={{ fontSize: 12 }}>
                          {inputError}
                        </Text>
                      </div>
                  )}
                </div>

                <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSubmit}
                    disabled={isLoading || !inputValue.trim()}
                    loading={isLoading}
                    className="send-button"
                    title="发送查询"
                >
                  发送
                </Button>
              </div>
            </div>
          </Card>
        </div>

        {/* 历史记录管理 */}
        <HistoryManager
          visible={historyVisible}
          onClose={() => setHistoryVisible(false)}
          onLoadHistory={handleLoadHistory}
        />
      </div>
    </PageWrapper>
  )
}

export default RetrievalPage 