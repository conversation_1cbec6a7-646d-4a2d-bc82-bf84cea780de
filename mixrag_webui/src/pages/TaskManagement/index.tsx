import React, { useState } from 'react'
import { 
  Card, 
  Button, 
  Space, 
  Row, 
  Col,
  Pagination,
  Tabs,
  message,
  Modal
} from 'antd'
import { 
  ReloadOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  ClusterOutlined,
  MonitorOutlined
} from '@ant-design/icons'
import PageWrapper from '../../components/common/PageWrapper'
import { 
  PipelineTable, 
  PipelineFilter, 
  PipelineDetailModal,
  PipelineLogsModal,
  ExecutorStatusCard
} from '../../components/TaskManagement'
import { usePipelineManagement } from '../../hooks/usePipelineManagement'



const TaskManagement = () => {
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isLogsModalVisible, setIsLogsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('pipelines')
  const [selectedPipelineForLogs, setSelectedPipelineForLogs] = useState(null)

  const {
    // 数据状态
    pipelines,
    loading,
    total,
    currentPage,
    pageSize,
    statusFilter,
    createdByFilter,
    selectedPipeline,
    pipelineTasks,
    statistics,
    executorStatus,

    // 操作状态
    cancelLoading,
    detailLoading,
    tasksLoading,
    cleanupLoading,
    restartLoading,
    resumeLoading,
    refreshLoading,
    logsLoading,

    // 操作方法
    loadPipelines,
    loadPipelineDetail,
    loadPipelineTasks,
    handleCancelPipeline,
    handleRestartPipeline,
    handleResumePipeline,
    handleRefreshExecutor,
    loadPipelineLogs,
    loadStatistics,
    loadExecutorStatus,
    handleCleanupCompleted,

    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setStatusFilter,
    setCreatedByFilter,
    setSelectedPipeline
  } = usePipelineManagement()

  // 处理查看流水线详情
  const handleViewPipeline = async (pipelineId) => {
    try {
      // 同时加载流水线详情和任务列表
      const [pipelineDetail] = await Promise.all([
        loadPipelineDetail(pipelineId),
        loadPipelineTasks(pipelineId)
      ])
      
      if (pipelineDetail) {
        setIsDetailModalVisible(true)
      }
    } catch {
      message.error('加载流水线详情失败')
    }
  }

  // 处理取消流水线
  const handleCancel = async (pipelineId) => {
    Modal.confirm({
      title: '确认取消',
      icon: <ExclamationCircleOutlined />,
      content: '确定要取消这个流水线吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await handleCancelPipeline(pipelineId)
      }
    })
  }

  // 处理重启流水线
  const handleRestart = async (pipelineId) => {
    Modal.confirm({
      title: '确认重启',
      icon: <ExclamationCircleOutlined />,
      content: '确定要重启这个流水线吗？将重置所有失败的任务。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await handleRestartPipeline(pipelineId)
      }
    })
  }

  // 处理恢复流水线
  const handleResume = async (pipelineId) => {
    Modal.confirm({
      title: '确认恢复',
      icon: <ExclamationCircleOutlined />,
      content: '确定要恢复这个流水线吗？将从上次中断的地方继续执行。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await handleResumePipeline(pipelineId)
      }
    })
  }

  // 处理查看日志
  const handleViewLogs = (pipelineId) => {
    setSelectedPipelineForLogs(pipelineId)
    setIsLogsModalVisible(true)
  }

  // 处理分页变化
  const handleTableChange = (page, size) => {
    setCurrentPage(page)
    setPageSize(size)
  }

  // 处理刷新
  const handleRefresh = () => {
    loadPipelines()
    loadStatistics()
    loadExecutorStatus()
  }

  // 处理详情弹窗刷新
  const handleDetailRefresh = async () => {
    if (selectedPipeline?.pipeline_id) {
      await Promise.all([
        loadPipelineDetail(selectedPipeline.pipeline_id),
        loadPipelineTasks(selectedPipeline.pipeline_id)
      ])
    }
  }

  // 处理清理已完成流水线
  const handleCleanup = () => {
    Modal.confirm({
      title: '清理已完成的流水线',
      icon: <ExclamationCircleOutlined />,
      content: '确定要清理已完成的流水线吗？默认保留最近7天的记录。',
      okText: '确认清理',
      cancelText: '取消',
      onOk: async () => {
        await handleCleanupCompleted(7)
      }
    })
  }

  return (
    <PageWrapper>
      <div style={{ padding: '0 24px' }}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
          size="large"
          items={[
            {
              key: 'pipelines',
              label: (
                <span>
                  <ClusterOutlined />
                  流水线管理
                </span>
              ),
              children: (
                <Card>
                {/* 操作栏 */}
                <Row justify="space-between" style={{ marginBottom: 16 }}>
                  <Col>
                    <Space>
                      <Button 
                        icon={<ReloadOutlined />}
                        onClick={handleRefresh}
                      >
                        刷新
                      </Button>
                      <Button 
                        icon={<DeleteOutlined />}
                        onClick={handleCleanup}
                        loading={cleanupLoading}
                      >
                        清理已完成
                      </Button>
                    </Space>
                  </Col>
                  <Col>
                    <PipelineFilter
                      statusFilter={statusFilter}
                      createdByFilter={createdByFilter}
                      onStatusChange={setStatusFilter}
                      onCreatedByChange={setCreatedByFilter}
                    />
                  </Col>
                </Row>

                             {/* 流水线列表表格 */}
                 <PipelineTable
                   pipelines={pipelines}
                   loading={loading}
                   onViewPipeline={handleViewPipeline}
                   onCancelPipeline={handleCancel}
                   onRestartPipeline={handleRestart}
                   onResumePipeline={handleResume}
                   onViewLogs={handleViewLogs}
                   cancelLoading={cancelLoading}
                   restartLoading={restartLoading}
                   resumeLoading={resumeLoading}
                 />

                {/* 分页 */}
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Pagination
                    current={currentPage}
                    pageSize={pageSize}
                    total={total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => 
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                    onChange={handleTableChange}
                    onShowSizeChange={handleTableChange}
                  />
                </div>
              </Card>
              )
            },
            {
              key: 'executor',
              label: (
                <span>
                  <MonitorOutlined />
                  执行器状态
                </span>
              ),
              children: (
                <ExecutorStatusCard 
                  executorStatus={executorStatus}
                  statistics={statistics}
                  loading={loading}
                  onRefresh={handleRefreshExecutor}
                  refreshLoading={refreshLoading}
                />
              )
            }
          ]}
        />
      </div>

             {/* 流水线详情弹窗 */}
       <PipelineDetailModal
         visible={isDetailModalVisible}
         onCancel={() => {
           setIsDetailModalVisible(false)
           setSelectedPipeline(null)
         }}
         pipeline={selectedPipeline}
         tasks={pipelineTasks}
         loading={detailLoading}
         tasksLoading={tasksLoading}
         onRefresh={handleDetailRefresh}
       />

       {/* 流水线日志弹窗 */}
       <PipelineLogsModal
         visible={isLogsModalVisible}
         onCancel={() => {
           setIsLogsModalVisible(false)
           setSelectedPipelineForLogs(null)
         }}
         pipelineId={selectedPipelineForLogs}
         onLoadLogs={loadPipelineLogs}
         loading={logsLoading}
       />
    </PageWrapper>
  )
}

export default TaskManagement