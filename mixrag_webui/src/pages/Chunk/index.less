/* Chunk 页面样式 */
.chunk-management {
  .ant-table {
    /* 表格整体样式 */
    table-layout: auto;
    width: 100%;

    .ant-table-thead > tr > th {
      text-align: left;
      vertical-align: middle;
      font-weight: 600;
      background-color: #fafafa;
      border-bottom: 2px solid #f0f0f0;
      white-space: nowrap;
      padding: 12px 8px;
    }

    .ant-table-tbody > tr > td {
      text-align: left;
      vertical-align: middle;
      padding: 12px 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    /* 确保表格行高一致 */
    .ant-table-tbody > tr {
      height: 60px;
    }

    /* 表格行悬停效果 */
    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }

    /* 表格边框样式 */
    &.ant-table-bordered {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        border-right: 1px solid #f0f0f0;
      }
    }

    /* 内容列特殊处理 */
    .ant-table-tbody > tr > td.content-cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 0; /* 让内容列自动扩展 */
      width: auto;
    }
  }

  /* 搜索结果表格特殊样式 */
  .search-results-table {
    .similarity-tag {
      margin: 0;
      font-weight: 500;
    }
  }

  /* 列表表格特殊样式 */
  .list-table {
    .chunk-id-cell,
    .doc-id-cell {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
    }
  }

  /* 内容单元格样式 */
  .content-cell {
    text-align: left !important;
    vertical-align: middle !important;

    .content-text {
      display: block;
      width: 100%;
      line-height: 1.5;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  /* 数字单元格样式 */
  .number-cell {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 500;
    text-align: left !important;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }
  }

  /* 表格加载状态 */
  .ant-table-placeholder {
    .ant-table-tbody > tr > td {
      text-align: center;
      padding: 40px 20px;
    }
  }

  /* 分页器样式 */
  .ant-pagination {
    margin-top: 16px;
    text-align: center;
    
    .ant-pagination-total-text {
      margin-right: 16px;
    }
  }
}

/* 全局表格优化 */
.ant-table-wrapper {
  .ant-table-container {
    border-radius: 6px;
    overflow: hidden;
  }
}

/* 表格滚动条样式 */
.ant-table-body {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}
