import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Tabs, Card, Typography, Spin, message } from 'antd';
import { DatabaseOutlined, FileTextOutlined, NodeIndexOutlined, ShareAltOutlined } from '@ant-design/icons';
import PageWrapper from '../../../components/common/PageWrapper';
import DocumentPage from '../../Document';
import ChunkPage from '../../Chunk';
import GraphPage from '../../Graph';
import { fetchKnowledgeBaseDetail } from '../../../api/knowledgeBaseApi';

const { Title } = Typography;

const KnowledgeBaseDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const [knowledgeBase, setKnowledgeBase] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadKnowledgeBaseDetail = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const response = await fetchKnowledgeBaseDetail(id);
        if (response.success) {
          setKnowledgeBase(response.data);
        } else {
          message.error(response.message || '获取知识库详情失败');
        }
      } catch (error: any) {
        message.error(error.message || '获取知识库详情失败');
      } finally {
        setLoading(false);
      }
    };

    loadKnowledgeBaseDetail();
  }, [id]);

  const tabItems = [
    {
      key: 'document',
      label: (
        <span>
          <FileTextOutlined />
          Document
        </span>
      ),
      children: <DocumentPage kb_id={id} />
    },
    {
      key: 'chunk',
      label: (
        <span>
          <NodeIndexOutlined />
          Chunk
        </span>
      ),
      children: <ChunkPage kb_id={id} />
    },
    {
      key: 'graph',
      label: (
        <span>
          <ShareAltOutlined />
          Graph
        </span>
      ),
      children: <GraphPage kb_id={id} />
    }
  ];

  if (loading) {
    return (
      <PageWrapper>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <Card>
        {/* 知识库标题 */}
        <div style={{ marginBottom: '24px', borderBottom: '1px solid #f0f0f0', paddingBottom: '16px' }}>
          <Title level={3} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '8px' }}>
            <DatabaseOutlined style={{ color: '#1677ff' }} />
            {knowledgeBase?.kb_name || '知识库详情'}
          </Title>
          {knowledgeBase?.kb_des && (
            <p style={{ margin: '8px 0 0 0', color: '#666' }}>
              {knowledgeBase.kb_des}
            </p>
          )}
        </div>

        {/* Tab内容 */}
        <Tabs defaultActiveKey="document" items={tabItems} />
      </Card>
    </PageWrapper>
  );
};

export default KnowledgeBaseDetailPage;
