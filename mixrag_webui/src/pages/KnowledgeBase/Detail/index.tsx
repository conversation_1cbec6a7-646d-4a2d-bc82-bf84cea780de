import React from 'react';
import { useParams } from 'react-router-dom';
import { Tabs, Card } from 'antd';
import PageWrapper from '../../../components/common/PageWrapper';
import DocumentPage from '../../Document';
import ChunkPage from '../../Chunk';
import GraphPage from '../../Graph';

const { TabPane } = Tabs;

const KnowledgeBaseDetailPage = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <PageWrapper>
      <Card>
        <Tabs defaultActiveKey="document">
          <TabPane tab="Document" key="document">
            <DocumentPage kb_id={id} />
          </TabPane>
          <TabPane tab="Chunk" key="chunk">
            <ChunkPage kb_id={id} />
          </TabPane>
          <TabPane tab="Graph" key="graph">
            <GraphPage kb_id={id} />
          </TabPane>
        </Tabs>
      </Card>
    </PageWrapper>
  );
};

export default KnowledgeBaseDetailPage;
