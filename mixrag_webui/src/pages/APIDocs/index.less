.api-docs-container {
  width: 100%;
  height: calc(100vh - 160px); // 减去header(约64px) + content margin(48px) + 其他间距
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  min-height: 600px; // 设置最小高度保证iframe不会太小
  
  .api-docs-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex: 1;
    background: #fff;
    
    // 确保iframe加载时有过渡效果
    transition: opacity 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}