import { useEffect, useState, useCallback, useMemo, useRef } from 'react'
// import { MiniMap } from '@react-sigma/minimap'
import { SigmaContainer, useRegisterEvents, useSigma } from '@react-sigma/core'
import { Settings as SigmaSettings } from 'sigma/settings'
import { GraphSearchOption, OptionItem } from '@react-sigma/graph-search'
import { EdgeArrowProgram, NodePointProgram, NodeCircleProgram } from 'sigma/rendering'
import { NodeBorderProgram } from '@sigma/node-border'
import EdgeCurveProgram, { EdgeCurvedArrowProgram } from '@sigma/edge-curve'
import { DirectedGraph } from 'graphology'
import MiniSearch from 'minisearch'

import FocusOnNode from '@/components/Graph/FocusOnNode'
import LayoutsControl from '@/components/Graph/LayoutsControl'
import GraphControl from '@/components/Graph/GraphControl'
// import ThemeToggle from '@/components/ThemeToggle'
import ZoomControl from '@/components/Graph/ZoomControl'
import FullScreenControl from '@/components/Graph/FullScreenControl'
import Settings from '@/components/Graph/Settings'
import GraphSearch from '@/components/Graph/GraphSearch'
import GraphLabels from '@/components/Graph/GraphLabels'
import PropertiesView from '@/components/Graph/PropertiesView'
import SettingsDisplay from '@/components/Graph/SettingsDisplay'
import Legend from '@/components/Graph/Legend'
import LegendButton from '@/components/Graph/LegendButton'

import { useSettingsStore } from '@/stores/settings'
import { useGraphStore } from '@/stores/graph'
import { RawGraph } from '@/types/graph'

import '@react-sigma/core/lib/style.css'
import '@react-sigma/graph-search/lib/style.css'

// Sigma settings
const defaultSigmaSettings: Partial<SigmaSettings> = {
  allowInvalidContainer: true,
  defaultNodeType: 'default',
  defaultEdgeType: 'curvedNoArrow',
  renderEdgeLabels: false,
  edgeProgramClasses: {
    arrow: EdgeArrowProgram,
    curvedArrow: EdgeCurvedArrowProgram,
    curvedNoArrow: EdgeCurveProgram
  },
  nodeProgramClasses: {
    default: NodeBorderProgram,
    circel: NodeCircleProgram,
    point: NodePointProgram
  },
  labelGridCellSize: 60,
  labelRenderedSizeThreshold: 12,
  enableEdgeEvents: true,
  labelColor: {
    color: '#000',
    attribute: 'labelColor'
  },
  edgeLabelColor: {
    color: '#000',
    attribute: 'labelColor'
  },
  edgeLabelSize: 8,
  labelSize: 12
  // minEdgeThickness: 2
  // labelFont: 'Lato, sans-serif'
}

const GraphEvents = () => {
  const registerEvents = useRegisterEvents()
  const sigma = useSigma()
  const [draggedNode, setDraggedNode] = useState<string | null>(null)

  useEffect(() => {
    // Register the events
    registerEvents({
      downNode: (e) => {
        setDraggedNode(e.node)
        sigma.getGraph().setNodeAttribute(e.node, 'highlighted', true)
      },
      // On mouse move, if the drag mode is enabled, we change the position of the draggedNode
      mousemovebody: (e) => {
        if (!draggedNode) return
        // Get new position of node
        const pos = sigma.viewportToGraph(e)
        sigma.getGraph().setNodeAttribute(draggedNode, 'x', pos.x)
        sigma.getGraph().setNodeAttribute(draggedNode, 'y', pos.y)

        // Prevent sigma to move camera:
        e.preventSigmaDefault()
        e.original.preventDefault()
        e.original.stopPropagation()
      },
      // On mouse up, we reset the autoscale and the dragging mode
      mouseup: () => {
        if (draggedNode) {
          setDraggedNode(null)
          sigma.getGraph().removeNodeAttribute(draggedNode, 'highlighted')
        }
      },
      // Disable the autoscale at the first down interaction
      mousedown: (e) => {
        // Only set custom BBox if it's a drag operation (mouse button is pressed)
        const mouseEvent = e.original as MouseEvent
        if (mouseEvent.buttons !== 0 && !sigma.getCustomBBox()) {
          sigma.setCustomBBox(sigma.getBBox())
        }
      }
    })
  }, [registerEvents, sigma, draggedNode])

  return null
}

// 加载图数据的函数
const loadGraphData = async (label: string = '*', maxDepth: number = 4, maxNodes: number = 1000) => {
  try {
    // 导入API函数
    const { fetchGraphData } = await import('@/api/graphApi')

    const graphData = await fetchGraphData({
      label: label,
      max_depth: maxDepth,
      max_nodes: maxNodes
    })

    console.log('Loaded graph data:', graphData)
    return graphData
  } catch (error) {
    console.error('Error loading graph data:', error)
    return null
  }
}

// 转换图数据为适合sigma.js的格式
const convertGraphDataToSigmaFormat = (graphData: any) => {
  if (!graphData || !graphData.nodes || !graphData.edges) {
    console.error('Invalid graph data format')
    return null
  }

  // 创建RawGraph实例
  const rawGraph = new RawGraph()
  
  // 颜色映射
  const entityTypeColors: Record<string, string> = {
    'person': '#ff6b6b',
    'organization': '#4ecdc4', 
    'geo': '#45b7d1',
    'event': '#96ceb4',
    'category': '#ffeaa7',
    'UNKNOWN': '#ddd'
  }

  // 计算合适的环形布局半径（基于节点数量）
  const nodeCount = graphData.nodes.length
  // 环形布局使用固定半径，确保所有节点在环形上均匀分布
  const circleRadius = Math.min(0.25, Math.max(0.15, Math.sqrt(nodeCount) * 0.015))

  // 处理节点
  const processedNodes = graphData.nodes.map((node: any, index: number) => {
    const entityType = node.properties?.entity_type || 'UNKNOWN'

    // 使用环形布局：所有节点在同一个圆周上均匀分布
    const angle = (index / nodeCount) * 2 * Math.PI

    const nodeData = {
      id: node.id,
      labels: node.labels || [node.id],
      properties: node.properties || {},
      size: 5, // 初始大小，将在计算度数后调整
      // 环形布局：所有节点在固定半径的圆周上
      x: 0.5 + Math.cos(angle) * circleRadius,
      y: 0.5 + Math.sin(angle) * circleRadius,
      color: entityTypeColors[entityType as keyof typeof entityTypeColors] || entityTypeColors['UNKNOWN'],
      degree: 0 // 将在后面计算
    }

    rawGraph.nodes.push(nodeData)
    rawGraph.nodeIdMap[node.id] = index
    return nodeData
  })

  // 处理边
  const processedEdges = graphData.edges.map((edge: any, index: number) => {
    const edgeData = {
      id: edge.id,
      source: edge.source,
      target: edge.target,
      type: edge.type || 'DIRECTED',
      properties: edge.properties || {},
      dynamicId: '' // 将在添加到sigma图时设置
    }
    
    rawGraph.edges.push(edgeData)
    rawGraph.edgeIdMap[edge.id] = index
    return edgeData
  })

  // 计算节点度数
  processedEdges.forEach((edge: any) => {
    const sourceIndex = rawGraph.nodeIdMap[edge.source]
    const targetIndex = rawGraph.nodeIdMap[edge.target]
    if (sourceIndex !== undefined) {
      rawGraph.nodes[sourceIndex].degree += 1
    }
    if (targetIndex !== undefined) {
      rawGraph.nodes[targetIndex].degree += 1
    }
  })

  // 计算度数范围，用于节点大小缩放
  const degrees = processedNodes.map(node => node.degree)
  const minDegree = Math.min(...degrees)
  const maxDegree = Math.max(...degrees)
  const degreeRange = maxDegree - minDegree

  // 更新节点大小基于度数
  processedNodes.forEach((node: any) => {
    if (degreeRange > 0) {
      // 使用对数缩放，让度数差异更明显但不会过于极端
      const normalizedDegree = (node.degree - minDegree) / degreeRange
      // 使用平方根函数让大小变化更平滑 (范围: 5-15，缩小了约40%)
      node.size = 5 + Math.sqrt(normalizedDegree) * 10
    } else {
      // 所有节点度数相同时使用默认大小
      node.size = 8
    }

    // 确保最小节点大小
    node.size = Math.max(node.size, 4)
  })

  // 创建sigma图
  const sigmaGraph = new DirectedGraph()

  // 添加节点到sigma图
  processedNodes.forEach((node: any) => {
    // 过滤掉可能与Sigma.js冲突的属性
    const { type, ...safeProperties } = node.properties || {}

    sigmaGraph.addNode(node.id, {
      label: node.labels[0] || node.id,
      size: node.size, // 使用已计算的基于度数的大小
      x: node.x,
      y: node.y,
      color: node.color,
      borderColor: '#000',
      borderSize: 1,
      highlighted: false,
      // 保存原始type到nodeType字段，避免与Sigma.js的type冲突
      nodeType: type,
      ...safeProperties
    })
  })

  // 添加边到sigma图
  processedEdges.forEach((edge: any) => {
    if (sigmaGraph.hasNode(edge.source) && sigmaGraph.hasNode(edge.target)) {
      const edgeId = sigmaGraph.addEdge(edge.source, edge.target, {
        label: edge.properties?.keywords || '',
        size: edge.properties?.weight || 1,
        originalWeight: edge.properties?.weight || 1,
        color: '#666',
        type: 'curvedNoArrow',
        ...edge.properties
      })
      // 更新dynamicId为sigma生成的边ID
      edge.dynamicId = edgeId
      const edgeIndex = rawGraph.edgeIdMap[edge.id]
      if (edgeIndex !== undefined) {
        rawGraph.edges[edgeIndex].dynamicId = edgeId
        rawGraph.edgeDynamicIdMap[edgeId] = edgeIndex
      }
    }
  })

  // 构建边的动态映射
  rawGraph.buildDynamicMap()

  console.log('Converted graph data:', {
    nodes: sigmaGraph.order,
    edges: sigmaGraph.size,
    rawGraph
  })

  return { rawGraph, sigmaGraph }
}

// 初始化搜索引擎
const initializeSearchEngine = (nodes: any[]) => {
  if (!nodes || nodes.length === 0) {
    return null
  }

  const miniSearch = new MiniSearch({
    fields: ['id', 'label', 'entity_type'],
    storeFields: ['id', 'label', 'properties'],
    searchOptions: {
      boost: { id: 2, label: 3 },
      fuzzy: 0.2
    }
  })

  const searchableNodes = nodes.map((node: any) => ({
    id: node.id,
    label: node.labels?.[0] || node.id,
    entity_type: node.properties?.entity_type || 'UNKNOWN',
    properties: node.properties || {}
  }))

  miniSearch.addAll(searchableNodes)
  return miniSearch
}

// 数据加载组件
const GraphDataLoader = () => {
  const queryLabel = useSettingsStore.use.queryLabel()
  const graphQueryMaxDepth = useSettingsStore.use.graphQueryMaxDepth()
  const graphMaxNodes = useSettingsStore.use.graphMaxNodes()
  const forceReload = useGraphStore.use.forceReload()

  // 使用ref来跟踪当前加载状态，避免依赖项循环
  const loadingRef = useRef(false)
  const lastLoadedLabelRef = useRef('')

  useEffect(() => {
    const loadData = async () => {
      // 防止重复调用
      if (loadingRef.current) {
        console.log('Graph data is already being loaded, skipping...')
        return
      }

      // 检查是否需要强制重载
      const shouldForceReload = forceReload

      // 如果标签没有变化且不是强制重载，跳过
      if (lastLoadedLabelRef.current === queryLabel && !shouldForceReload) {
        console.log('Same label and no force reload, skipping reload...')
        return
      }

      // 直接从store获取函数，避免依赖项问题
      const store = useGraphStore.getState()

      // 如果是强制重载，重置forceReload标志
      if (shouldForceReload) {
        console.log('🔄 Force reload triggered for label:', queryLabel)
        store.setForceReload(false)
      }

      loadingRef.current = true
      lastLoadedLabelRef.current = queryLabel

      store.setIsFetching(true)
      try {
        console.log('Loading graph data for label:', queryLabel)
        // 使用真实的API获取图数据
        const graphData = await loadGraphData(queryLabel, graphQueryMaxDepth, graphMaxNodes)
        if (graphData) {
          const convertedData = convertGraphDataToSigmaFormat(graphData)
          if (convertedData) {
            const { rawGraph, sigmaGraph } = convertedData

            // 设置图数据到store
            store.setRawGraph(rawGraph)
            store.setSigmaGraph(sigmaGraph)

            // 初始化搜索引擎
            const searchEngine = initializeSearchEngine(rawGraph.nodes)
            store.setSearchEngine(searchEngine)

            store.setGraphIsEmpty(false)
            console.log('Graph data loaded successfully for label:', queryLabel)
          } else {
            store.setGraphIsEmpty(true)
            console.error('Failed to convert graph data')
          }
        } else {
          store.setGraphIsEmpty(true)
          console.error('Failed to load graph data')
        }
      } catch (error) {
        console.error('Error loading graph data:', error)
        store.setGraphIsEmpty(true)
      } finally {
        store.setIsFetching(false)
        loadingRef.current = false
      }
    }

    // 只有当queryLabel有值时才加载数据
    if (queryLabel) {
      loadData()
    }
  }, [queryLabel, graphQueryMaxDepth, graphMaxNodes, forceReload])

  return null
}

const GraphViewer = () => {
  const [sigmaSettings, setSigmaSettings] = useState(defaultSigmaSettings)
  const sigmaRef = useRef<any>(null)

  const selectedNode = useGraphStore.use.selectedNode()
  const focusedNode = useGraphStore.use.focusedNode()
  const moveToSelectedNode = useGraphStore.use.moveToSelectedNode()
  const isFetching = useGraphStore.use.isFetching()
  const sigmaGraph = useGraphStore.use.sigmaGraph()

  const showPropertyPanel = useSettingsStore.use.showPropertyPanel()
  const showNodeSearchBar = useSettingsStore.use.showNodeSearchBar()
  const enableNodeDrag = useSettingsStore.use.enableNodeDrag()
  const showLegend = useSettingsStore.use.showLegend()

  // 备用相机调整函数 - 使用与重置缩放按钮相同的逻辑
  const backupCameraAdjustment = useCallback(() => {
    const sigma = useGraphStore.getState().sigmaInstance
    if (!sigma || !sigmaGraph || sigmaGraph.order === 0) return

    try {
      console.log('Performing backup camera adjustment...')

      // 首先清除任何自定义边界框并刷新
      sigma.setCustomBBox(null)
      sigma.refresh()

      // 获取刷新后的图
      const graph = sigma.getGraph()

      // 检查图是否有节点
      if (!graph?.order || graph.nodes().length === 0) {
        console.log('No nodes for backup adjustment')
        return
      }

      // 使用与重置缩放按钮相同的逻辑
      sigma.getCamera().animate(
        { x: 0.5, y: 0.5, ratio: 1.1 },
        { duration: 1000 }
      )

      console.log('Backup camera adjustment completed using reset zoom logic')
    } catch (error) {
      console.error('Backup camera adjustment failed:', error)
    }
  }, [sigmaGraph])

  // Initialize sigma settings once on component mount
  // All dynamic settings will be updated in GraphControl using useSetSettings
  useEffect(() => {
    setSigmaSettings(defaultSigmaSettings)
    console.log('Initialized sigma settings')
  }, [])

  // 备用相机调整 - 在图数据加载完成后执行
  useEffect(() => {
    if (sigmaGraph && sigmaGraph.order > 0 && !isFetching) {
      // 等待一段时间确保所有布局和渲染完成
      const timeoutId = setTimeout(() => {
        backupCameraAdjustment()
      }, 2000) // 2秒后执行备用调整

      return () => clearTimeout(timeoutId)
    }
  }, [sigmaGraph, isFetching, backupCameraAdjustment])

  // Clean up sigma instance when component unmounts
  useEffect(() => {
    return () => {
      // TAB is mount twice in vite dev mode, this is a workaround

      const sigma = useGraphStore.getState().sigmaInstance
      if (sigma) {
        try {
          // Destroy sigma，and clear WebGL context
          sigma.kill()
          useGraphStore.getState().setSigmaInstance(null)
          console.log('Cleared sigma instance on Graphviewer unmount')
        } catch (error) {
          console.error('Error cleaning up sigma instance:', error)
        }
      }
    }
  }, [])

  // Note: There was a useLayoutEffect hook here to set up the sigma instance and graph data,
  // but testing showed it wasn't executing or having any effect, while the backup mechanism
  // in GraphControl was sufficient. This code was removed to simplify implementation

  const onSearchFocus = useCallback((value: GraphSearchOption | null) => {
    if (value === null) useGraphStore.getState().setFocusedNode(null)
    else if (value.type === 'nodes') useGraphStore.getState().setFocusedNode(value.id)
  }, [])

  const onSearchSelect = useCallback((value: GraphSearchOption | null) => {
    if (value === null) {
      useGraphStore.getState().setSelectedNode(null)
    } else if (value.type === 'nodes') {
      useGraphStore.getState().setSelectedNode(value.id, true)
    }
  }, [])

  const autoFocusedNode = useMemo(() => focusedNode ?? selectedNode, [focusedNode, selectedNode])
  const searchInitSelectedNode = useMemo(
      (): OptionItem | null => (selectedNode ? { type: 'nodes', id: selectedNode } : null),
      [selectedNode]
  )

  // Always render SigmaContainer but control its visibility with CSS
  return (
    <div className="relative h-full w-full overflow-hidden">
      {/* 数据加载器 */}
      <GraphDataLoader />
      
      <SigmaContainer
        settings={sigmaSettings}
        className="!bg-background !size-full overflow-hidden"
        ref={sigmaRef}
      >
        <GraphControl />

        {enableNodeDrag && <GraphEvents />}

        <FocusOnNode node={autoFocusedNode} move={moveToSelectedNode} />

        <div className="absolute top-2 left-2 flex items-start gap-2">
          <GraphLabels />
          {showNodeSearchBar && (
            <GraphSearch
              value={searchInitSelectedNode}
              onFocus={onSearchFocus}
              onChange={onSearchSelect}
            />
          )}
        </div>

        <div className="bg-background/60 absolute bottom-2 left-2 flex flex-col rounded-xl border-2 backdrop-blur-lg">
          <LayoutsControl />
          <ZoomControl />
          <FullScreenControl />
          <LegendButton />
          <Settings />
          {/* <ThemeToggle /> */}
        </div>

        {showPropertyPanel && (
          <div className="absolute top-2 right-2">
            <PropertiesView />
          </div>
        )}

        {showLegend && (
          <div className="absolute bottom-10 right-2">
            <Legend className="bg-background/60 backdrop-blur-lg" />
          </div>
        )}

        {/* <div className="absolute bottom-2 right-2 flex flex-col rounded-xl border-2">
          <MiniMap width="100px" height="100px" />
        </div> */}

        <SettingsDisplay />
      </SigmaContainer>

      {/* Loading overlay - shown when data is loading */}
      {isFetching && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
          <div className="text-center">
            <div className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p>Loading Graph Data...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default GraphViewer
