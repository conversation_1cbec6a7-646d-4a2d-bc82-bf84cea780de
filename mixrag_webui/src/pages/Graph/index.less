.graph-page {
  .ant-tabs-card > .ant-tabs-content {
    height: auto;
    margin-top: -16px;
  }

  .ant-tabs-card > .ant-tabs-content > .ant-tabs-tabpane {
    background: #fff;
    padding: 24px;
    border-radius: 0 0 8px 8px;
  }

  // 图可视化容器样式
  .graph-visualization-container {
    position: relative;
    width: 100%;
    height: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    overflow: hidden;
    background: #fafafa;
    
    &:hover {
      border-color: #40a9ff;
    }
  }

  // Sigma.js 容器样式调整
  .sigma-container {
    width: 100% !important;
    height: 100% !important;
  }

  // 控制面板样式
  .graph-controls {
    position: absolute;
    z-index: 100;
    
    &.top-left {
      top: 12px;
      left: 12px;
    }
    
    &.bottom-left {
      bottom: 12px;
      left: 12px;
    }
    
    &.top-right {
      top: 12px;
      right: 12px;
    }
    
    &.bottom-right {
      bottom: 12px;
      right: 12px;
    }
  }

  // 加载状态样式
  .graph-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1000;
    
    .loading-content {
      text-align: center;
      
      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin-top: 12px;
        font-size: 14px;
        color: #666;
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .node-link {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }

  .path-visualization {
    .ant-steps-item-title {
      font-size: 12px;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    }
  }

  .stats-card {
    .ant-statistic-title {
      font-size: 14px;
      color: #666;
    }

    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;
    }
  }

  .search-highlight {
    background-color: #fff2e8;
    border: 1px solid #ffab00;
    border-radius: 4px;
    padding: 2px 4px;
  }

  .traversal-result {
    .ant-table-tbody > tr {
      &:hover {
        .node-link {
          color: #1890ff;
          font-weight: 500;
        }
      }
    }
  }

  .graph-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid #fff;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-tabs-card .ant-tabs-content {
      padding: 16px;
    }

    .graph-visualization-container {
      height: 400px !important;
    }

    .ant-col {
      margin-bottom: 16px;
    }
  }

  // 深色主题支持
  @media (prefers-color-scheme: dark) {
    .graph-visualization-container {
      background-color: #1f1f1f;
      border-color: #434343;
    }

    .node-link {
      color: #69c0ff;
    }
  }
} 