import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Input,
  Select,
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Row,
  Col,
  Form,
  Pagination,
  Typography,
  Descriptions,
  Modal,
  Spin
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FilterOutlined,
  ClearOutlined
} from '@ant-design/icons'
import { fetchNodesList, GraphNode, NodeListParams, PaginatedResponse } from '@/api/graphApi'

const { Option } = Select
const { Text } = Typography
const { TextArea } = Input

interface NodeManagementProps {}

const NodeManagement: React.FC<NodeManagementProps> = () => {
  const [loading, setLoading] = useState(false)
  const [nodes, setNodes] = useState<GraphNode[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [searchText, setSearchText] = useState('')
  const [nodeType, setNodeType] = useState<string | undefined>()
  const [sortBy, setSortBy] = useState('id')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [nodeTypes, setNodeTypes] = useState<string[]>([])

  // 获取节点列表
  const fetchNodes = async (params?: Partial<NodeListParams>) => {
    setLoading(true)
    try {
      const requestParams: NodeListParams = {
        page: currentPage,
        page_size: pageSize,
        search_text: searchText || undefined,
        node_type: nodeType,
        sort_by: sortBy,
        sort_order: sortOrder,
        ...params
      }

      const response: PaginatedResponse<GraphNode> = await fetchNodesList(requestParams)

      if (response.success) {
        setNodes(response.data.records)
        setTotal(response.data.pagination.total)

        // 提取节点类型用于筛选
        const types = Array.from(new Set(response.data.records.map(node => node.entity_type).filter(Boolean)))
        setNodeTypes(prev => Array.from(new Set([...prev, ...types])))
      } else {
        message.error(response.message || '获取节点列表失败')
      }
    } catch (error) {
      console.error('获取节点列表失败:', error)
      message.error('获取节点列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化加载
  useEffect(() => {
    fetchNodes()
  }, [currentPage, pageSize, sortBy, sortOrder])

  // 搜索处理
  const handleSearch = () => {
    setCurrentPage(1)
    fetchNodes({ page: 1 })
  }

  // 重置筛选
  const handleReset = () => {
    setSearchText('')
    setNodeType(undefined)
    setSortBy('id')
    setSortOrder('asc')
    setCurrentPage(1)
    fetchNodes({
      page: 1,
      search_text: undefined,
      node_type: undefined,
      sort_by: 'id',
      sort_order: 'asc'
    })
  }

  // 查看节点详情
  const handleViewDetail = (node: GraphNode) => {
    setSelectedNode(node)
    setDetailModalVisible(true)
  }

  // 分页变化
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page)
    if (size && size !== pageSize) {
      setPageSize(size)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '节点ID',
      dataIndex: 'entity_id',
      key: 'entity_id',
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text copyable={{ text }}>{text}</Text>
        </Tooltip>
      )
    },
    {
      title: '节点名称',
      dataIndex: 'properties',
      key: 'entity_name',
      width: 150,
      ellipsis: true,
      render: (properties: Record<string, any>) => {
        const name = properties?.entity_name || properties?.name || properties?.label || '-'
        return (
          <Tooltip title={name}>
            <Text>{name}</Text>
          </Tooltip>
        )
      }
    },
    {
      title: '节点类型',
      dataIndex: 'entity_type',
      key: 'entity_type',
      width: 120,
      render: (type: string) => (
        <Tag color="blue">{type || '未知'}</Tag>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text>{text || '-'}</Text>
        </Tooltip>
      )
    },
    {
      title: '属性',
      dataIndex: 'properties',
      key: 'properties',
      width: 200,
      ellipsis: true,
      render: (properties: Record<string, any>) => {
        if (!properties || Object.keys(properties).length === 0) {
          return <Text>-</Text>
        }

        // 显示主要属性
        const mainProps = []
        if (properties.source_id) mainProps.push(`来源: ${properties.source_id}`)
        if (properties.doc_id) mainProps.push(`文档: ${properties.doc_id}`)
        if (properties.confidence) mainProps.push(`置信度: ${properties.confidence}`)

        const displayText = mainProps.length > 0 ? mainProps.join(', ') : JSON.stringify(properties)
        const tooltipText = JSON.stringify(properties, null, 2)

        return (
          <Tooltip title={<pre>{tooltipText}</pre>}>
            <Text>{displayText}</Text>
          </Tooltip>
        )
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: GraphNode) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div className="node-management">
      <Card>
        {/* 筛选区域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Input
              placeholder="搜索节点ID或名称"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="节点类型"
              value={nodeType}
              onChange={setNodeType}
              allowClear
              style={{ width: '100%' }}
            >
              {nodeTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: '100%' }}
            >
              <Option value="id">按ID排序</Option>
              <Option value="entity_name">按名称排序</Option>
              <Option value="entity_type">按类型排序</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              value={sortOrder}
              onChange={setSortOrder}
              style={{ width: '100%' }}
            >
              <Option value="asc">升序</Option>
              <Option value="desc">降序</Option>
            </Select>
          </Col>
          <Col span={7}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button icon={<ClearOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => fetchNodes()}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={nodes}
          rowKey="entity_id"
          loading={loading}
          pagination={false}
          scroll={{ x: 800 }}
          size="middle"
        />

        {/* 分页 */}
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
            onChange={handlePageChange}
            pageSizeOptions={['10', '20', '50', '100']}
          />
        </div>
      </Card>

      {/* 节点详情弹窗 */}
      <Modal
        title="节点详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedNode && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="节点ID">
              <Text copyable>{selectedNode.entity_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="节点名称">
              {selectedNode.entity_name || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="节点类型">
              <Tag color="blue">{selectedNode.entity_type || '未知'}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="描述">
              {selectedNode.description || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="属性">
              <TextArea
                value={JSON.stringify(selectedNode.properties || {}, null, 2)}
                readOnly
                rows={6}
                style={{ fontFamily: 'monospace' }}
              />
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  )
}

export default NodeManagement
