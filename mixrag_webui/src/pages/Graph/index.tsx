import React, { useState } from 'react'
import { Col, Row, Tabs } from 'antd'
import { Bar<PERSON>hartOutlined, EyeOutlined, NodeIndexOutlined, ShareAltOutlined } from '@ant-design/icons'
import PageWrapper from '../../components/common/PageWrapper'
import { GraphStats } from '../../components/Graph'
import GraphViewer from './GraphViewer'
import NodeManagement from './NodeManagement'
import EdgeManagement from './EdgeManagement'
import './index.less'

interface GraphPageProps {
  kb_id?: string
}

const GraphPage: React.FC<GraphPageProps> = ({ kb_id }) => {
  const [activeTab, setActiveTab] = useState('stats')

  const tabItems = [
    {
      key: 'stats',
      label: (
        <span>
          <BarChartOutlined />
          图统计
        </span>
      ),
      children: <GraphStats />
    },
    {
      key: 'visualization',
      label: (
        <span>
          <EyeOutlined />
          图可视化
        </span>
      ),
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <div style={{ height: '70vh', minHeight: '650px' }}>
              <GraphViewer />
            </div>
          </Col>
        </Row>
      )
    },
    {
      key: 'nodes',
      label: (
        <span>
          <NodeIndexOutlined />
          节点
        </span>
      ),
      children: <NodeManagement />
    },
    {
      key: 'edges',
      label: (
        <span>
          <ShareAltOutlined />
          边
        </span>
      ),
      children: <EdgeManagement />
    }
  ]

  return (
    <PageWrapper>
      <div className="graph-page" style={{ padding: '0 24px' }}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
          size="large"
          items={tabItems}
        />
      </div>
    </PageWrapper>
  )
}

export default GraphPage 