import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Input,
  Select,
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Row,
  Col,
  Form,
  Pagination,
  Typography,
  Descriptions,
  Modal,
  Spin
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FilterOutlined,
  ClearOutlined,
  ArrowRightOutlined
} from '@ant-design/icons'
import { fetchEdgesList, GraphEdge, EdgeListParams, PaginatedResponse } from '@/api/graphApi'

const { Option } = Select
const { Text } = Typography
const { TextArea } = Input

interface EdgeManagementProps {}

const EdgeManagement: React.FC<EdgeManagementProps> = () => {
  const [loading, setLoading] = useState(false)
  const [edges, setEdges] = useState<GraphEdge[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [searchText, setSearchText] = useState('')
  const [edgeType, setEdgeType] = useState<string | undefined>()
  const [sourceId, setSourceId] = useState('')
  const [targetId, setTargetId] = useState('')
  const [sortBy, setSortBy] = useState('source_id')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedEdge, setSelectedEdge] = useState<GraphEdge | null>(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [edgeTypes, setEdgeTypes] = useState<string[]>([])

  // 获取边列表
  const fetchEdges = async (params?: Partial<EdgeListParams>) => {
    setLoading(true)
    try {
      const requestParams: EdgeListParams = {
        page: currentPage,
        page_size: pageSize,
        search_text: searchText || undefined,
        edge_type: edgeType,
        source_id: sourceId || undefined,
        target_id: targetId || undefined,
        sort_by: sortBy,
        sort_order: sortOrder,
        ...params
      }

      const response: PaginatedResponse<GraphEdge> = await fetchEdgesList(requestParams)

      if (response.success) {
        setEdges(response.data.records)
        setTotal(response.data.pagination.total)

        // 提取边类型用于筛选
        const types = Array.from(new Set(response.data.records.map(edge => edge.relationship_type).filter(Boolean)))
        setEdgeTypes(prev => Array.from(new Set([...prev, ...types])))
      } else {
        message.error(response.message || '获取边列表失败')
      }
    } catch (error) {
      console.error('获取边列表失败:', error)
      message.error('获取边列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化加载
  useEffect(() => {
    fetchEdges()
  }, [currentPage, pageSize, sortBy, sortOrder])

  // 搜索处理
  const handleSearch = () => {
    setCurrentPage(1)
    fetchEdges({ page: 1 })
  }

  // 重置筛选
  const handleReset = () => {
    setSearchText('')
    setEdgeType(undefined)
    setSourceId('')
    setTargetId('')
    setSortBy('source_id')
    setSortOrder('asc')
    setCurrentPage(1)
    fetchEdges({
      page: 1,
      search_text: undefined,
      edge_type: undefined,
      source_id: undefined,
      target_id: undefined,
      sort_by: 'source_id',
      sort_order: 'asc'
    })
  }

  // 查看边详情
  const handleViewDetail = (edge: GraphEdge) => {
    setSelectedEdge(edge)
    setDetailModalVisible(true)
  }

  // 分页变化
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page)
    if (size && size !== pageSize) {
      setPageSize(size)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '源节点',
      dataIndex: 'source_id',
      key: 'source_id',
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text copyable={{ text }}>{text}</Text>
        </Tooltip>
      )
    },
    {
      title: '关系',
      key: 'relationship',
      width: 80,
      align: 'center' as const,
      render: () => <ArrowRightOutlined style={{ color: '#1890ff' }} />
    },
    {
      title: '目标节点',
      dataIndex: 'target_id',
      key: 'target_id',
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text copyable={{ text }}>{text}</Text>
        </Tooltip>
      )
    },
    {
      title: '关系类型',
      dataIndex: 'relationship_type',
      key: 'relationship_type',
      width: 120,
      render: (type: string) => (
        <Tag color="green">{type || '未知'}</Tag>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text>{text || '-'}</Text>
        </Tooltip>
      )
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      width: 80,
      render: (weight: number) => weight || '-'
    },
    {
      title: '属性',
      dataIndex: 'properties',
      key: 'properties',
      width: 200,
      ellipsis: true,
      render: (properties: Record<string, any>) => {
        if (!properties || Object.keys(properties).length === 0) {
          return <Text>-</Text>
        }

        // 显示主要属性
        const mainProps = []
        if (properties.keywords) mainProps.push(`关键词: ${properties.keywords}`)
        if (properties.source_chunk) mainProps.push(`来源块: ${properties.source_chunk}`)
        if (properties.doc_id) mainProps.push(`文档: ${properties.doc_id}`)

        const displayText = mainProps.length > 0 ? mainProps.join(', ') : JSON.stringify(properties)
        const tooltipText = JSON.stringify(properties, null, 2)

        return (
          <Tooltip title={<pre>{tooltipText}</pre>}>
            <Text>{displayText}</Text>
          </Tooltip>
        )
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: GraphEdge) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div className="edge-management">
      <Card>
        {/* 筛选区域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜索源节点、目标节点或关系类型"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="关系类型"
              value={edgeType}
              onChange={setEdgeType}
              allowClear
              style={{ width: '100%' }}
            >
              {edgeTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Input
              placeholder="源节点ID"
              value={sourceId}
              onChange={(e) => setSourceId(e.target.value)}
            />
          </Col>
          <Col span={4}>
            <Input
              placeholder="目标节点ID"
              value={targetId}
              onChange={(e) => setTargetId(e.target.value)}
            />
          </Col>
          <Col span={7}>
            <Space>
              <Select
                value={sortBy}
                onChange={setSortBy}
                style={{ width: 120 }}
              >
                <Option value="source_id">源节点</Option>
                <Option value="target_id">目标节点</Option>
                <Option value="relationship_type">关系类型</Option>
              </Select>
              <Select
                value={sortOrder}
                onChange={setSortOrder}
                style={{ width: 80 }}
              >
                <Option value="asc">升序</Option>
                <Option value="desc">降序</Option>
              </Select>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button icon={<ClearOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => fetchEdges()}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={edges}
          rowKey={(record) => `${record.source_id}-${record.target_id}`}
          loading={loading}
          pagination={false}
          scroll={{ x: 1000 }}
          size="middle"
        />

        {/* 分页 */}
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
            onChange={handlePageChange}
            pageSizeOptions={['10', '20', '50', '100']}
          />
        </div>
      </Card>

      {/* 边详情弹窗 */}
      <Modal
        title="边详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedEdge && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="源节点ID">
              <Text copyable>{selectedEdge.source_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="目标节点ID">
              <Text copyable>{selectedEdge.target_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="关系类型">
              <Tag color="green">{selectedEdge.relationship_type || '未知'}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="描述">
              {selectedEdge.description || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="权重">
              {selectedEdge.weight || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="属性">
              <TextArea
                value={JSON.stringify(selectedEdge.properties || {}, null, 2)}
                readOnly
                rows={6}
                style={{ fontFamily: 'monospace' }}
              />
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  )
}

export default EdgeManagement
