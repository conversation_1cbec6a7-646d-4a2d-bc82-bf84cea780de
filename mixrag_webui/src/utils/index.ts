/**
 * 工具函数统一导出模块
 * 
 * 集中导出所有工具函数和工具类：
 * 1. 分页工具
 * 2. 错误处理工具
 * 3. 日志工具
 * 4. 配置管理工具
 * 5. 其他通用工具
 */

// 分页工具
export { buildPaginationParams } from './pagination'

// 错误处理工具
export {
  ERROR_TYPES,
  ERROR_LEVELS,
  getErrorTypeByStatus,
  getUserFriendlyMessage,
  logError,
  handleApiError,
  withErrorHandler,
  wrapAsyncFunction
} from './errorHandler'

// 日志工具
export {
  Logger,
  LOG_LEVELS,
  createLogger,
  debug,
  info,
  warn,
  error,
  time,
  timeEnd,
  group,
  groupEnd,
  table
} from './logger'

// 默认导出
export { default as errorHandler } from './errorHandler'
export { default as logger } from './logger'
