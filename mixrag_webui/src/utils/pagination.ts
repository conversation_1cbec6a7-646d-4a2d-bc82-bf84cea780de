/**
 * 分页数据处理工具函数
 */

// 类型定义
export interface PaginationInfo {
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
}

export interface UnifiedResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
}

export interface PaginationResponse<T = any> {
  records: T[];
  pagination: PaginationInfo;
}

export interface ParsedPaginationData<T = any> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  filters?: Record<string, any>;
}

/**
 * 解析分页响应数据
 * @param response - 后端统一响应格式
 * @returns 解析后的分页数据
 */
export const parsePaginationResponse = <T = any>(
  response?: UnifiedResponse<PaginationResponse<T>>
): ParsedPaginationData<T> => {
  if (!response || !response.data) {
    return {
      data: [],
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      }
    }
  }

  return {
    data: response.data.records || [],
    pagination: {
      page: response.data.pagination?.page || 1,
      pageSize: response.data.pagination?.page_size || 10,
      total: response.data.pagination?.total || 0,
      totalPages: response.data.pagination?.total_pages || 0
    }
  }
}

/**
 * 构建分页请求参数
 * @param params - 分页参数
 * @returns 请求参数对象
 */
export const buildPaginationParams = (params: PaginationParams = {}): Record<string, any> => {
  const { page = 1, pageSize = 10, filters = {} } = params

  return {
    page,
    page_size: pageSize,
    ...filters
  }
}

/**
 * 转换 Ant Design 分页组件的参数格式
 * @param page - 页码
 * @param pageSize - 每页数量
 * @returns 标准分页参数
 */
export const convertAntdPaginationParams = (
  page: number,
  pageSize: number
): { page: number; pageSize: number } => {
  return {
    page,
    pageSize
  }
}