/**
 * 统一日志工具模块
 * 
 * 提供统一的日志记录功能：
 * 1. 分级日志记录
 * 2. 格式化日志输出
 * 3. 开发/生产环境适配
 * 4. 性能监控支持
 */

/**
 * 日志级别枚举
 */
export const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  SILENT: 4
}

/**
 * 日志级别名称映射
 */
const LOG_LEVEL_NAMES = {
  [LOG_LEVELS.DEBUG]: 'DEBUG',
  [LOG_LEVELS.INFO]: 'INFO',
  [LOG_LEVELS.WARN]: 'WARN',
  [LOG_LEVELS.ERROR]: 'ERROR'
}

/**
 * 日志级别颜色映射
 */
const LOG_LEVEL_COLORS = {
  [LOG_LEVELS.DEBUG]: '#6B7280',
  [LOG_LEVELS.INFO]: '#3B82F6',
  [LOG_LEVELS.WARN]: '#F59E0B',
  [LOG_LEVELS.ERROR]: '#EF4444'
}

/**
 * 日志级别emoji映射
 */
const LOG_LEVEL_EMOJIS = {
  [LOG_LEVELS.DEBUG]: '🔍',
  [LOG_LEVELS.INFO]: 'ℹ️',
  [LOG_LEVELS.WARN]: '⚠️',
  [LOG_LEVELS.ERROR]: '❌'
}

/**
 * Logger类
 */
class Logger {
  constructor(options = {}) {
    this.level = options.level || (import.meta.env.DEV ? LOG_LEVELS.DEBUG : LOG_LEVELS.INFO)
    this.prefix = options.prefix || ''
    this.enableColors = options.enableColors !== false
    this.enableTimestamp = options.enableTimestamp !== false
    this.enableEmojis = options.enableEmojis !== false
  }

  /**
   * 设置日志级别
   * @param {number} level - 日志级别
   */
  setLevel(level) {
    this.level = level
  }

  /**
   * 格式化时间戳
   * @returns {string} 格式化的时间戳
   */
  formatTimestamp() {
    const now = new Date()
    return now.toISOString().replace('T', ' ').slice(0, 19)
  }

  /**
   * 格式化日志消息
   * @param {number} level - 日志级别
   * @param {string} message - 日志消息
   * @param {Array} args - 额外参数
   * @returns {Array} 格式化后的参数数组
   */
  formatMessage(level, message, args) {
    const parts = []
    
    // 添加emoji
    if (this.enableEmojis) {
      parts.push(LOG_LEVEL_EMOJIS[level])
    }
    
    // 添加时间戳
    if (this.enableTimestamp) {
      parts.push(`[${this.formatTimestamp()}]`)
    }
    
    // 添加日志级别
    parts.push(`[${LOG_LEVEL_NAMES[level]}]`)
    
    // 添加前缀
    if (this.prefix) {
      parts.push(`[${this.prefix}]`)
    }
    
    // 添加消息
    parts.push(message)
    
    return [parts.join(' '), ...args]
  }

  /**
   * 输出日志
   * @param {number} level - 日志级别
   * @param {string} message - 日志消息
   * @param {...any} args - 额外参数
   */
  log(level, message, ...args) {
    if (level < this.level) {
      return
    }

    const formattedArgs = this.formatMessage(level, message, args)
    
    // 根据级别选择console方法
    switch (level) {
      case LOG_LEVELS.DEBUG:
        console.debug(...formattedArgs)
        break
      case LOG_LEVELS.INFO:
        console.info(...formattedArgs)
        break
      case LOG_LEVELS.WARN:
        console.warn(...formattedArgs)
        break
      case LOG_LEVELS.ERROR:
        console.error(...formattedArgs)
        break
    }
  }

  /**
   * 调试日志
   * @param {string} message - 日志消息
   * @param {...any} args - 额外参数
   */
  debug(message, ...args) {
    this.log(LOG_LEVELS.DEBUG, message, ...args)
  }

  /**
   * 信息日志
   * @param {string} message - 日志消息
   * @param {...any} args - 额外参数
   */
  info(message, ...args) {
    this.log(LOG_LEVELS.INFO, message, ...args)
  }

  /**
   * 警告日志
   * @param {string} message - 日志消息
   * @param {...any} args - 额外参数
   */
  warn(message, ...args) {
    this.log(LOG_LEVELS.WARN, message, ...args)
  }

  /**
   * 错误日志
   * @param {string} message - 日志消息
   * @param {...any} args - 额外参数
   */
  error(message, ...args) {
    this.log(LOG_LEVELS.ERROR, message, ...args)
  }

  /**
   * 性能计时开始
   * @param {string} label - 计时标签
   */
  time(label) {
    console.time(`⏱️ ${label}`)
  }

  /**
   * 性能计时结束
   * @param {string} label - 计时标签
   */
  timeEnd(label) {
    console.timeEnd(`⏱️ ${label}`)
  }

  /**
   * 分组日志开始
   * @param {string} label - 分组标签
   */
  group(label) {
    console.group(`📁 ${label}`)
  }

  /**
   * 分组日志结束
   */
  groupEnd() {
    console.groupEnd()
  }

  /**
   * 表格日志
   * @param {Array|Object} data - 表格数据
   */
  table(data) {
    console.table(data)
  }
}

// 创建默认logger实例
const defaultLogger = new Logger({
  prefix: 'MixRAG'
})

// 创建模块特定的logger
export const createLogger = (prefix, options = {}) => {
  return new Logger({
    ...options,
    prefix
  })
}

// 导出默认logger的方法
export const debug = (...args) => defaultLogger.debug(...args)
export const info = (...args) => defaultLogger.info(...args)
export const warn = (...args) => defaultLogger.warn(...args)
export const error = (...args) => defaultLogger.error(...args)
export const time = (...args) => defaultLogger.time(...args)
export const timeEnd = (...args) => defaultLogger.timeEnd(...args)
export const group = (...args) => defaultLogger.group(...args)
export const groupEnd = (...args) => defaultLogger.groupEnd(...args)
export const table = (...args) => defaultLogger.table(...args)

// 导出Logger类
export { Logger }

// 默认导出
export default defaultLogger
