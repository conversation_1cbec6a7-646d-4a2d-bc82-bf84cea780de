/**
 * 错误处理工具模块
 * 
 * 提供统一的错误处理功能：
 * 1. 错误信息格式化
 * 2. 错误类型分类
 * 3. 错误日志记录
 * 4. 用户友好的错误提示
 */

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
  NETWORK: 'network',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  VALIDATION: 'validation',
  SERVER: 'server',
  CLIENT: 'client',
  UNKNOWN: 'unknown'
}

/**
 * 错误级别枚举
 */
export const ERROR_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

/**
 * 根据HTTP状态码确定错误类型
 * @param {number} status - HTTP状态码
 * @returns {string} 错误类型
 */
export const getErrorTypeByStatus = (status) => {
  if (status === 401) return ERROR_TYPES.AUTHENTICATION
  if (status === 403) return ERROR_TYPES.AUTHORIZATION
  if (status >= 400 && status < 500) return ERROR_TYPES.CLIENT
  if (status >= 500) return ERROR_TYPES.SERVER
  return ERROR_TYPES.UNKNOWN
}

/**
 * 获取用户友好的错误消息
 * @param {Error|string} error - 错误对象或错误消息
 * @param {number} status - HTTP状态码
 * @returns {string} 用户友好的错误消息
 */
export const getUserFriendlyMessage = (error, status) => {
  const message = error instanceof Error ? error.message : String(error)
  
  // 根据状态码返回友好消息
  switch (status) {
    case 400:
      return '请求参数有误，请检查输入内容'
    case 401:
      return '登录已过期，请重新登录'
    case 403:
      return '您没有权限执行此操作'
    case 404:
      return '请求的资源不存在'
    case 429:
      return '请求过于频繁，请稍后再试'
    case 500:
      return '服务器内部错误，请稍后重试'
    case 502:
      return '网关错误，请稍后重试'
    case 503:
      return '服务暂时不可用，请稍后重试'
    default:
      // 检查是否是网络错误
      if (message.includes('NetworkError') || 
          message.includes('Failed to fetch') ||
          message.includes('Network request failed')) {
        return '网络连接失败，请检查网络连接'
      }
      
      // 返回原始消息或默认消息
      return message || '操作失败，请重试'
  }
}

/**
 * 记录错误日志
 * @param {Error|string} error - 错误对象或错误消息
 * @param {Object} context - 错误上下文信息
 */
export const logError = (error, context = {}) => {
  const errorInfo = {
    message: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    ...context
  }
  
  console.error('🚨 错误日志:', errorInfo)
  
  // 在生产环境中，可以将错误发送到日志服务
  if (import.meta.env.PROD) {
    // TODO: 发送到错误监控服务
  }
}

/**
 * 处理API错误的统一函数
 * @param {Error} error - 错误对象
 * @param {Object} options - 处理选项
 * @returns {Object} 处理后的错误信息
 */
export const handleApiError = (error, options = {}) => {
  const {
    showNotification = true,
    logToConsole = true,
    context = {}
  } = options
  
  let status = null
  const message = error.message
  
  // 尝试从错误消息中提取状态码
  const statusMatch = message.match(/^(\d{3})\s/)
  if (statusMatch) {
    status = parseInt(statusMatch[1], 10)
  }
  
  const errorType = getErrorTypeByStatus(status)
  const userMessage = getUserFriendlyMessage(error, status)
  
  const errorInfo = {
    type: errorType,
    status,
    message: userMessage,
    originalMessage: message,
    timestamp: new Date().toISOString()
  }
  
  // 记录错误日志
  if (logToConsole) {
    logError(error, { ...context, errorInfo })
  }
  
  // 显示用户通知
  if (showNotification && window.antd?.message) {
    window.antd.message.error(userMessage)
  }
  
  return errorInfo
}

/**
 * 创建错误处理装饰器
 * @param {Object} options - 装饰器选项
 * @returns {Function} 装饰器函数
 */
export const withErrorHandler = (options = {}) => {
  return (target, propertyKey, descriptor) => {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args) {
      try {
        return await originalMethod.apply(this, args)
      } catch (error) {
        handleApiError(error, {
          ...options,
          context: {
            method: propertyKey,
            args: args.length > 0 ? '[arguments]' : 'none'
          }
        })
        throw error
      }
    }
    
    return descriptor
  }
}

/**
 * 异步函数错误处理包装器
 * @param {Function} asyncFn - 异步函数
 * @param {Object} options - 处理选项
 * @returns {Function} 包装后的函数
 */
export const wrapAsyncFunction = (asyncFn, options = {}) => {
  return async (...args) => {
    try {
      return await asyncFn(...args)
    } catch (error) {
      handleApiError(error, options)
      throw error
    }
  }
}

export default {
  ERROR_TYPES,
  ERROR_LEVELS,
  getErrorTypeByStatus,
  getUserFriendlyMessage,
  logError,
  handleApiError,
  withErrorHandler,
  wrapAsyncFunction
}
