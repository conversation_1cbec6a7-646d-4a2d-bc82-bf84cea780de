/**
 * 应用设置状态管理
 *
 * 统一管理应用的各种设置，包括：
 * - 主题设置
 * - 图可视化设置
 * - 检索设置
 * - 认证设置
 * - 界面显示设置
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { createSelectors } from '@/lib/utils'
import { defaultQueryLabel } from '@/lib/constants'

// 类型定义
export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  isError?: boolean
  mermaidRendered?: boolean
}

export interface QueryRequest {
  query?: string
  mode: string
  response_type: string
  top_k: number
  max_token_for_text_unit: number
  max_token_for_global_context: number
  max_token_for_local_context: number
  only_need_context: boolean
  only_need_prompt: boolean
  stream: boolean
  history_turns: number
  hl_keywords: string[]
  ll_keywords: string[]
  user_prompt: string
}

export type Theme = 'dark' | 'light' | 'system'
export type Tab = 'documents' | 'knowledge-graph' | 'retrieval' | 'api'

// 图可视化设置接口
interface GraphSettings {
  showPropertyPanel: boolean
  showNodeSearchBar: boolean
  showLegend: boolean
  showNodeLabel: boolean
  enableNodeDrag: boolean
  showEdgeLabel: boolean
  enableHideUnselectedEdges: boolean
  enableEdgeEvents: boolean
  minEdgeSize: number
  maxEdgeSize: number
  graphQueryMaxDepth: number
  graphMaxNodes: number
  graphLayoutMaxIterations: number
}

// 检索设置接口
interface RetrievalSettings {
  queryLabel: string
  retrievalHistory: Message[]
  querySettings: Omit<QueryRequest, 'query'>
}

// 应用设置接口
interface AppSettings {
  theme: Theme
  enableHealthCheck: boolean
  currentTab: Tab
  showFileName: boolean
  apiKey: string | null
}

// 主设置状态接口
interface SettingsState extends GraphSettings, RetrievalSettings, AppSettings {
  // 图设置更新方法
  setShowLegend: (show: boolean) => void
  setMinEdgeSize: (size: number) => void
  setMaxEdgeSize: (size: number) => void
  setGraphQueryMaxDepth: (depth: number) => void
  setGraphMaxNodes: (nodes: number) => void
  setGraphLayoutMaxIterations: (iterations: number) => void

  // 检索设置更新方法
  setQueryLabel: (queryLabel: string) => void
  setRetrievalHistory: (history: Message[]) => void
  updateQuerySettings: (settings: Partial<QueryRequest>) => void

  // 应用设置更新方法
  setTheme: (theme: Theme) => void
  setEnableHealthCheck: (enable: boolean) => void
  setCurrentTab: (tab: Tab) => void
  setShowFileName: (show: boolean) => void
  setApiKey: (key: string | null) => void
}

// 默认设置值
const DEFAULT_SETTINGS = {
  // 应用设置
  theme: 'system' as Theme,
  enableHealthCheck: true,
  currentTab: 'documents' as Tab,
  showFileName: false,
  apiKey: null,

  // 图可视化设置
  showPropertyPanel: true,
  showNodeSearchBar: true,
  showLegend: false,
  showNodeLabel: true,
  enableNodeDrag: true,
  showEdgeLabel: false,
  enableHideUnselectedEdges: true,
  enableEdgeEvents: false,
  minEdgeSize: 1,
  maxEdgeSize: 1,
  graphQueryMaxDepth: 3,
  graphMaxNodes: 1000,
  graphLayoutMaxIterations: 15,

  // 检索设置
  queryLabel: defaultQueryLabel,
  retrievalHistory: [] as Message[],
  querySettings: {
    mode: 'global',
    response_type: 'Multiple Paragraphs',
    top_k: 10,
    max_token_for_text_unit: 4000,
    max_token_for_global_context: 4000,
    max_token_for_local_context: 4000,
    only_need_context: false,
    only_need_prompt: false,
    stream: true,
    history_turns: 3,
    hl_keywords: [],
    ll_keywords: [],
    user_prompt: ''
  } as Omit<QueryRequest, 'query'>
}

const useSettingsStoreBase = create<SettingsState>()(
  persist(
    (set) => ({
      ...DEFAULT_SETTINGS,

      setTheme: (theme: Theme) => set({ theme }),

      setGraphLayoutMaxIterations: (iterations: number) =>
        set({
          graphLayoutMaxIterations: iterations
        }),

      setQueryLabel: (queryLabel: string) =>
        set({
          queryLabel
        }),

      setGraphQueryMaxDepth: (depth: number) => set({ graphQueryMaxDepth: depth }),

      setGraphMaxNodes: (nodes: number) => set({ graphMaxNodes: nodes }),

      setMinEdgeSize: (size: number) => set({ minEdgeSize: size }),

      setMaxEdgeSize: (size: number) => set({ maxEdgeSize: size }),

      setEnableHealthCheck: (enable: boolean) => set({ enableHealthCheck: enable }),

      setApiKey: (apiKey: string | null) => set({ apiKey }),

      setCurrentTab: (tab: Tab) => set({ currentTab: tab }),

      setRetrievalHistory: (history: Message[]) => set({ retrievalHistory: history }),

      updateQuerySettings: (settings: Partial<QueryRequest>) =>
        set((state) => ({
          querySettings: { ...state.querySettings, ...settings }
        })),

      setShowFileName: (show: boolean) => set({ showFileName: show }),
      setShowLegend: (show: boolean) => set({ showLegend: show })
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => localStorage),
      version: 13,
      migrate: (state: any, version: number) => {
        if (version < 2) {
          state.showEdgeLabel = false
        }
        if (version < 3) {
          state.queryLabel = defaultQueryLabel
        }
        if (version < 4) {
          state.showPropertyPanel = true
          state.showNodeSearchBar = true
          state.showNodeLabel = true
          state.enableHealthCheck = true
          state.apiKey = null
        }
        if (version < 5) {
          state.currentTab = 'documents'
        }
        if (version < 6) {
          state.querySettings = {
            mode: 'global',
            response_type: 'Multiple Paragraphs',
            top_k: 10,
            max_token_for_text_unit: 4000,
            max_token_for_global_context: 4000,
            max_token_for_local_context: 4000,
            only_need_context: false,
            only_need_prompt: false,
            stream: true,
            history_turns: 3,
            hl_keywords: [],
            ll_keywords: []
          }
          state.retrievalHistory = []
        }
        if (version < 7) {
          state.graphQueryMaxDepth = 3
          state.graphLayoutMaxIterations = 15
        }
        if (version < 8) {
          state.graphMinDegree = 0
        }
        if (version < 9) {
          state.showFileName = false
        }
        if (version < 10) {
          delete state.graphMinDegree // 删除废弃参数
          state.graphMaxNodes = 1000  // 添加新参数
        }
        if (version < 11) {
          state.minEdgeSize = 1
          state.maxEdgeSize = 1
        }
        if (version < 12) {
          // Clear retrieval history to avoid compatibility issues with MessageWithError type
          state.retrievalHistory = []
        }
        if (version < 13) {
          // Add user_prompt field for older versions
          if (state.querySettings) {
            state.querySettings.user_prompt = ''
          }
        }
        return state
      }
    }
  )
)

const useSettingsStore = createSelectors(useSettingsStoreBase)

export { useSettingsStore, type Theme }
