/**
 * 应用主布局组件
 *
 * 提供应用的整体布局结构，包括：
 * - 左侧导航栏
 * - 顶部标题栏
 * - 主内容区域
 */

import React from 'react'
import { Layout, Typography } from 'antd'
import Sidebar from './Sidebar'

const { Header, Content } = Layout
const { Title } = Typography

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 左侧导航栏 */}
      <Sidebar />

      <Layout>
        {/* 顶部标题栏 */}
        <Header style={{
          background: '#fff',
          padding: '0 24px',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            MIXRAG
          </Title>
        </Header>

        {/* 主内容区域 */}
        <Content style={{ margin: '24px' }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}

export default AppLayout