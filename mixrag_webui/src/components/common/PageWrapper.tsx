/**
 * 页面包装器组件
 *
 * 为所有页面提供统一的布局和样式：
 * 1. 统一的页面容器样式
 * 2. 默认的空状态显示
 * 3. 响应式布局支持
 */

import React from 'react'
import PropTypes from 'prop-types'

/**
 * 页面包装器组件
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 子组件
 * @param {string} props.title - 页面标题
 * @param {string} props.className - 额外的CSS类名
 */
const PageWrapper = ({
  children,
  title,
  className = ''
}) => (
  <div className={`page-content ${className}`}>
    {title && (
      <div className="page-header">
        <h1 className="page-title">{title}</h1>
      </div>
    )}
    <div className="page-body">
      {children || (
        <div className="empty-state">
          <p>功能开发中...</p>
        </div>
      )}
    </div>
  </div>
)

PageWrapper.propTypes = {
  children: PropTypes.node,
  title: PropTypes.string,
  className: PropTypes.string
}

export default PageWrapper