import { useCallback, useEffect, useState } from 'react'
import { AsyncSelect } from '@/components/ui/AsyncSelect'
import { useSettingsStore } from '@/stores/settings'
import { useGraphStore } from '@/stores/graph'
import { labelListLimit, controlButtonVariant } from '@/lib/constants'
import MiniSearch from 'minisearch'
import { RefreshCw } from 'lucide-react'
import Button from '@/components/ui/Button'

const GraphLabels = () => {
  const label = useSettingsStore.use.queryLabel()
  const allDatabaseLabels = useGraphStore.use.allDatabaseLabels()
  const labelsFetchAttempted = useGraphStore.use.labelsFetchAttempted()
  const isLoadingLabels = useGraphStore.use.isLoadingLabels()
  const fetchAllDatabaseLabels = useGraphStore.use.fetchAllDatabaseLabels()

  // 添加一个状态来强制重新渲染
  const [labelsReady, setLabelsReady] = useState(false)

  // 初始化标签数据
  useEffect(() => {
    const initLabels = async () => {
      if (allDatabaseLabels.length <= 1 && !isLoadingLabels) {
        try {
          await fetchAllDatabaseLabels()
        } catch (error) {
          console.error('Failed to initialize labels:', error)
        }
      }
    }

    initLabels()
  }, []) // 只在组件挂载时执行一次

  // 监听标签数据变化，设置ready状态
  useEffect(() => {
    if (allDatabaseLabels.length > 1) {
      setLabelsReady(true)
    }
  }, [allDatabaseLabels.length])

  // Remove initial label fetch effect as it's now handled by fetchGraph based on lastSuccessfulQueryLabel

  const fetchData = useCallback(
    async (query?: string): Promise<string[]> => {
      // 确保标签数据已加载
      let labels = allDatabaseLabels

      // 如果标签数据还没有加载（只有默认的*），尝试加载
      if (labels.length <= 1 && !isLoadingLabels) {
        try {
          await fetchAllDatabaseLabels()
          // 重新获取最新的标签数据
          labels = useGraphStore.getState().allDatabaseLabels
        } catch (error) {
          console.error('Failed to load labels:', error)
          labels = ['*']
        }
      }

      // 创建搜索引擎
      const searchEngine = new MiniSearch({
        idField: 'id',
        fields: ['value'],
        searchOptions: {
          prefix: true,
          fuzzy: 0.2,
          boost: {
            label: 2
          }
        }
      })

      // 添加文档
      const documents = labels.map((str, index) => ({ id: index, value: str }))
      searchEngine.addAll(documents)

      let result: string[] = labels
      if (query) {
        // Search labels using MiniSearch
        result = searchEngine.search(query).map((r: { id: number }) => labels[r.id])

        // Add middle-content matching if results are few
        // This enables matching content in the middle of text, not just from the beginning
        if (result.length < 15) {
          // Get already matched labels to avoid duplicates
          const matchedLabels = new Set(result)

          // Perform middle-content matching on all labels
          const middleMatchResults = labels.filter(label => {
            // Skip already matched labels
            if (matchedLabels.has(label)) return false

            // Match if label contains query string but doesn't start with it
            return label &&
                   typeof label === 'string' &&
                   !label.toLowerCase().startsWith(query.toLowerCase()) &&
                   label.toLowerCase().includes(query.toLowerCase())
          })

          // Merge results
          result = [...result, ...middleMatchResults]
        }
      }

      return result.length <= labelListLimit
        ? result
        : [...result.slice(0, labelListLimit), '...']
    },
    [allDatabaseLabels, isLoadingLabels]
  )

  // Validate label
  useEffect(() => {

    if (labelsFetchAttempted) {
      if (allDatabaseLabels.length > 1) {
        if (label && label !== '*' && !allDatabaseLabels.includes(label)) {
          console.log(`Label "${label}" not in available labels, setting to "*"`)
          useSettingsStore.getState().setQueryLabel('*')
        } else {
          console.log(`Label "${label}" is valid`)
        }
      } else if (label && allDatabaseLabels.length <= 1 && label && label !== '*' && label !== '') {
        console.log('Available labels list is empty, setting label to empty')
        useSettingsStore.getState().setQueryLabel('')
      }
      useGraphStore.getState().setLabelsFetchAttempted(false)
    }

  }, [allDatabaseLabels, label, labelsFetchAttempted])

  const handleRefresh = useCallback(() => {
    console.log('🔄 Refresh button clicked - forcing reload')

    // Set force reload flag
    useGraphStore.getState().setForceReload(true)

    // Reset fetch status flags
    useGraphStore.getState().setLabelsFetchAttempted(false)
    useGraphStore.getState().setGraphDataFetchAttempted(false)

    // Clear last successful query label to ensure labels are fetched
    useGraphStore.getState().setLastSuccessfulQueryLabel('')

    // Get current label
    const currentLabel = useSettingsStore.getState().queryLabel

    // If current label is empty, use default label '*'
    if (!currentLabel) {
      useSettingsStore.getState().setQueryLabel('*')
    } else {
      // Trigger data reload by temporarily changing the label
      useSettingsStore.getState().setQueryLabel('')
      setTimeout(() => {
        useSettingsStore.getState().setQueryLabel(currentLabel)
      }, 0)
    }
  }, [])

  return (
    <div className="flex items-center">
      {/* Always show refresh button */}
      <Button
        size="icon"
        variant={controlButtonVariant}
        onClick={handleRefresh}
        tooltip="重载图形数据(添加文件后需重载)"
        className="mr-2"
      >
        <RefreshCw className="h-4 w-4" />
      </Button>
      {labelsReady ? (
        <AsyncSelect<string>
          key={`labels-${allDatabaseLabels.length}`} // 强制重新渲染
          className="min-w-[300px]"
          triggerClassName="max-h-8"
          searchInputClassName="max-h-8"
          triggerTooltip="选择查询标签"
          fetcher={fetchData}
          preload={false}
          renderOption={(item) => <div>{item}</div>}
          getOptionValue={(item) => item}
          getDisplayValue={(item) => <div>{item}</div>}
          notFound={<div className="py-6 text-center text-sm">未找到标签</div>}
          label="标签"
          placeholder="搜索标签..."
          value={label !== null ? label : '*'}
          onChange={(newLabel) => {
            const currentLabel = useSettingsStore.getState().queryLabel

            // select the last item means query all
            if (newLabel === '...') {
              newLabel = '*'
            }

            // Handle reselecting the same label
            if (newLabel === currentLabel && newLabel !== '*') {
              newLabel = '*'
            }

            // Reset graphDataFetchAttempted flag to ensure data fetch is triggered
            useGraphStore.getState().setGraphDataFetchAttempted(false)

            // Update the label to trigger data loading
            useSettingsStore.getState().setQueryLabel(newLabel)
          }}
          clearable={false}  // Prevent clearing value on reselect
        />
      ) : (
        <div className="min-w-[300px] max-h-8 px-3 py-2 border border-input bg-background rounded-md text-sm">
          加载标签中...
        </div>
      )}
    </div>
  )
}

export default GraphLabels
