import { PencilIcon } from 'lucide-react'
import Text from '@/components/ui/Text'

interface PropertyNameProps {
  name: string
}

const propertyNameMap: { [key: string]: string } = {
  description: '描述',
  entity_id: '名称',
  entity_type: '类型',
  source_id: '信源ID',
  Neighbour: '邻接',
  file_path: '信源',
  keywords: 'Keys',
  weight: '权重'
}

export const PropertyName = ({ name }: PropertyNameProps) => {
  const getPropertyNameTranslation = (propName: string) => {
    return propertyNameMap[propName] || propName
  }

  return (
    <span className="text-primary/60 tracking-wide whitespace-nowrap">
      {getPropertyNameTranslation(name)}
    </span>
  )
}

interface EditIconProps {
  onClick: () => void
}

export const EditIcon = ({ onClick }: EditIconProps) => (
  <div>
    <PencilIcon
      className="h-3 w-3 text-gray-500 hover:text-gray-700 cursor-pointer"
      onClick={onClick}
    />
  </div>
)

interface PropertyValueProps {
  value: any
  onClick?: () => void
  tooltip?: string
}

export const PropertyValue = ({ value, onClick, tooltip }: PropertyValueProps) => (
  <div className="flex items-center gap-1 overflow-hidden">
    <Text
      className="hover:bg-primary/20 rounded p-1 overflow-hidden text-ellipsis whitespace-nowrap"
      tooltipClassName="max-w-80 -translate-x-15"
      text={value}
      tooltip={tooltip || (typeof value === 'string' ? value : JSON.stringify(value, null, 2))}
      side="left"
      onClick={onClick}
    />
  </div>
)
