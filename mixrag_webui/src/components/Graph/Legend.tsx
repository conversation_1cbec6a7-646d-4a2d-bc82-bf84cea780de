import React from 'react'
import { useGraphStore } from '@/stores/graph'
import { Card } from '@/components/ui/Card'
import { ScrollArea } from '@/components/ui/ScrollArea'

interface LegendProps {
  className?: string
}

const nodeTypeMap: { [key: string]: string } = {
  person: '人物角色',
  category: '分类',
  geo: '地理名称',
  location: '位置',
  organization: '组织机构',
  event: '事件',
  equipment: '装备',
  weapon: '武器',
  animal: '动物',
  unknown: '未知',
  object: '物品',
  group: '群组',
  technology: '技术'
}

const Legend: React.FC<LegendProps> = ({ className }) => {
  const typeColorMap = useGraphStore.use.typeColorMap()

  if (!typeColorMap || typeColorMap.size === 0) {
    return null
  }

  return (
    <Card className={`p-2 max-w-xs ${className}`}>
      <h3 className="text-sm font-medium mb-2">图例</h3>
      <ScrollArea className="max-h-80">
        <div className="flex flex-col gap-1">
          {Array.from(typeColorMap.entries()).map(([type, color]) => (
            <div key={type} className="flex items-center gap-2">
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: color }}
              />
              <span className="text-xs truncate" title={type}>
                {nodeTypeMap[type.toLowerCase()] || type}
              </span>
            </div>
          ))}
        </div>
      </ScrollArea>
    </Card>
  )
}

export default Legend
