import React, { useMemo, useState, useEffect } from 'react'
import { Card, Statistic, Row, Col, Tag, Alert, Button } from 'antd'
import { BarChartOutlined, NodeIndexOutlined, BranchesOutlined, ReloadOutlined } from '@ant-design/icons'
import { useGraphStore } from '../../stores/graph'
import { fetchGraphStats } from '../../api/graphApi'

const GraphStats = () => {
  const rawGraph = useGraphStore.use.rawGraph()
  const isFetching = useGraphStore.use.isFetching()
  const _graphIsEmpty = useGraphStore.use.graphIsEmpty() // 使用下划线前缀表示未使用

  // 独立的统计数据状态
  const [statsData, setStatsData] = useState(null)
  const [statsLoading, setStatsLoading] = useState(false)
  const [statsError, setStatsError] = useState(null)

  // 加载统计数据的函数
  const loadStatsData = async () => {
    setStatsLoading(true)
    setStatsError(null)
    try {
      const response = await fetchGraphStats()
      setStatsData(response)
    } catch (error) {
      console.error('Failed to load graph stats:', error)
      setStatsError(error.message || '加载统计数据失败')
    } finally {
      setStatsLoading(false)
    }
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadStatsData()
  }, [])

  // 计算图统计信息（优先使用API数据，回退到rawGraph数据）
  const graphStats = useMemo(() => {
    // 优先使用API数据
    if (statsData) {
      // 处理可能的不同API响应格式
      let processedStats = statsData

      // 如果API返回的是包装格式（如 {data: {...}, success: true}）
      if (statsData.data && typeof statsData.data === 'object') {
        processedStats = statsData.data
      }

      // 如果API返回的是图数据而不是统计数据，从图数据计算统计
      if (processedStats.nodes && processedStats.edges) {
        const nodes = processedStats.nodes || []
        const edges = processedStats.edges || []

        // 计算节点类型分布
        const nodeTypes = {}
        nodes.forEach(node => {
          const entityType = node.properties?.entity_type || node.labels?.[0] || 'UNKNOWN'
          nodeTypes[entityType] = (nodeTypes[entityType] || 0) + 1
        })

        // 计算度数统计
        const degrees = nodes.map(node => {
          // 计算每个节点的度数
          return edges.filter(edge => edge.source === node.id || edge.target === node.id).length
        })
        const totalDegree = degrees.reduce((sum, degree) => sum + degree, 0)
        const avgDegree = nodes.length > 0 ? totalDegree / nodes.length : 0
        const maxDegree = Math.max(...degrees, 0)

        return {
          total_nodes: nodes.length,
          total_edges: edges.length,
          avg_degree: avgDegree,
          max_degree: maxDegree,
          node_types: nodeTypes,
          density: nodes.length > 1 ?
            (2 * edges.length) / (nodes.length * (nodes.length - 1)) : 0
        }
      }

      // 如果API返回的就是统计数据格式，直接使用
      return processedStats
    }

    // 回退到rawGraph数据（用于图可视化tab已加载数据的情况）
    if (!rawGraph || !rawGraph.nodes || !rawGraph.edges) {
      return null
    }

    // 使用 rawGraph 数据计算统计信息

    // 计算节点类型分布
    const nodeTypes = {}
    rawGraph.nodes.forEach(node => {
      const entityType = node.properties?.entity_type || 'UNKNOWN'
      nodeTypes[entityType] = (nodeTypes[entityType] || 0) + 1
    })

    // 计算度数统计
    const degrees = rawGraph.nodes.map(node => node.degree || 0)
    const totalDegree = degrees.reduce((sum, degree) => sum + degree, 0)
    const avgDegree = rawGraph.nodes.length > 0 ? totalDegree / rawGraph.nodes.length : 0
    const maxDegree = Math.max(...degrees, 0)

    return {
      total_nodes: rawGraph.nodes.length,
      total_edges: rawGraph.edges.length,
      avg_degree: avgDegree,
      max_degree: maxDegree,
      node_types: nodeTypes,
      density: rawGraph.nodes.length > 1 ?
        (2 * rawGraph.edges.length) / (rawGraph.nodes.length * (rawGraph.nodes.length - 1)) : 0
    }
  }, [statsData, rawGraph])

  const renderHealthStatus = () => {
    if (statsLoading || isFetching) {
      return (
        <Alert
          message="图数据状态: 加载中"
          description="正在从API加载图统计信息..."
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )
    }

    if (statsError) {
      return (
        <Alert
          message="图数据状态: 加载失败"
          description={`加载统计数据失败: ${statsError}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )
    }

    if (!graphStats) {
      return (
        <Alert
          message="图数据状态: 无数据"
          description="未找到图数据"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )
    }

    return (
      <Alert
        message="图数据状态: 正常"
        description={`已成功加载图数据 ${graphStats?.total_nodes || 0}个节点, ${graphStats?.total_edges || 0}条边`}
        type="success"
        showIcon
        style={{ marginBottom: 16 }}
      />
    )
  }

  const renderNodeTypes = () => {
    if (!graphStats?.node_types) return null

    const nodeTypes = Object.entries(graphStats.node_types || {})
    const colors = {
      'person': 'red',
      'organization': 'blue', 
      'geo': 'green',
      'event': 'orange',
      'category': 'purple',
      'UNKNOWN': 'default'
    }

    return (
      <div>
        <h4 style={{ marginBottom: 12 }}>节点类型分布:</h4>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
          {nodeTypes.map(([type, count]) => (
            <Tag
              key={type}
              color={colors[type] || 'cyan'}
              style={{ margin: 0, padding: '4px 8px' }}
            >
              {type}: {count}
            </Tag>
          ))}
        </div>
      </div>
    )
  }

  const handleRefresh = () => {
    // 重新加载统计数据
    loadStatsData()
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>
            <BarChartOutlined style={{ marginRight: 8 }} />
            图统计信息
          </span>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={statsLoading || isFetching}
            size="small"
          >
            刷新
          </Button>
        </div>
      }
      style={{ marginBottom: 16 }}
    >
      {renderHealthStatus()}
      
      {graphStats ? (
        <>
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Statistic
                title="总节点数"
                value={graphStats?.total_nodes || 0}
                prefix={<NodeIndexOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="总边数"
                value={graphStats?.total_edges || 0}
                prefix={<BranchesOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="平均度数"
                value={graphStats?.avg_degree || 0}
                precision={2}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="图密度"
                value={graphStats?.density || 0}
                precision={4}
                valueStyle={{ color: '#f5222d' }}
              />
            </Col>
          </Row>

          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={12}>
              <Statistic
                title="最大度数"
                value={graphStats?.max_degree || 0}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="节点类型数"
                value={Object.keys(graphStats?.node_types || {}).length}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Col>
          </Row>

          {renderNodeTypes()}

        </>
      ) : (
        <div style={{ textAlign: 'center', padding: 40 }}>
          <p style={{ color: '#999' }}>
            {(statsLoading || isFetching) ? '正在加载图数据...' : '暂无图数据'}
          </p>
        </div>
      )}
    </Card>
  )
}

export default GraphStats 