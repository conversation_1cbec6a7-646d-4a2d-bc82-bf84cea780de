import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Select, 
  InputNumber, 
  Switch, 
  Input,
  Divider, 
  Space,
  Typography,
  Tooltip
} from 'antd'
import { SettingOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import './QuerySettings.less'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

const QuerySettings = ({ 
  onSettingsChange, 
  initialSettings = {} 
}) => {
  const [settings, setSettings] = useState({
    mode: 'naive',
    response_format: '多段落',
    top_k: 10,
    similarity_threshold: 0.75,
    text_unit_max_tokens: 4000,
    global_context_max_tokens: 4000,
    history_turns: 3,
    user_prompt: '',
    context_only: false,
    prompt_only: false,
    stream: false,
    ...initialSettings
  })

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    onSettingsChange?.(newSettings)
  }

  return (
    <div className="query-settings">
      <Card 
        title={
          <Space>
            <SettingOutlined />
            <span>检索设置</span>
          </Space>
        }
        size="small"
        className="settings-card"
      >
        <Form layout="vertical" size="small">
          {/* 查询模式 */}
          <Form.Item 
            label={
              <Space>
                <Text>查询模式</Text>
                <Tooltip title="选择不同的查询策略">
                  <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }
          >
            <Select
              value={settings.mode}
              onChange={(value) => handleSettingChange('mode', value)}
              style={{ width: '100%' }}
            >
              <Option value="naive">Naive</Option>
              <Option value="global">Global</Option>
              <Option value="mix">Mix</Option>
            </Select>
          </Form.Item>

          {/* 相似度 - 仅在Naive和mix模式下显示 */}
          { (settings.mode === 'naive' || settings.mode === 'mix') && (
            <Form.Item 
              label={
                <Space>
                  <Text>相似度阈值</Text>
                  <Tooltip title="设置查询结果的最小相似度，范围(0-1)">
                    <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                  </Tooltip>
                </Space>
              }
            >
              <InputNumber
                value={settings.similarity_threshold}
                onChange={(value) => handleSettingChange('similarity_threshold', value)}
                min={0.01}
                max={0.99}
                step={0.01}
                precision={2}
                style={{ width: '100%' }}
                placeholder="0.01-0.99"
              />
            </Form.Item>
          )}

          {/* 响应格式 */}
          <Form.Item 
            label={
              <Space>
                <Text>响应格式</Text>
                <Tooltip title="选择响应内容的格式">
                  <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }
          >
            <Select
              value={settings.response_format}
              onChange={(value) => handleSettingChange('response_format', value)}
              style={{ width: '100%' }}
            >
              <Option value="单段落">单段落</Option>
              <Option value="多段落">多段落</Option>
              <Option value="要点">要点</Option>
            </Select>
          </Form.Item>

          {/* Top K结果 */}
          <Form.Item 
            label={
              <Space>
                <Text>Top K结果</Text>
                <Tooltip title="返回的相关结果数量">
                  <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }
          >
            <InputNumber
              value={settings.top_k}
              onChange={(value) => handleSettingChange('top_k', value)}
              min={1}
              max={50}
              style={{ width: '100%' }}
            />
          </Form.Item>

          {/* 历史轮次 */}
          <Form.Item 
            label={
              <Space>
                <Text>历史轮次</Text>
                <Tooltip title="对话中考虑的历史消息轮数">
                  <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }
          >
            <InputNumber
              value={settings.history_turns}
              onChange={(value) => handleSettingChange('history_turns', value)}
              min={0}
              max={20}
              style={{ width: '100%' }}
            />
          </Form.Item>

          {/* 用户提示词 */}
          <Form.Item 
            label={
              <Space>
                <Text>用户提示词</Text>
                <Tooltip title="自定义的用户提示词，可选">
                  <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }
          >
            <TextArea
              value={settings.user_prompt}
              onChange={(e) => handleSettingChange('user_prompt', e.target.value)}
              placeholder="输入自定义提示词（可选）"
              autoSize={{ minRows: 2, maxRows: 4 }}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Divider style={{ margin: '12px 0' }} />

          {/* 仅需上下文 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <Tooltip title="只返回上下文信息，不生成回答" placement="left">
              <Text 
                style={{ 
                  flex: 1, 
                  marginLeft: '4px', 
                  cursor: 'help' 
                }}
              >
                仅需上下文
              </Text>
            </Tooltip>
            <Switch
              style={{ marginRight: '4px' }}
              checked={settings.context_only}
              onChange={(checked) => handleSettingChange('context_only', checked)}
            />
          </div>

          {/* 仅需提示 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <Tooltip title="只返回提示信息，不检索上下文" placement="left">
              <Text 
                style={{ 
                  flex: 1, 
                  marginLeft: '4px', 
                  cursor: 'help' 
                }}
              >
                仅需提示
              </Text>
            </Tooltip>
            <Switch
              style={{ marginRight: '4px' }}
              checked={settings.prompt_only}
              onChange={(checked) => handleSettingChange('prompt_only', checked)}
            />
          </div>

          {/* 流式响应 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <Tooltip title="启用后将实时显示响应内容" placement="left">
              <Text 
                style={{ 
                  flex: 1, 
                  marginLeft: '4px', 
                  cursor: 'help' 
                }}
              >
                流式响应
              </Text>
            </Tooltip>
            <Switch
              style={{ marginRight: '4px' }}
              checked={settings.stream}
              onChange={(checked) => handleSettingChange('stream', checked)}
            />
          </div>
        </Form>
      </Card>
    </div>
  )
}

export default QuerySettings 