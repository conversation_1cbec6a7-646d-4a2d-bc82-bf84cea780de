.query-settings {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
  height: 100%;
  overflow-y: auto;
  
  .settings-card {
    height: 100%;
    border-radius: 8px;
    
    .ant-card-head {
      padding: 12px 16px;
      min-height: auto;
      
      .ant-card-head-title {
        font-size: 14px;
        font-weight: 500;
      }
    }
    
    .ant-card-body {
      padding: 16px;
      height: calc(100% - 50px);
      overflow-y: auto;
      
      .ant-form-item {
        margin-bottom: 12px;
        
        .ant-form-item-label {
          padding: 0 0 4px;
          
          label {
            font-size: 13px;
            font-weight: 500;
          }
        }
        
        .ant-form-item-control {
          .ant-input-number,
          .ant-select {
            font-size: 13px;
          }
        }
      }
      
      .ant-divider {
        margin: 12px 0;
        border-color: #f0f0f0;
      }
      
      .ant-typography {
        &.ant-typography-title {
          font-size: 13px;
          font-weight: 600;
          color: #262626;
          margin: 8px 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .query-settings {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
  }
}

@media (max-width: 768px) {
  .query-settings {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    z-index: 1000;
    background: #fff;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    
    &.visible {
      transform: translateX(0);
    }
  }
} 