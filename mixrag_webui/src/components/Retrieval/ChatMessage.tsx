import React, { useEffect, useRef } from 'react'
import { Card, Avatar, Typography, <PERSON><PERSON>, Button } from 'antd'
import { UserOutlined, RobotOutlined, CopyOutlined, ReloadOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'
import mermaid from 'mermaid'
import './ChatMessage.less'

const { Text } = Typography

// 初始化 Mermaid
mermaid.initialize({
  startOnLoad: true,
  theme: 'default',
  securityLevel: 'loose',
  fontFamily: 'monospace'
})

const ChatMessage = ({ message }) => {
  const isUser = message.role === 'user'
  const isError = message.isError
  const mermaidRef = useRef(null)

  // 处理 Mermaid 图表渲染
  useEffect(() => {
    if (!isUser && message.content && message.mermaidRendered) {
      const renderMermaid = async () => {
        try {
          const mermaidElements = document.querySelectorAll('.mermaid-diagram')
          for (const element of mermaidElements) {
            if (element.getAttribute('data-processed') !== 'true') {
              await mermaid.init(undefined, element)
              element.setAttribute('data-processed', 'true')
            }
          }
        } catch (error) {
          console.error('Mermaid rendering error:', error)
        }
      }

      // 延迟渲染以确保DOM已更新
      setTimeout(renderMermaid, 100)
    }
  }, [message.content, message.mermaidRendered, isUser])

  // 复制内容到剪贴板
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text)
      // 这里可以添加成功提示
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 自定义 Markdown 组件
  const markdownComponents = {
    code({ node, inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || '')
      const language = match ? match[1] : ''

      if (!inline) {
        // 检查是否是 Mermaid 图表
        if (language === 'mermaid') {
          const mermaidCode = String(children).replace(/\n$/, '')
          return (
            <div className="mermaid-container">
              <div className="mermaid-header">
                <Text type="secondary">Mermaid 图表</Text>
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => copyToClipboard(mermaidCode)}
                  title="复制代码"
                />
              </div>
              <div
                className="mermaid-diagram mermaid"
                data-processed="false"
              >
                {mermaidCode}
              </div>
            </div>
          )
        }

        // 普通代码块
        return (
          <div className="code-block-container">
            <div className="code-block-header">
              <Text type="secondary">{language || 'code'}</Text>
              <Button
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(String(children))}
                title="复制代码"
              />
            </div>
            <SyntaxHighlighter
              style={tomorrow}
              language={language}
              PreTag="div"
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
        )
      }

      // 行内代码
      return (
        <code className={className} {...props}>
          {children}
        </code>
      )
    }
  }

  return (
    <div className={`chat-message ${isUser ? 'user-message' : 'assistant-message'}`}>
      <div className="message-avatar">
        <Avatar
          icon={isUser ? <UserOutlined /> : <RobotOutlined />}
          style={{
            backgroundColor: isUser ? '#1890ff' : (isError ? '#ff4d4f' : '#52c41a'),
            color: 'white'
          }}
        />
      </div>
      <div className="message-content">
        <Card
          size="small"
          className={`message-card ${isError ? 'error-message' : ''}`}
          style={{
            backgroundColor: isUser ? '#e6f7ff' : (isError ? '#fff2f0' : '#f6ffed'),
            borderColor: isUser ? '#91d5ff' : (isError ? '#ffccc7' : '#b7eb8f')
          }}
        >
          {isUser ? (
            <Text>{message.content}</Text>
          ) : isError ? (
            <Alert
              message="查询出错"
              description={message.content}
              type="error"
              showIcon
              action={
                <Button size="small" icon={<ReloadOutlined />}>
                  重试
                </Button>
              }
            />
          ) : (
            <div className={`assistant-content`}>
              <ReactMarkdown components={markdownComponents}>
                {message.content}
              </ReactMarkdown>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}

export default ChatMessage 