import React, { useState, useEffect } from 'react'
import { 
  Mo<PERSON>, 
  List, 
  Button, 
  Empty, 
  Typography, 
  Space, 
  Popconfirm,
  message,
  Input,
  DatePicker,
  Card,
  Tag,
  Tooltip
} from 'antd'
import { 
  HistoryOutlined, 
  DeleteOutlined, 
  SearchOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  ClearOutlined
} from '@ant-design/icons'
import { 
  getRetrievalHistory, 
  clearRetrievalHistory 
} from '../../api/retrievalApi'
import './HistoryManager.less'

const { Text, Title } = Typography
const { Search } = Input
const { RangePicker } = DatePicker

interface HistoryItem {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  mode?: string
  processing_time?: number
  is_error?: boolean
  mermaid_rendered?: boolean
}

interface HistoryManagerProps {
  visible: boolean
  onClose: () => void
  onLoadHistory: (history: HistoryItem[]) => void
}

const HistoryManager: React.FC<HistoryManagerProps> = ({
  visible,
  onClose,
  onLoadHistory
}) => {
  const [loading, setLoading] = useState(false)
  const [historyData, setHistoryData] = useState<HistoryItem[]>([])
  const [filteredData, setFilteredData] = useState<HistoryItem[]>([])
  const [searchText, setSearchText] = useState('')
  const [dateRange, setDateRange] = useState<[any, any] | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize] = useState(20)
  const [total, setTotal] = useState(0)

  // 加载历史记录
  const loadHistory = async () => {
    setLoading(true)
    try {
      const response = await getRetrievalHistory()
      if (response && Array.isArray(response)) {
        setHistoryData(response)
        setFilteredData(response)
        setTotal(response.length)
      } else {
        setHistoryData([])
        setFilteredData([])
        setTotal(0)
      }
    } catch (error) {
      console.error('加载历史记录失败:', error)
      message.error('加载历史记录失败')
      setHistoryData([])
      setFilteredData([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }

  // 清除所有历史记录
  const handleClearAll = async () => {
    setLoading(true)
    try {
      await clearRetrievalHistory()
      setHistoryData([])
      setFilteredData([])
      setTotal(0)
      message.success('历史记录已清除')
    } catch (error) {
      console.error('清除历史记录失败:', error)
      message.error('清除历史记录失败')
    } finally {
      setLoading(false)
    }
  }

  // 搜索和过滤
  const handleFilter = () => {
    let filtered = [...historyData]

    // 文本搜索
    if (searchText) {
      filtered = filtered.filter(item =>
        item.content.toLowerCase().includes(searchText.toLowerCase())
      )
    }

    // 日期范围过滤
    if (dateRange && dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0].startOf('day')
      const endDate = dateRange[1].endOf('day')
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.timestamp)
        return itemDate >= startDate.toDate() && itemDate <= endDate.toDate()
      })
    }

    setFilteredData(filtered)
    setTotal(filtered.length)
    setPage(1)
  }

  // 加载选中的历史记录
  const handleLoadHistory = (items: HistoryItem[]) => {
    onLoadHistory(items)
    onClose()
    message.success('历史记录已加载')
  }

  // 格式化时间
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  // 获取模式标签颜色
  const getModeColor = (mode?: string) => {
    switch (mode) {
      case 'naive': return 'blue'
      case 'global': return 'green'
      case 'mix': return 'purple'
      default: return 'default'
    }
  }

  useEffect(() => {
    if (visible) {
      loadHistory()
    }
  }, [visible])

  useEffect(() => {
    handleFilter()
  }, [searchText, dateRange, historyData])

  // 按会话分组历史记录
  const groupedHistory = filteredData.reduce((groups, item) => {
    // 简单的会话分组逻辑：连续的用户-助手对话为一个会话
    const lastGroup = groups[groups.length - 1]
    if (!lastGroup || lastGroup.length >= 10) {
      groups.push([item])
    } else {
      lastGroup.push(item)
    }
    return groups
  }, [] as HistoryItem[][])

  return (
    <Modal
      title={
        <Space>
          <HistoryOutlined />
          <span>检索历史管理</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Popconfirm
          key="clear"
          title="确定要清除所有历史记录吗？"
          onConfirm={handleClearAll}
          okText="确定"
          cancelText="取消"
        >
          <Button 
            icon={<ClearOutlined />} 
            danger
            loading={loading}
          >
            清除全部
          </Button>
        </Popconfirm>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      className="history-manager-modal"
    >
      <div className="history-manager">
        {/* 搜索和过滤区域 */}
        <Card size="small" className="filter-card">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Search
              placeholder="搜索历史记录内容..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={handleFilter}
              enterButton={<SearchOutlined />}
              allowClear
            />
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              placeholder={['开始日期', '结束日期']}
              style={{ width: '100%' }}
            />
          </Space>
        </Card>

        {/* 历史记录列表 */}
        <div className="history-list">
          {loading ? (
            <div className="loading-container">
              <Text>加载中...</Text>
            </div>
          ) : filteredData.length === 0 ? (
            <Empty
              image={<MessageOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
              description="暂无历史记录"
            />
          ) : (
            <List
              dataSource={groupedHistory}
              pagination={{
                current: page,
                pageSize: pageSize,
                total: groupedHistory.length,
                onChange: setPage,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个会话`
              }}
              renderItem={(session, index) => (
                <List.Item
                  key={index}
                  className="session-item"
                  actions={[
                    <Button
                      key="load"
                      type="primary"
                      size="small"
                      onClick={() => handleLoadHistory(session)}
                    >
                      加载会话
                    </Button>
                  ]}
                >
                  <div className="session-content">
                    <div className="session-header">
                      <Space>
                        <ClockCircleOutlined />
                        <Text type="secondary">
                          {formatTime(session[0]?.timestamp)}
                        </Text>
                        {session[0]?.mode && (
                          <Tag color={getModeColor(session[0].mode)}>
                            {session[0].mode}
                          </Tag>
                        )}
                        <Text type="secondary">
                          {session.length} 条消息
                        </Text>
                      </Space>
                    </div>
                    <div className="session-preview">
                      {session.slice(0, 2).map((item, idx) => (
                        <div key={idx} className="message-preview">
                          <Text 
                            type={item.role === 'user' ? 'primary' : 'secondary'}
                            className="role-label"
                          >
                            {item.role === 'user' ? '用户' : '助手'}:
                          </Text>
                          <Text 
                            ellipsis={{ tooltip: item.content }}
                            className={`message-content ${item.is_error ? 'error' : ''}`}
                          >
                            {item.content}
                          </Text>
                        </div>
                      ))}
                      {session.length > 2 && (
                        <Text type="secondary" className="more-messages">
                          还有 {session.length - 2} 条消息...
                        </Text>
                      )}
                    </div>
                  </div>
                </List.Item>
              )}
            />
          )}
        </div>
      </div>
    </Modal>
  )
}

export default HistoryManager
