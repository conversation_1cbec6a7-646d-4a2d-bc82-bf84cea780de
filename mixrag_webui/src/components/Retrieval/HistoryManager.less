.history-manager-modal {
  .ant-modal-body {
    padding: 16px;
  }
}

.history-manager {
  .filter-card {
    margin-bottom: 16px;
    
    .ant-card-body {
      padding: 12px;
    }
  }
  
  .history-list {
    max-height: 500px;
    overflow-y: auto;
    
    .loading-container {
      text-align: center;
      padding: 40px;
    }
    
    .session-item {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      margin-bottom: 12px;
      padding: 12px;
      background-color: #fafafa;
      
      &:hover {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
      }
      
      .ant-list-item-action {
        margin-left: 16px;
      }
    }
    
    .session-content {
      flex: 1;
      
      .session-header {
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e8e8e8;
      }
      
      .session-preview {
        .message-preview {
          display: flex;
          margin-bottom: 4px;
          
          .role-label {
            min-width: 40px;
            margin-right: 8px;
            font-weight: 500;
          }
          
          .message-content {
            flex: 1;
            
            &.error {
              color: #ff4d4f;
            }
          }
        }
        
        .more-messages {
          font-style: italic;
          margin-top: 4px;
        }
      }
    }
  }
  
  .ant-pagination {
    margin-top: 16px;
    text-align: center;
  }
}
