.chat-message {
  display: flex;
  margin-bottom: 16px;
  
  &.user-message {
    justify-content: flex-end;
    
    .message-avatar {
      order: 2;
      margin-left: 8px;
    }
    
    .message-content {
      order: 1;
      max-width: 70%;
    }
  }
  
  &.assistant-message {
    justify-content: flex-start;
    
    .message-avatar {
      order: 1;
      margin-right: 8px;
    }
    
    .message-content {
      order: 2;
      max-width: 80%;
    }
  }
  
  .message-avatar {
    display: flex;
    align-items: flex-start;
    padding-top: 4px;
  }
  
  .message-content {
    flex: 1;
    
    .message-card {
      border-radius: 12px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      
      .ant-card-body {
        padding: 8px 12px;
      }
      
      &.error-message {
        border-color: #ff4d4f;
        
        .assistant-content.error {
          color: #ff4d4f;
        }
      }
    }
  }
  
  .assistant-content {
    font-size: 14px;
    line-height: 1.6;
    
    pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      overflow-x: auto;
      margin: 8px 0;
    }
    
    code {
      background-color: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 85%;
    }
    
    blockquote {
      border-left: 4px solid #dfe2e5;
      padding-left: 16px;
      margin-left: 0;
      color: #6a737d;
    }
    
    ul, ol {
      padding-left: 20px;
    }
    
    h1, h2, h3, h4, h5, h6 {
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 600;
    }
    
    p {
      margin-bottom: 8px;
    }

    // 代码块容器样式
    .code-block-container {
      margin: 12px 0;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      overflow: hidden;

      .code-block-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background-color: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        font-size: 12px;
      }

      pre {
        margin: 0;
        border-radius: 0;
        border: none;
      }
    }

    // Mermaid 图表样式
    .mermaid-container {
      margin: 16px 0;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      overflow: hidden;

      .mermaid-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background-color: #f0f9ff;
        border-bottom: 1px solid #e8e8e8;
        font-size: 12px;
      }

      .mermaid-diagram {
        padding: 16px;
        text-align: center;
        background-color: white;

        svg {
          max-width: 100%;
          height: auto;
        }
      }
    }

    // 表格样式
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 12px 0;

      th, td {
        border: 1px solid #e8e8e8;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background-color: #fafafa;
        font-weight: 600;
      }

      tr:nth-child(even) {
        background-color: #fafafa;
      }
    }
  }
}