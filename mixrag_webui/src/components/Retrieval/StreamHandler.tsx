import React, { useRef, useCallback } from 'react'
import { message } from 'antd'

interface StreamChunk {
  content?: string
  delta?: string
  error?: string
  finished?: boolean
}

interface StreamHandlerProps {
  onChunk: (chunk: string) => void
  onError: (error: string) => void
  onFinished: () => void
}

export const useStreamHandler = ({ onChunk, onError, onFinished }: StreamHandlerProps) => {
  const abortControllerRef = useRef<AbortController | null>(null)
  const isStreamingRef = useRef(false)

  const handleStream = useCallback(async (url: string, requestBody: any) => {
    // 如果已经在流式处理中，先停止
    if (isStreamingRef.current && abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 创建新的 AbortController
    abortControllerRef.current = new AbortController()
    isStreamingRef.current = true

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `请求失败: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()

      try {
        while (true) {
          const { done, value } = await reader.read()
          
          if (done) {
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data: StreamChunk = JSON.parse(line.slice(6))
                
                if (data.error) {
                  onError(data.error)
                } else if (data.content) {
                  onChunk(data.content)
                } else if (data.delta) {
                  onChunk(data.delta)
                }

                if (data.finished) {
                  onFinished()
                  return
                }
              } catch (parseError) {
                console.warn('Failed to parse SSE data:', line)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      onFinished()

    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Stream request was aborted')
      } else {
        console.error('Stream request failed:', error)
        onError(error.message || '流式请求失败')
      }
    } finally {
      isStreamingRef.current = false
      abortControllerRef.current = null
    }
  }, [onChunk, onError, onFinished])

  const stopStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      isStreamingRef.current = false
      abortControllerRef.current = null
    }
  }, [])

  const isStreaming = useCallback(() => {
    return isStreamingRef.current
  }, [])

  return {
    handleStream,
    stopStream,
    isStreaming
  }
}

// 流式响应处理工具类
export class StreamResponseHandler {
  private abortController: AbortController | null = null
  private isStreaming = false

  constructor(
    private onChunk: (chunk: string) => void,
    private onError: (error: string) => void,
    private onFinished: () => void
  ) {}

  async start(url: string, requestBody: any): Promise<void> {
    // 如果已经在流式处理中，先停止
    if (this.isStreaming) {
      this.stop()
    }

    // 创建新的 AbortController
    this.abortController = new AbortController()
    this.isStreaming = true

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: this.abortController.signal
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `请求失败: ${response.status}`)
      }

      await this.processStream(response)

    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Stream request was aborted')
      } else {
        console.error('Stream request failed:', error)
        this.onError(error.message || '流式请求失败')
      }
    } finally {
      this.isStreaming = false
      this.abortController = null
    }
  }

  private async processStream(response: Response): Promise<void> {
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法获取响应流')
    }

    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: StreamChunk = JSON.parse(line.slice(6))
              
              if (data.error) {
                this.onError(data.error)
              } else if (data.content) {
                this.onChunk(data.content)
              } else if (data.delta) {
                this.onChunk(data.delta)
              }

              if (data.finished) {
                this.onFinished()
                return
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', line)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    this.onFinished()
  }

  stop(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.isStreaming = false
      this.abortController = null
    }
  }

  getIsStreaming(): boolean {
    return this.isStreaming
  }
}

// 流式响应处理 Hook
export const useStreamResponse = () => {
  const handlerRef = useRef<StreamResponseHandler | null>(null)

  const startStream = useCallback((
    url: string,
    requestBody: any,
    onChunk: (chunk: string) => void,
    onError: (error: string) => void,
    onFinished: () => void
  ) => {
    // 停止之前的流
    if (handlerRef.current) {
      handlerRef.current.stop()
    }

    // 创建新的处理器
    handlerRef.current = new StreamResponseHandler(onChunk, onError, onFinished)
    
    // 开始流式处理
    handlerRef.current.start(url, requestBody)
  }, [])

  const stopStream = useCallback(() => {
    if (handlerRef.current) {
      handlerRef.current.stop()
      handlerRef.current = null
    }
  }, [])

  const isStreaming = useCallback(() => {
    return handlerRef.current?.getIsStreaming() || false
  }, [])

  return {
    startStream,
    stopStream,
    isStreaming
  }
}

export default StreamResponseHandler
