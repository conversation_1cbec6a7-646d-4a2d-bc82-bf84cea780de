import React, { useEffect, useState, useCallback, useRef } from 'react'
import { 
  Modal, 
  Timeline, 
  Spin, 
  Alert, 
  Typography, 
  Tag,
  Space,
  Button,
  Empty
} from 'antd'
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons'

const { Text, Title } = Typography

const PipelineLogsModal = ({ 
  visible, 
  onCancel, 
  pipelineId,
  onLoadLogs,
  loading 
}) => {
  const [logs, setLogs] = useState([])
  const lastLoadedPipelineId = useRef(null)
  const onLoadLogsRef = useRef(onLoadLogs)

  // 更新 onLoadLogs 引用
  useEffect(() => {
    onLoadLogsRef.current = onLoadLogs
  }, [onLoadLogs])

  const loadLogs = useCallback(async () => {
    if (!pipelineId || !onLoadLogsRef.current) return

    try {
      const logData = await onLoadLogsRef.current(pipelineId)
      console.log('logData--->',logData)

      setLogs(logData || [])
      lastLoadedPipelineId.current = pipelineId
    } catch (error) {
      console.error('加载日志失败:', error)
      setLogs([])
    }
  }, [pipelineId])

  // 当模态框打开且有pipelineId时，加载日志
  useEffect(() => {
    if (visible && pipelineId && onLoadLogsRef.current && lastLoadedPipelineId.current !== pipelineId) {
      loadLogs()
    }
  }, [visible, pipelineId, loadLogs])

  // 当模态框关闭时，重置状态
  useEffect(() => {
    if (!visible) {
      setLogs([])
      lastLoadedPipelineId.current = null
    }
  }, [visible])

  // 获取状态图标
  const getStatusIcon = (status, level) => {
    if (level === 'ERROR') {
      return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    }

    const iconMap = {
      'pending': <ClockCircleOutlined style={{ color: '#faad14' }} />,
      'running': <PlayCircleOutlined style={{ color: '#1890ff' }} />,
      'completed': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'failed': <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      'cancelled': <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      'created': <CheckCircleOutlined style={{ color: '#52c41a' }} />
    }

    return iconMap[status] || <ClockCircleOutlined />
  }

  // 获取状态颜色
  const getStatusColor = (status, level) => {
    if (level === 'ERROR') {
      return 'error'
    }

    const colorMap = {
      'pending': 'warning',
      'running': 'processing',
      'completed': 'success',
      'failed': 'error',
      'cancelled': 'default',
      'created': 'success'
    }

    return colorMap[status] || 'default'
  }

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return ''
    try {
      return new Date(timestamp).toLocaleString('zh-CN')
    } catch {
      return timestamp
    }
  }

  // 获取状态显示文本
  const getStatusText = (status) => {
    const statusMap = {
      'pending': '等待中',
      'running': '运行中',
      'completed': '已完成',
      'failed': '失败',
      'cancelled': '已取消',
      'created': '已创建'
    }
    return statusMap[status] || status
  }

  // 渲染时间线项目
  const renderTimelineItems = () => {
    if (!logs || logs.length === 0) {
      return []
    }

    return logs.map((log, index) => ({
      key: index,
      dot: getStatusIcon(log.status, log.level),
      children: (
        <div style={{ paddingBottom: '16px' }}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            {/* 时间和状态 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {formatTime(log.timestamp)}
              </Text>
              <Space>
                <Tag color={getStatusColor(log.status, log.level)}>
                  {getStatusText(log.status)}
                </Tag>
                {log.step_order >= 0 && (
                  <Text code style={{ fontSize: '12px' }}>
                    步骤 {log.step_order + 1}
                  </Text>
                )}
                {log.task_type === 'pipeline' && (
                  <Text code style={{ fontSize: '12px', color: '#722ed1' }}>
                    流水线
                  </Text>
                )}
              </Space>
            </div>

            {/* 任务信息 */}
            <div>
              <Text strong>{log.task_type === 'pipeline' ? '流水线' : log.task_type}</Text>
              <Text type="secondary" style={{ marginLeft: '8px', fontSize: '12px' }}>
                ({log.id})
              </Text>
            </div>

            {/* 消息内容 */}
            <div>
              <Text className={log.level === 'ERROR' ? 'error-message' : ''}>
                {log.message}
              </Text>
            </div>

            {/* 错误信息 */}
            {log.level === 'ERROR' && log.details?.error_message && (
              <div style={{
                marginTop: '8px',
                padding: '8px',
                background: '#fff2f0',
                border: '1px solid #ffccc7',
                borderRadius: '4px'
              }}>
                <Text type="danger" style={{ fontSize: '12px' }}>
                  错误详情: {log.details.error_message}
                </Text>
              </div>
            )}
          </Space>
        </div>
      )
    }))
  }

  return (
    <Modal
      title={
        <Space>
          <span>流水线执行日志</span>
          <Button 
            type="text" 
            icon={<ReloadOutlined />} 
            onClick={loadLogs}
            loading={loading}
            size="small"
          />
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>
      ]}
      width={800}
      styles={{ body: { maxHeight: '60vh', overflow: 'auto' } }}
    >
      <Spin spinning={loading}>
        {pipelineId && (
          <div style={{ marginBottom: '16px' }}>
            <Text type="secondary">流水线ID: </Text>
            <Text code>{pipelineId}</Text>
          </div>
        )}

        {logs && logs.length > 0 ? (
          <>
            <div style={{ marginBottom: '16px' }}>
              <Text type="secondary">共 {logs.length} 条日志记录</Text>
            </div>
            
            <Timeline
              mode="left"
              items={renderTimelineItems()}
            />
          </>
        ) : !loading && (
          <Empty 
            description="暂无日志记录"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Spin>

      <style jsx>{`
        .error-message {
          color: #ff4d4f;
        }
      `}</style>
    </Modal>
  )
}

export default PipelineLogsModal 