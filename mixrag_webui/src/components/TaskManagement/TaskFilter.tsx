import React from 'react'
import { Select, Space } from 'antd'
import { TASK_STATUS, TASK_TYPES } from '../../constants/taskConstants'

const TaskFilter = ({ 
  statusFilter, 
  typeFilter, 
  onStatusChange, 
  onTypeChange 
}) => {
  return (
    <Space>
      <Select
        placeholder="筛选状态"
        allowClear
        style={{ width: 120 }}
        value={statusFilter}
        onChange={onStatusChange}
      >
        {Object.entries(TASK_STATUS).map(([key, value]) => (
          <Select.Option key={key} value={key}>
            {value.text}
          </Select.Option>
        ))}
      </Select>
      <Select
        placeholder="筛选类型"
        allowClear
        style={{ width: 150 }}
        value={typeFilter}
        onChange={onTypeChange}
      >
        {Object.entries(TASK_TYPES).map(([key, value]) => (
          <Select.Option key={key} value={key}>
            {value}
          </Select.Option>
        ))}
      </Select>
    </Space>
  )
}

export default TaskFilter 