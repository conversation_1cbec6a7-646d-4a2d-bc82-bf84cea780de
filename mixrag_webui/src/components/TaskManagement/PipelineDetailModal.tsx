import React, { useEffect, useState } from 'react'
import { 
  Modal, 
  Descriptions, 
  Steps, 
  Tag, 
  Spin, 
  Alert, 
  Row, 
  Col, 
  Card,
  Typography,
  Divider,
  Space,
  Button,
  Collapse
} from 'antd'
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { 
  PIPELINE_STATUS_MAP, 
  TASK_STATUS_MAP 
} from '../../api/pipelineApi'

const { Step } = Steps
const { Text, Title } = Typography
const { Panel } = Collapse

const PipelineDetailModal = ({ 
  visible, 
  onCancel, 
  pipeline, 
  tasks,
  loading,
  tasksLoading,
  onRefresh
}) => {
  const [currentStep, setCurrentStep] = useState(0)

  // 任务状态图标映射
  const getTaskStatusIcon = (status) => {
    const iconMap = {
      'pending': <ClockCircleOutlined />,
      'running': <PlayCircleOutlined />,
      'completed': <CheckCircleOutlined />,
      'failed': <CloseCircleOutlined />,
      'cancelled': <ExclamationCircleOutlined />
    }
    return iconMap[status] || <ClockCircleOutlined />
  }

  // 任务状态颜色映射
  const getTaskStatusColor = (status) => {
    const colorMap = {
      'pending': 'default',
      'running': 'processing',
      'completed': 'success',
      'failed': 'error',
      'cancelled': 'warning'
    }
    return colorMap[status] || 'default'
  }

  // 获取Step的状态
  const getStepStatus = (taskStatus) => {
    const statusMap = {
      'pending': 'wait',
      'running': 'process',
      'completed': 'finish',
      'failed': 'error',
      'cancelled': 'error'
    }
    return statusMap[taskStatus] || 'wait'
  }

  // 渲染任务详情
  const renderTaskDetail = (task) => {
    return (
      <Card size="small" style={{ marginTop: 8 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Text strong>任务ID:</Text>
            <br />
            <Text code style={{ fontSize: '12px' }}>
              {task.id}
            </Text>
          </Col>
          <Col span={8}>
            <Text strong>任务类型:</Text>
            <br />
            <Tag>{task.task_type}</Tag>
          </Col>
          <Col span={8}>
            <Text strong>重试次数:</Text>
            <br />
            <Text>{task.retry_count}/{task.max_retries}</Text>
          </Col>
        </Row>
        
        <Row gutter={16} style={{ marginTop: 8 }}>
          <Col span={8}>
            <Text strong>开始时间:</Text>
            <br />
            <Text>
              {task.started_at ? new Date(task.started_at).toLocaleString('zh-CN') : '-'}
            </Text>
          </Col>
          <Col span={8}>
            <Text strong>完成时间:</Text>
            <br />
            <Text>
              {task.completed_at ? new Date(task.completed_at).toLocaleString('zh-CN') : '-'}
            </Text>
          </Col>
          <Col span={8}>
            <Text strong>执行顺序:</Text>
            <br />
            <Text>{task.step_order + 1}</Text>
          </Col>
        </Row>

        {task.error_message && (
          <Alert 
            message="错误信息" 
            description={task.error_message}
            type="error" 
            showIcon 
            style={{ marginTop: 8 }}
          />
        )}

        {task.input_data && (
          <Collapse size="small" style={{ marginTop: 8 }}>
            <Panel header="输入数据" key="input">
              <pre style={{ 
                maxHeight: '200px', 
                overflow: 'auto',
                background: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                {JSON.stringify(task.input_data, null, 2)}
              </pre>
            </Panel>
          </Collapse>
        )}

        {task.output_data && (
          <Collapse size="small" style={{ marginTop: 8 }}>
            <Panel header="输出数据" key="output">
              <pre style={{ 
                maxHeight: '200px', 
                overflow: 'auto',
                background: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                {JSON.stringify(task.output_data, null, 2)}
              </pre>
            </Panel>
          </Collapse>
        )}
      </Card>
    )
  }

  // 准备Steps数据
  const prepareStepsData = () => {
    if (!tasks || tasks.length === 0) return []

    // 按step_order排序
    const sortedTasks = [...tasks].sort((a, b) => a.step_order - b.step_order)

    return sortedTasks.map((task) => ({
      title: `步骤 ${task.step_order + 1}`,
      description: (
        <div>
          <div>
            <Tag color={getTaskStatusColor(task.status)}>
              {TASK_STATUS_MAP[task.status] || task.status}
            </Tag>
            <Text>{task.task_type}</Text>
          </div>
          {renderTaskDetail(task)}
        </div>
      ),
      status: getStepStatus(task.status),
      icon: getTaskStatusIcon(task.status)
    }))
  }

  // 计算当前步骤
  useEffect(() => {
    if (tasks && tasks.length > 0) {
      const runningTaskIndex = tasks.findIndex(task => task.status === 'running')
      if (runningTaskIndex !== -1) {
        setCurrentStep(runningTaskIndex)
      } else {
        const completedCount = tasks.filter(task => task.status === 'completed').length
        setCurrentStep(completedCount)
      }
    }
  }, [tasks])

  const stepsData = prepareStepsData()

  return (
    <Modal
      title={
        <Space>
          <span>流水线详情</span>
          <Button 
            type="text" 
            icon={<ReloadOutlined />} 
            onClick={onRefresh}
            loading={loading || tasksLoading}
          />
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>
      ]}
      width={1000}
      styles={{ body: { maxHeight: '70vh', overflow: 'auto' } }}
    >
      <Spin spinning={loading}>
        {pipeline && (
          <>
            {/* 基本信息 */}
            <Descriptions column={2} bordered size="small">
              <Descriptions.Item label="流水线ID">
                <Text code>{pipeline.pipeline_id}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getTaskStatusColor(pipeline.status)}>
                  {PIPELINE_STATUS_MAP[pipeline.status] || pipeline.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="总任务数">
                {pipeline.total_tasks || 0}
              </Descriptions.Item>
              <Descriptions.Item label="创建者">
                {pipeline.created_by || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {pipeline.created_at ? new Date(pipeline.created_at).toLocaleString('zh-CN') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="完成时间">
                {pipeline.completed_at ? new Date(pipeline.completed_at).toLocaleString('zh-CN') : '-'}
              </Descriptions.Item>
            </Descriptions>

            {pipeline.error_message && (
              <Alert 
                message="错误信息" 
                description={pipeline.error_message}
                type="error" 
                showIcon 
                style={{ margin: '16px 0' }}
              />
            )}

            <Divider orientation="left">
              <Title level={4}>任务流程</Title>
            </Divider>

            {/* 任务流程可视化 */}
            <Spin spinning={tasksLoading}>
              {stepsData.length > 0 ? (
                <Steps
                  direction="vertical"
                  current={currentStep}
                  items={stepsData}
                  style={{ marginTop: 16 }}
                />
              ) : (
                <Alert 
                  message="暂无任务数据" 
                  type="info" 
                  showIcon 
                />
              )}
            </Spin>
          </>
        )}
      </Spin>
    </Modal>
  )
}

export default PipelineDetailModal 