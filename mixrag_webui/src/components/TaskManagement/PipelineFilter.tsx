import React from 'react'
import { Select, Input, Space } from 'antd'
import { PIPELINE_STATUS, PIPELINE_STATUS_MAP } from '../../api/pipelineApi'

const { Option } = Select

const PipelineFilter = ({
  statusFilter,
  createdByFilter,
  onStatusChange,
  onCreatedByChange
}) => {
  return (
    <Space>
      <Select
        placeholder="筛选状态"
        allowClear
        style={{ width: 120 }}
        value={statusFilter || undefined}
        onChange={onStatusChange}
      >
        {Object.entries(PIPELINE_STATUS_MAP).map(([status, label]) => (
          <Option key={status} value={status}>
            {label}
          </Option>
        ))}
      </Select>
      
      <Input
        placeholder="创建者"
        allowClear
        style={{ width: 120 }}
        value={createdByFilter}
        onChange={(e) => onCreatedByChange(e.target.value)}
      />
    </Space>
  )
}

export default PipelineFilter 