import React from 'react'
import { Modal, Descriptions, Badge } from 'antd'
import { TASK_TYPES, TASK_STATUS } from '../../constants/taskConstants'

const TaskDetailModal = ({ 
  visible, 
  onCancel, 
  task 
}) => {
  return (
    <Modal
      title="任务详情"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      {task && (
        <Descriptions column={2} bordered>
          <Descriptions.Item label="任务ID" span={2}>
            <span style={{ fontFamily: 'monospace' }}>{task.id}</span>
          </Descriptions.Item>
          <Descriptions.Item label="任务类型">
            {TASK_TYPES[task.task_type] || task.task_type}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Badge 
              status={TASK_STATUS[task.status]?.color || 'default'} 
              text={TASK_STATUS[task.status]?.text || task.status}
            />
          </Descriptions.Item>

          <Descriptions.Item label="重试次数">
            {task.retry_count}/{task.max_retries}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {task.created_at ? new Date(task.created_at).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="开始时间">
            {task.started_at ? new Date(task.started_at).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="完成时间">
            {task.completed_at ? new Date(task.completed_at).toLocaleString() : '-'}
          </Descriptions.Item>

          <Descriptions.Item label="输入数据" span={2}>
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '8px', 
              borderRadius: '4px',
              fontSize: '12px',
              maxHeight: '200px',
              overflow: 'auto'
            }}>
              {JSON.stringify(task.input_data, null, 2)}
            </pre>
          </Descriptions.Item>
          {task.output_data && (
            <Descriptions.Item label="输出数据" span={2}>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '8px', 
                borderRadius: '4px',
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                {JSON.stringify(task.output_data, null, 2)}
              </pre>
            </Descriptions.Item>
          )}
          {task.error_message && (
            <Descriptions.Item label="错误信息" span={2}>
              <div style={{ color: 'red' }}>
                {task.error_message}
              </div>
            </Descriptions.Item>
          )}
        </Descriptions>
      )}
    </Modal>
  )
}

export default TaskDetailModal 