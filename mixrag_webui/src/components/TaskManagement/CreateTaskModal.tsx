import React from 'react'
import { Modal, Form, Input, Select, InputNumber, Button, Space, message } from 'antd'
import { TASK_TYPES } from '../../constants/taskConstants'

const { TextArea } = Input

const CreateTaskModal = ({ 
  visible, 
  onCancel, 
  onSubmit,
  loading 
}) => {
  const [form] = Form.useForm()

  const handleSubmit = async (values) => {
    try {
      // 验证JSON格式
      let inputData
      try {
        inputData = JSON.parse(values.input_data)
       
      } catch {
        message.error('输入数据必须是有效的JSON格式')
        return
      }

      const taskData = {
        task_type: values.task_type,
        input_data: inputData,
        max_retries: values.max_retries
      }

      await onSubmit(taskData)
      form.resetFields()
     
    } catch {
      // 错误处理由父组件处理
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title="创建任务"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="task_type"
          label="任务类型"
          rules={[{ required: true, message: '请选择任务类型' }]}
        >
          <Select placeholder="选择任务类型">
            {Object.entries(TASK_TYPES).map(([key, value]) => (
              <Select.Option key={key} value={key}>
                {value}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="input_data"
          label="输入数据 (JSON格式)"
          rules={[{ required: true, message: '请输入任务数据' }]}
        >
          <TextArea
            rows={6}
            placeholder='例如: {"document_id": "doc123", "content": "需要处理的文档内容"}'
          />
        </Form.Item>

        <Form.Item
          name="max_retries"
          label="最大重试次数"
          initialValue={3}
        >
          <InputNumber min={0} max={10} />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button 
              type="primary" 
              htmlType="submit"
              loading={loading}
            >
              创建任务
            </Button>
            <Button onClick={handleCancel}>
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CreateTaskModal 