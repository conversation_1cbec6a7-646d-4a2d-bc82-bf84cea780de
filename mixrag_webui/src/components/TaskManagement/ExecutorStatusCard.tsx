import React from 'react'
import { Card, Row, Col, Statistic, Badge, Progress, Descriptions, Button, Space } from 'antd'
import { 
  CheckCircleOutlined, 
  PlayCircleOutlined, 
  DatabaseOutlined,
  ReloadOutlined
} from '@ant-design/icons'

const ExecutorStatusCard = ({
  executorStatus = {},
  statistics = {},
  loading = false,
  onRefresh,
  refreshLoading = false
}) => {

  const {
    service_running = false,
    executor_status = {},
    database_connected = false,
    scheduler_running = false,
    configuration = {}
  } = executorStatus

  const currentStatus = executor_status.current_status || {}
  const {
    running_pipelines = 0,
    processed_pipelines = 0
  } = currentStatus

  // 统计数据
  const {
    total_pipelines = 0,
    completed_pipelines = 0,
    failed_pipelines = 0,
    pending_pipelines = 0
  } = statistics

  // 成功率计算
  const successRate = total_pipelines > 0 ?
    Math.round((completed_pipelines / total_pipelines) * 100) : 0

  return (
    <Row gutter={16}>
        {/* 执行器状态 */}
        <Col span={12}>
        <Card 
          title={
            <Space>
              <span>执行器状态</span>
              {onRefresh && (
                <Button 
                  type="text" 
                  icon={<ReloadOutlined />} 
                  onClick={onRefresh}
                  loading={refreshLoading}
                  size="small"
                />
              )}
            </Space>
          }
          size="small"
          loading={loading}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Badge 
                status={service_running ? 'processing' : 'error'} 
                text={service_running ? '服务运行中' : '服务已停止'}
              />
            </Col>
            <Col span={12}>
              <Badge 
                status={database_connected ? 'success' : 'error'} 
                text={database_connected ? '数据库已连接' : '数据库未连接'}
              />
            </Col>
          </Row>
          
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
              <Badge 
                status={scheduler_running ? 'processing' : 'default'} 
                text={scheduler_running ? '调度器运行中' : '调度器未运行'}
              />
            </Col>
            <Col span={12}>
              <Badge 
                status={executor_status.is_running ? 'processing' : 'default'} 
                text={executor_status.is_running ? '执行器运行中' : '执行器未运行'}
              />
            </Col>
          </Row>

          <Descriptions 
            size="small" 
            column={1} 
            style={{ marginTop: 16 }}
          >
            <Descriptions.Item label="执行间隔">
              {configuration.interval || 'N/A'} 秒
            </Descriptions.Item>
            <Descriptions.Item label="线程数">
              {configuration.threads || 'N/A'}
            </Descriptions.Item>
            <Descriptions.Item label="最大并发">
              {configuration.max_concurrent || 'N/A'}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </Col>

      {/* 实时状态 */}
      <Col span={12}>
        <Card 
          title="实时状态" 
          size="small"
          loading={loading}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title="正在运行"
                value={running_pipelines}
                prefix={<PlayCircleOutlined style={{ color: '#1890ff' }} />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="已处理"
                value={processed_pipelines}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              />
            </Col>
          </Row>
        </Card>
      </Col>

      {/* 总体统计 */}
      <Col span={24} style={{ marginTop: 16 }}>
        <Card 
          title="总体统计" 
          size="small"
          loading={loading}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="总流水线"
                value={total_pipelines}
                prefix={<DatabaseOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="已完成"
                value={completed_pipelines}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="失败"
                value={failed_pipelines}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="等待中"
                value={pending_pipelines}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
          </Row>
          
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <div style={{ marginBottom: 8 }}>
                <span>成功率: {successRate}%</span>
              </div>
              <Progress 
                percent={successRate} 
                status={successRate < 80 ? 'exception' : 'success'}
                strokeColor={
                  successRate >= 90 ? '#52c41a' : 
                  successRate >= 80 ? '#faad14' : '#ff4d4f'
                }
              />
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
  )
}

export default ExecutorStatusCard 