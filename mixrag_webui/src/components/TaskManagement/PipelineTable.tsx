import React from 'react'
import { Table, Tag, Button, Space, Tooltip, Progress } from 'antd'
import { 
  EyeOutlined, 
  StopOutlined, 
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CaretRightOutlined
} from '@ant-design/icons'
import { PIPELINE_STATUS, PIPELINE_STATUS_MAP } from '../../api/pipelineApi'

const PipelineTable = ({ 
  pipelines, 
  loading, 
  onViewPipeline, 
  onCancelPipeline,
  onRestartPipeline,
  onResumePipeline,
  onViewLogs,
  cancelLoading,
  restartLoading,
  resumeLoading
}) => {
  
  // 状态标签渲染
  const renderStatus = (status) => {
    const statusConfig = {
      [PIPELINE_STATUS.PENDING]: { color: 'default', icon: <PlayCircleOutlined /> },
      [PIPELINE_STATUS.RUNNING]: { color: 'processing', icon: <PlayCircleOutlined /> },
      [PIPELINE_STATUS.COMPLETED]: { color: 'success', icon: <CheckCircleOutlined /> },
      [PIPELINE_STATUS.FAILED]: { color: 'error', icon: <CloseCircleOutlined /> },
      [PIPELINE_STATUS.CANCELLED]: { color: 'warning', icon: <ExclamationCircleOutlined /> }
    }

    const config = statusConfig[status] || { color: 'default', icon: null }
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {PIPELINE_STATUS_MAP[status] || status}
      </Tag>
    )
  }

  // 进度渲染
  const renderProgress = (record) => {
    const { status, total_tasks = 0, completed_tasks = 0 } = record
    
    if (total_tasks === 0) {
      return '-'
    }

    // 直接使用后端返回的completed_tasks
    const percent = total_tasks > 0 ? Math.round((completed_tasks / total_tasks) * 100) : 0
    
    let progressStatus = 'normal'
    if (status === PIPELINE_STATUS.COMPLETED) {
      progressStatus = 'success'
    } else if (status === PIPELINE_STATUS.FAILED) {
      progressStatus = 'exception'
    } else if (status === PIPELINE_STATUS.RUNNING) {
      progressStatus = 'active'
    }

    return (
      <div style={{ minWidth: '120px' }}>
        <Progress 
          percent={percent} 
          size="small" 
          status={progressStatus}
          format={() => `${completed_tasks}/${total_tasks}`}
        />
      </div>
    )
  }

  // 操作按钮渲染
  const renderActions = (record) => {
    const { pipeline_id, status } = record
    
    return (
      <Space size="small">
        <Tooltip title="查看详情">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => onViewPipeline(pipeline_id)}
            size="small"
          />
        </Tooltip>
        
        <Tooltip title="查看日志">
          <Button 
            type="link" 
            icon={<FileTextOutlined />} 
            onClick={() => onViewLogs && onViewLogs(pipeline_id)}
            size="small"
          />
        </Tooltip>
        
        {(status === PIPELINE_STATUS.PENDING || status === PIPELINE_STATUS.RUNNING) && (
          <Tooltip title="取消流水线">
            <Button 
              type="link" 
              danger
              icon={<StopOutlined />} 
              onClick={() => onCancelPipeline(pipeline_id)}
              loading={cancelLoading}
              size="small"
            />
          </Tooltip>
        )}
        
        {status === PIPELINE_STATUS.FAILED && (
          <Tooltip title="重启流水线">
            <Button 
              type="link" 
              icon={<ReloadOutlined />} 
              onClick={() => onRestartPipeline && onRestartPipeline(pipeline_id)}
              loading={restartLoading}
              size="small"
            />
          </Tooltip>
        )}
        
        {status === PIPELINE_STATUS.CANCELLED && (
          <>
            <Tooltip title="恢复流水线">
              <Button 
                type="link" 
                icon={<CaretRightOutlined />} 
                onClick={() => onResumePipeline && onResumePipeline(pipeline_id)}
                loading={resumeLoading}
                size="small"
              />
            </Tooltip>
            <Tooltip title="重启流水线">
              <Button 
                type="link" 
                icon={<ReloadOutlined />} 
                onClick={() => onRestartPipeline && onRestartPipeline(pipeline_id)}
                loading={restartLoading}
                size="small"
              />
            </Tooltip>
          </>
        )}
      </Space>
    )
  }

  // 表格列定义
  const columns = [
    {
      title: '流水线ID',
      dataIndex: 'pipeline_id',
      key: 'pipeline_id',
      width: 150,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>
            {text?.slice(0, 12)}...
          </code>
        </Tooltip>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: renderStatus
    },
    {
      title: '进度',
      key: 'progress',
      width: 150,
      render: (_, record) => renderProgress(record)
    },
    {
      title: '任务数',
      dataIndex: 'total_tasks',
      key: 'total_tasks',
      width: 80,
      render: (count) => count || 0
    },
    {
      title: '创建者',
      dataIndex: 'created_by',
      key: 'created_by',
      width: 120,
      render: (text) => text || '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text) => {
        if (!text) return '-'
        try {
          return new Date(text).toLocaleString('zh-CN')
        } catch {
          return text
        }
      }
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      width: 180,
      render: (text) => {
        if (!text) return '-'
        try {
          return new Date(text).toLocaleString('zh-CN')
        } catch {
          return text
        }
      }
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      width: 200,
      render: (text) => {
        if (!text) return '-'
        return (
          <Tooltip title={text}>
            <div style={{ 
              maxWidth: '180px', 
              overflow: 'hidden', 
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {text}
            </div>
          </Tooltip>
        )
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      fixed: 'right',
      render: (_, record) => renderActions(record)
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={pipelines}
      loading={loading}
      rowKey="pipeline_id"
      scroll={{ x: 1200 }}
      size="small"
      pagination={false}
    />
  )
}

export default PipelineTable 