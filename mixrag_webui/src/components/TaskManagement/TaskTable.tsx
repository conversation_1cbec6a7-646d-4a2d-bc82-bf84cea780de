import React from 'react'
import { Table, Button, Space, Badge, Popconfirm } from 'antd'
import { EyeOutlined, DeleteOutlined } from '@ant-design/icons'
import { TASK_TYPES, TASK_STATUS, CANCELABLE_STATUSES } from '../../constants/taskConstants'

const TaskTable = ({ 
  tasks, 
  loading, 
  onViewTask, 
  onCancelTask 
}) => {
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text?.slice(0, 8)}...</span>
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (type) => TASK_TYPES[type] || type
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusInfo = TASK_STATUS[status] || { text: status, color: 'default' }
        return <Badge status={statusInfo.color} text={statusInfo.text} />
      }
    },
    {
      title: '重试次数',
      dataIndex: 'retry_count',
      key: 'retry_count',
      width: 100,
      render: (count, record) => `${count}/${record.max_retries}`
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => onViewTask(record.id)}
          >
            详情
          </Button>
          {CANCELABLE_STATUSES.includes(record.status) && (
            <Popconfirm
              title="确定要取消这个任务吗？"
              onConfirm={() => onCancelTask(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />}
              >
                取消
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={tasks}
      loading={loading}
      rowKey="id"
      pagination={false}
      scroll={{ x: 'max-content' }}
    />
  )
}

export default TaskTable 