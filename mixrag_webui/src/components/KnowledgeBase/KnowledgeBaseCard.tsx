import React from 'react'
import { Card, Typography, Tooltip, Tag, Space, Button } from 'antd'
import {
  EditOutlined,
  DeleteOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  CalendarOutlined
} from '@ant-design/icons'
import { KnowledgeBase } from '../../api/knowledgeBaseApi'

const { Title, Text, Paragraph } = Typography

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase
  onEdit: (knowledgeBase: KnowledgeBase) => void
  onDelete: (id: string) => void
  loading?: boolean
}

const KnowledgeBaseCard: React.FC<KnowledgeBaseCardProps> = ({
  knowledgeBase,
  onEdit,
  onDelete,
  loading = false
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(knowledgeBase);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(knowledgeBase.id);
  };

  return (
    <Card 
      hoverable
      style={{ height: '100%', minHeight: 190, display: 'flex', flexDirection: 'column' }} // Height adjusted
      styles={{
        body: {
          padding: '16px',
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      {/* Card Header */}
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px', marginBottom: '12px' }}>
        <DatabaseOutlined style={{ fontSize: 28, color: '#1677ff', marginTop: '4px' }} />
        <div style={{ flex: 1 }}>
          <Title level={5} style={{ margin: 0 }} ellipsis={{ tooltip: knowledgeBase.kb_name }}>
            {knowledgeBase.kb_name}
          </Title>
        </div>
      </div>

      {/* Card Content */}
      <div style={{ flex: 1, marginBottom: '12px' }}>
        <Paragraph 
          ellipsis={{ rows: 2, tooltip: knowledgeBase.kb_des }} // Reduced rows for compactness
          style={{ margin: 0, color: '#666', fontSize: 14, minHeight: 44 }}
        >
          {knowledgeBase.kb_des || '暂无描述'}
        </Paragraph>
      </div>

      {/* Card Footer */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-end', marginTop: 'auto' }}>
        <Space direction="vertical" size={4} align="start">
          <Tag icon={<FileTextOutlined />} color="blue">
            {knowledgeBase.doc_count} 个文档
          </Tag>
          <Space size="small">
            <CalendarOutlined />
            <Text type="secondary" style={{ fontSize: 12 }}>{formatDate(knowledgeBase.create_time)}</Text>
          </Space>
        </Space>
        <Space size="small">
          <Tooltip title="编辑">
            <Button type="text" icon={<EditOutlined />} onClick={handleEdit} />
          </Tooltip>
          <Tooltip title="删除">
            <Button type="text" danger icon={<DeleteOutlined />} onClick={handleDelete} loading={loading} />
          </Tooltip>
        </Space>
      </div>
    </Card>
  )
}

export default KnowledgeBaseCard
