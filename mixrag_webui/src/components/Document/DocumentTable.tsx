import React from 'react'
import { Table, Button, Space, Badge, Popconfirm, Tag, Tooltip } from 'antd'
import { 
  EyeOutlined, 
  DeleteOutlined, 
  PlayCircleOutlined,
  FileTextOutlined,
  LinkOutlined,
  DownloadOutlined
} from '@ant-design/icons'

// 文档上传状态配置
const UPLOAD_STATUS = {
  pending: { text: '待上传', color: 'default' },
  uploaded: { text: '已上传', color: 'success' },
  failed: { text: '上传失败', color: 'error' }
}

// 文档处理状态配置
const PROCESS_STATUS = {
  pending: { text: '待处理', color: 'default' },
  processing: { text: '处理中', color: 'processing' },
  completed: { text: '已处理', color: 'success' },
  failed: { text: '处理失败', color: 'error' }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 截取文档ID显示
const formatDocId = (docId) => {
  if (!docId) return ''
  return docId.length > 12 ? `${docId.slice(0, 12)}...` : docId
}

const DocumentTable = ({ 
  mixrag_documents, 
  loading, 
  onViewDocument, 
  onDeleteDocument, 
  onProcessDocument,
  onDownloadDocument
}) => {
  const columns = [
    {
      title: '文档ID',
      dataIndex: 'doc_id',
      key: 'doc_id',
      width: 150,
      render: (docId) => (
        <Tooltip title={docId}>
          <span style={{ fontFamily: 'monospace', cursor: 'pointer' }}>
            {formatDocId(docId)}
          </span>
        </Tooltip>
      )
    },
    {
      title: '文件名',
      dataIndex: 'original_filename',
      key: 'original_filename',
      width: 150,
      render: (filename) => (
        <Space>
          <FileTextOutlined />
          <Tooltip title={filename}>
            <span>{filename?.length > 30 ? `${filename.slice(0, 30)}...` : filename}</span>
          </Tooltip>
        </Space>
      )
    },
    {
      title: '文件类型',
      dataIndex: 'file_extension',
      key: 'file_extension',
      width: 100,
      render: (extension) => (
        <Tag color="blue">{(extension || '').toUpperCase()}</Tag>
      )
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100,
      render: (size) => formatFileSize(size)
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '上传状态',
      dataIndex: 'upload_status',
      key: 'upload_status',
      width: 100,
      render: (status) => {
        const statusConfig = UPLOAD_STATUS[status] || { text: status, color: 'default' }
        return (
          <Badge
            status={statusConfig.color}
            text={statusConfig.text}
          />
        )
      }
    },
    {
      title: '处理状态',
      dataIndex: 'process_status',
      key: 'process_status',
      width: 100,
      render: (status) => {
        const statusConfig = PROCESS_STATUS[status] || { text: status, color: 'default' }
        return (
          <Badge
            status={statusConfig.color}
            text={statusConfig.text}
          />
        )
      }
    },
    {
      title: '上传者',
      dataIndex: 'uploaded_by',
      key: 'uploaded_by',
      width: 100,
      render: (user) => user || '未知'
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small"
              icon={<EyeOutlined />} 
              onClick={() => onViewDocument(record.doc_id)}
            />
          </Tooltip>
          
          {/* 下载按钮 */}
          {record.upload_status === 'uploaded' && (
            <Tooltip title="下载原始文档">
              <Button 
                type="text" 
                size="small"
                icon={<DownloadOutlined />}
                onClick={() => onDownloadDocument(record.doc_id, record.original_filename)}
              />
            </Tooltip>
          )}
          
          {/* 处理按钮 */}
          {record.upload_status === 'uploaded' && 
           record.process_status !== 'processing' && (
            <Tooltip title={record.process_status === 'completed' ? '重新处理' : '开始处理'}>
              <Button 
                type="text" 
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => onProcessDocument(
                  record.doc_id, 
                  record.process_status === 'completed'
                )}
              />
            </Tooltip>
          )}
          
          {/* 删除按钮 */}
          <Popconfirm
            title="确定要删除这个文档吗？"
            description="删除后无法恢复，包括文件和相关的处理数据。"
            onConfirm={() => onDeleteDocument(record.doc_id)}
            okText="确定"
            cancelText="取消"
            placement="topRight"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={mixrag_documents}
      loading={loading}
      rowKey="doc_id"
      pagination={false}
      scroll={{ x: 1100 }}
      size="small"
      bordered
    />
  )
}

export default DocumentTable 