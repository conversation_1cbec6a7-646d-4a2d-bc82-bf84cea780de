import React, { useState } from 'react'
import { 
  Modal, 
  Upload, 
  Button, 
  Form, 
  Input, 
  message,
  Space,
  Alert 
} from 'antd'
import { 
  UploadOutlined, 
  InboxOutlined,
  FileTextOutlined 
} from '@ant-design/icons'

const { Dragger } = Upload
const { TextArea } = Input

const DocumentUpload = ({ visible, onCancel, onUpload, loading }) => {
  const [form] = Form.useForm()
  const [uploadedFile, setUploadedFile] = useState(null)
  const [uploading, setUploading] = useState(false)

  // 支持的文件类型
  const ALLOWED_EXTENSIONS = ['txt', 'pdf', 'docx', 'md', 'doc']
  const ALLOWED_MIME_TYPES = [
    'text/plain',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/markdown',
    'application/msword'
  ]

  // 最大文件大小 (50MB)
  const MAX_FILE_SIZE = 50 * 1024 * 1024

  // 验证文件
  const validateFile = (file) => {
    // 检查文件扩展名
    const extension = file.name.split('.').pop().toLowerCase()
    if (!ALLOWED_EXTENSIONS.includes(extension)) {
      message.error(`不支持的文件类型。支持的类型：${ALLOWED_EXTENSIONS.join(', ')}`)
      return false
    }

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      message.error('文件大小超过50MB限制')
      return false
    }

    return true
  }

  // 处理文件上传前的验证
  const beforeUpload = (file) => {
    if (validateFile(file)) {
      setUploadedFile(file)
      // 自动填充文件名到上传者字段（如果为空）
      if (!form.getFieldValue('uploadedBy')) {
        form.setFieldsValue({
          uploadedBy: '当前用户'
        })
      }
    }
    return false // 阻止自动上传
  }

  // 处理文件移除
  const handleRemove = () => {
    setUploadedFile(null)
  }

  // 处理表单提交
  const handleSubmit = async () => {
    if (!uploadedFile) {
      message.error('请选择要上传的文件')
      return
    }

    try {
      const values = await form.validateFields()
      setUploading(true)
      
      await onUpload(uploadedFile, values.uploadedBy)
      
      // 重置表单
      form.resetFields()
      setUploadedFile(null)
    } catch (error) {
      console.error('上传失败:', error)
    } finally {
      setUploading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    setUploadedFile(null)
    onCancel()
  }

  return (
    <Modal
      title="上传文档"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          loading={uploading || loading}
          onClick={handleSubmit}
          disabled={!uploadedFile}
        >
          上传
        </Button>
      ]}
      width={600}
      destroyOnHidden
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 文件上传提示 */}
        <Alert
          message="支持的文件格式"
          description={`${ALLOWED_EXTENSIONS.map(ext => ext.toUpperCase()).join(', ')} | 最大文件大小: 50MB`}
          type="info"
          showIcon
        />

        {/* 文件上传区域 */}
        <Dragger
          name="file"
          multiple={false}
          beforeUpload={beforeUpload}
          onRemove={handleRemove}
          fileList={uploadedFile ? [uploadedFile] : []}
          accept={ALLOWED_EXTENSIONS.map(ext => `.${ext}`).join(',')}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 TXT、PDF、DOCX、MD、DOC 格式文件
          </p>
        </Dragger>

        {/* 上传信息表单 */}
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            uploadedBy: '当前用户'
          }}
        >
          <Form.Item
            label="上传者"
            name="uploadedBy"
            rules={[
              { required: true, message: '请输入上传者信息' },
              { max: 50, message: '上传者信息不能超过50个字符' }
            ]}
          >
            <Input 
              placeholder="请输入上传者姓名或标识"
              prefix={<FileTextOutlined />}
            />
          </Form.Item>

          <Form.Item
            label="备注"
            name="remarks"
            rules={[
              { max: 200, message: '备注不能超过200个字符' }
            ]}
          >
            <TextArea 
              rows={3}
              placeholder="可选：添加文档相关备注信息"
              showCount
              maxLength={200}
            />
          </Form.Item>
        </Form>

        {/* 上传的文件信息 */}
        {uploadedFile && (
          <div style={{ 
            padding: '12px', 
            background: '#f5f5f5', 
            borderRadius: '6px',
            border: '1px solid #d9d9d9'
          }}>
            <Space>
              <FileTextOutlined style={{ color: '#1890ff' }} />
              <div>
                <div style={{ fontWeight: 'bold' }}>{uploadedFile.name}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                </div>
              </div>
            </Space>
          </div>
        )}
      </Space>
    </Modal>
  )
}

export default DocumentUpload 