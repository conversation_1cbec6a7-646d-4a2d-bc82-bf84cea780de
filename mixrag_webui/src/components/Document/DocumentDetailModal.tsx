import React, { useEffect, useState } from 'react'
import {
  Modal,
  Descriptions,
  Badge,
  Space,
  Button,
  Tag,
  Alert,
  Typography,
  Progress,
  Popconfirm,
  Tabs,
  Spin,
  message
} from 'antd'
import {
  PlayCircleOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  Bar<PERSON><PERSON>Outlined,
  DatabaseOutlined,
  SettingOutlined,
  WarningOutlined,
  BlockOutlined
} from '@ant-design/icons'
import ChunkTable from './ChunkTable'
import { useDocumentManagement } from '../../hooks/useDocumentManagement'
import { fetchDocumentProcessingStats } from '../../api/documentApi'

const { Text, Title } = Typography

// 状态配置
const UPLOAD_STATUS = {
  pending: { text: '待上传', color: 'default', icon: <ClockCircleOutlined /> },
  uploaded: { text: '已上传', color: 'success', icon: <CheckCircleOutlined /> },
  failed: { text: '上传失败', color: 'error', icon: <ExclamationCircleOutlined /> }
}

const PROCESS_STATUS = {
  pending: { text: '待处理', color: 'default', icon: <ClockCircleOutlined /> },
  processing: { text: '处理中', color: 'processing', icon: <SyncOutlined spin /> },
  completed: { text: '已处理', color: 'success', icon: <CheckCircleOutlined /> },
  failed: { text: '处理失败', color: 'error', icon: <ExclamationCircleOutlined /> }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const DocumentDetailModal = ({ 
  visible, 
  onCancel, 
  document, 
  onProcessDocument 
}) => {
  const {
    chunks,
    chunksLoading,
    chunksTotal,
    chunksCurrentPage,
    chunksPageSize,
    loadDocumentChunks,
    resetChunksState,
    setChunksCurrentPage,
    setChunksPageSize
  } = useDocumentManagement()

  // 处理统计状态
  const [processingStats, setProcessingStats] = useState(null)
  const [statsLoading, setStatsLoading] = useState(false)

  // 加载文档处理统计信息
  const loadProcessingStats = async (docId) => {
    setStatsLoading(true)
    try {
      const response = await fetchDocumentProcessingStats(docId)
      // 提取响应中的data字段
      const stats = response?.success ? response.data : response
      setProcessingStats(stats)
    } catch (error) {
      message.error(error.message || '获取处理统计失败')
      setProcessingStats(null)
    } finally {
      setStatsLoading(false)
    }
  }

  // 当模态框打开且有文档时，加载chunks和统计信息
  useEffect(() => {
    if (visible && document?.doc_id) {
      loadDocumentChunks(document.doc_id, 1, 10)
      loadProcessingStats(document.doc_id)
    } else if (!visible) {
      // 模态框关闭时重置状态
      resetChunksState()
      setProcessingStats(null)
    }
  }, [visible, document?.doc_id, loadDocumentChunks, resetChunksState])

  // 处理chunk分页变化
  const handleChunkPageChange = (page, pageSize) => {
    setChunksCurrentPage(page)
    setChunksPageSize(pageSize)
    if (document?.doc_id) {
      loadDocumentChunks(document.doc_id, page, pageSize)
    }
  }

  if (!document) return null

  // 计算处理进度
  const getProcessProgress = () => {
    if (document.process_status === 'completed') return 100
    if (document.process_status === 'processing') return 50
    if (document.process_status === 'failed') return 0
    return 0
  }

  // 是否可以处理
  const canProcess = document.upload_status === 'uploaded' && 
                    document.process_status !== 'processing'

  // 处理重新处理
  const handleReprocess = () => {
    onProcessDocument(document.doc_id, true)
  }

  // 处理开始处理
  const handleProcess = () => {
    onProcessDocument(document.doc_id, false)
  }

  // 基本信息 Tab
  const BasicInfoTab = () => (
    <Space direction="vertical" style={{ width: '100%' }} size="middle">
      {/* 文档基本信息 */}
      <Descriptions bordered size="small" column={2}>
        <Descriptions.Item label="文档ID" span={2}>
          <Text code copyable>{document.doc_id}</Text>
        </Descriptions.Item>
        <Descriptions.Item label="文件名">
          {document.original_filename}
        </Descriptions.Item>
        <Descriptions.Item label="文件类型">
          <Tag color="blue">{(document.file_extension || '').toUpperCase()}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="文件大小">
          {formatFileSize(document.file_size)}
        </Descriptions.Item>
        <Descriptions.Item label="MIME类型">
          {document.mime_type || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="上传者">
          {document.uploaded_by || '未知'}
        </Descriptions.Item>
        <Descriptions.Item label="上传时间">
          {formatDateTime(document.created_at)}
        </Descriptions.Item>
      </Descriptions>

      {/* 状态信息 */}
      <Descriptions bordered size="small" column={2}>
        <Descriptions.Item label="上传状态">
          <Badge 
            status={UPLOAD_STATUS[document.upload_status]?.color || 'default'}
            text={
              <Space>
                {UPLOAD_STATUS[document.upload_status]?.icon}
                {UPLOAD_STATUS[document.upload_status]?.text || document.upload_status}
              </Space>
            }
          />
        </Descriptions.Item>
        <Descriptions.Item label="处理状态">
          <Badge 
            status={PROCESS_STATUS[document.process_status]?.color || 'default'}
            text={
              <Space>
                {PROCESS_STATUS[document.process_status]?.icon}
                {PROCESS_STATUS[document.process_status]?.text || document.process_status}
              </Space>
            }
          />
        </Descriptions.Item>
        <Descriptions.Item label="更新时间" span={2}>
          {formatDateTime(document.updated_at)}
        </Descriptions.Item>
        {document.pipeline_id && (
          <Descriptions.Item label="流水线ID" span={2}>
            <Text code copyable>{document.pipeline_id}</Text>
          </Descriptions.Item>
        )}
      </Descriptions>

      {/* 处理进度 */}
      {document.process_status === 'processing' && (
        <div>
          <Text strong>处理进度</Text>
          <Progress 
            percent={getProcessProgress()} 
            status="active"
            showInfo={false}
            style={{ marginTop: 8 }}
          />
          <Text type="secondary">文档正在处理中，请稍候...</Text>
        </div>
      )}
    </Space>
  )

  // 处理结果 Tab
  const ProcessResultTab = () => {
    // 使用API获取的统计数据，如果没有则使用文档中的数据作为后备
    const stats = {
      chunks_count: processingStats?.chunks_stats?.total_chunks || document.chunks_count || 0,
      entities_count: processingStats?.graph_stats?.entities_count || document.entities_count || 0,
      relationships_count: processingStats?.graph_stats?.relationships_count || document.relationships_count || 0
    }

    const hasResults = stats.chunks_count > 0 || stats.entities_count > 0 || stats.relationships_count > 0

    return (
      <Spin spinning={statsLoading} tip="加载统计信息...">
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Descriptions bordered size="small" column={3}>
            <Descriptions.Item label="分块数量">
              <Text strong style={{ color: '#1890ff' }}>
                {stats.chunks_count}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="实体数量">
              <Text strong style={{ color: '#52c41a' }}>
                {stats.entities_count}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="关系数量">
              <Text strong style={{ color: '#fa8c16' }}>
                {stats.relationships_count}
              </Text>
            </Descriptions.Item>
          </Descriptions>

          {/* 显示处理状态信息 */}
          {processingStats?.processing_stats && (
            <Descriptions bordered size="small" column={2}>
              <Descriptions.Item label="流水线状态">
                <Badge
                  status={processingStats.processing_stats.pipeline_status === 'completed' ? 'success' :
                          processingStats.processing_stats.pipeline_status === 'failed' ? 'error' :
                          processingStats.processing_stats.pipeline_status === 'running' ? 'processing' : 'default'}
                  text={processingStats.processing_stats.pipeline_status || '未知'}
                />
              </Descriptions.Item>
              <Descriptions.Item label="流水线ID">
                {processingStats.processing_stats.pipeline_id ? (
                  <Text code copyable={{ text: processingStats.processing_stats.pipeline_id }}>
                    {processingStats.processing_stats.pipeline_id}
                  </Text>
                ) : (
                  <Text type="secondary">无</Text>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="任务进度">
                <Text>
                  {processingStats.processing_stats.completed_tasks || 0} / {processingStats.processing_stats.total_tasks || 0}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="失败任务">
                <Text type={processingStats.processing_stats.failed_tasks > 0 ? 'danger' : 'secondary'}>
                  {processingStats.processing_stats.failed_tasks || 0}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          )}

          {/* 根据实际数据显示结果 */}
          {!hasResults && (
            <Alert
              message={
                document.process_status === 'completed' 
                  ? '处理已完成但未产生数据' 
                  : document.process_status === 'processing'
                  ? '文档正在处理中'
                  : document.process_status === 'failed'
                  ? '文档处理失败'
                  : '文档尚未开始处理'
              }
              description={
                document.process_status === 'completed' 
                  ? '文档已处理完成，但可能因为内容格式或配置问题未产生分块、实体或关系数据。'
                  : document.process_status === 'processing'
                  ? '文档正在处理中，请稍后刷新查看结果。'
                  : document.process_status === 'failed'
                  ? '文档处理过程中发生错误，请检查错误信息或重新处理。'
                  : '请先启动文档处理以生成分块、实体和关系数据。'
              }
              type={
                document.process_status === 'failed' ? 'error' :
                document.process_status === 'processing' ? 'info' : 
                'warning'
              }
              showIcon
            />
          )}

          {/* 如果有结果，显示详细说明 */}
          {hasResults && (
            <Alert
              message="处理结果概览"
              description={
                <div>
                  <p>该文档已成功处理并生成以下数据：</p>
                  <ul style={{ marginBottom: 0 }}>
                    {stats.chunks_count > 0 && (
                      <li>文档已分块为 <strong>{stats.chunks_count}</strong> 个片段，可在"文档块"标签页查看详细内容</li>
                    )}
                    {stats.entities_count > 0 && (
                      <li>从文档中提取了 <strong>{stats.entities_count}</strong> 个实体</li>
                    )}
                    {stats.relationships_count > 0 && (
                      <li>建立了 <strong>{stats.relationships_count}</strong> 个实体关系</li>
                    )}
                  </ul>
                </div>
              }
              type="success"
              showIcon
            />
          )}
        </Space>
      </Spin>
    )
  }

  // 存储信息 Tab
  const StorageInfoTab = () => (
    <Descriptions bordered size="small" column={1}>
      <Descriptions.Item label="存储桶">
        <Text code>{document.minio_bucket}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="对象键">
        <Text code copyable>{document.minio_object_key}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="内容哈希">
        <Text code copyable>{document.content_hash}</Text>
      </Descriptions.Item>
    </Descriptions>
  )

  // Chunk Tab
  const ChunkTab = () => (
    <div>
      <ChunkTable
        chunks={chunks}
        loading={chunksLoading}
        total={chunksTotal}
        currentPage={chunksCurrentPage}
        pageSize={chunksPageSize}
        onPageChange={handleChunkPageChange}
      />
    </div>
  )

  // 构建 Tab 项
  const tabItems = [
    {
      key: 'basic',
      label: (
        <Space>
          <InfoCircleOutlined />
          基本信息
        </Space>
      ),
      children: <BasicInfoTab />
    },
    {
      key: 'result',
      label: (
        <Space>
          <BarChartOutlined />
          处理结果
        </Space>
      ),
      children: <ProcessResultTab />
    },
    {
      key: 'chunks',
      label: (
        <Space>
          <BlockOutlined />
          文档块
          {chunksTotal > 0 && (
            <Badge count={chunksTotal} size="small" />
          )}
        </Space>
      ),
      children: <ChunkTab />
    },
    {
      key: 'storage',
      label: (
        <Space>
          <DatabaseOutlined />
          存储信息
        </Space>
      ),
      children: <StorageInfoTab />
    },
  ]

  // 如果有错误信息，添加错误 tab
  if (document.error_message) {
    tabItems.push({
      key: 'error',
      label: (
        <Space>
          <WarningOutlined />
          错误信息
        </Space>
      ),
      children: <ErrorInfoTab />
    })
  }

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          <span>文档详情</span>
          <Tag color="blue">{document.original_filename}</Tag>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>,
        ...(canProcess ? [
          document.process_status === 'completed' ? (
            <Popconfirm
              key="reprocess"
              title="重新处理文档"
              description="确定要重新处理这个文档吗？这将覆盖现有的处理结果。"
              onConfirm={handleReprocess}
              okText="确定"
              cancelText="取消"
            >
              <Button type="primary" icon={<SyncOutlined />}>
                重新处理
              </Button>
            </Popconfirm>
          ) : (
            <Button 
              key="process" 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={handleProcess}
            >
              开始处理
            </Button>
          )
        ] : [])
      ]}
      width={800}
      styles={{
        body: { paddingTop: 0 }
      }}
    >
      <Tabs
        items={tabItems}
        defaultActiveKey="basic"
        size="small"
        style={{ marginTop: 16 }}
      />
    </Modal>
  )
}

export default DocumentDetailModal 