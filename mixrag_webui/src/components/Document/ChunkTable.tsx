import React, { useState } from 'react'
import { 
  Table, 
  Typography, 
  Tag, 
  Pagination, 
  Space, 
  Card,
  Tooltip,
  Badge,
  Button
} from 'antd'
import { 
  FileTextOutlined,
  NumberOutlined,
  FieldTimeOutlined,
  EyeOutlined
} from '@ant-design/icons'
import ChunkDetailModal from './ChunkDetailModal'

const { Text, Paragraph } = Typography

const ChunkTable = ({ 
  chunks, 
  loading, 
  total, 
  currentPage, 
  pageSize, 
  onPageChange 
}) => {
  // 详情模态框状态
  const [isDetailVisible, setIsDetailVisible] = useState(false)
  const [selectedChunk, setSelectedChunk] = useState(null)

  // 处理查看详情
  const handleViewChunkDetail = (chunk) => {
    setSelectedChunk(chunk)
    setIsDetailVisible(true)
  }

  // 关闭详情模态框
  const handleCloseDetail = () => {
    setIsDetailVisible(false)
    setSelectedChunk(null)
  }
  // 格式化时间
  const formatDateTime = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 格式化内容大小
  const formatSize = (size) => {
    if (!size) return '0'
    if (size < 1024) return `${size} 字符`
    return `${Math.round(size / 1024 * 100) / 100}K 字符`
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'chunk_index',
      key: 'chunk_index',
      width: 80,
      sorter: (a, b) => a.chunk_index - b.chunk_index,
      render: (index) => (
        <Badge 
          count={index + 1} 
          style={{ backgroundColor: '#1890ff' }}
          showZero
        />
      )
    },
    {
      title: 'Chunk ID',
      dataIndex: 'chunk_id',
      key: 'chunk_id',
      width: 200,
      render: (chunkId) => (
        <Tooltip title={chunkId}>
          <Text code copyable={{ text: chunkId }}>
            {chunkId.substring(0, 16)}...
          </Text>
        </Tooltip>
      )
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content) => (
        <div style={{ maxWidth: 400 }}>
          <Paragraph 
            ellipsis={{ 
              rows: 3, 
              expandable: true, 
              symbol: '展开' 
            }}
            style={{ marginBottom: 0 }}
          >
            {content}
          </Paragraph>
        </div>
      )
    },
    {
      title: '大小',
      dataIndex: 'chunk_size',
      key: 'chunk_size',
      width: 120,
      sorter: (a, b) => a.chunk_size - b.chunk_size,
      render: (size) => (
        <Tag color="blue" icon={<FileTextOutlined />}>
          {formatSize(size)}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (createdAt) => (
        <Space>
          <FieldTimeOutlined style={{ color: '#8c8c8c' }} />
          <Text type="secondary">
            {formatDateTime(createdAt)}
          </Text>
        </Space>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Button 
          type="link" 
          size="small"
          icon={<EyeOutlined />} 
          onClick={() => handleViewChunkDetail(record)}
          title="查看完整内容"
        >
          详情
        </Button>
      )
    }
  ]

  return (
    <div>
      <Card 
        size="small" 
        style={{ marginBottom: 16 }}
        styles={{ body: { padding: '8px 16px' } }}
      >
        <Space>
          <Text strong>共找到 {total} 个文档块</Text>
          {chunks.length > 0 && (
            <Text type="secondary">
              当前显示第 {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, total)} 个
            </Text>
          )}
        </Space>
      </Card>

      <Table
        columns={columns}
        dataSource={chunks}
        loading={loading}
        pagination={false}
        size="small"
        rowKey="chunk_id"
        scroll={{ x: 1000 }}
        locale={{
          emptyText: '暂无数据'
        }}
      />

      {total > 0 && (
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
            onChange={onPageChange}
            onShowSizeChange={onPageChange}
            pageSizeOptions={['5', '10', '20', '50']}
            size="small"
          />
        </div>
      )}

      {/* 文档块详情模态框 */}
      <ChunkDetailModal
        visible={isDetailVisible}
        onCancel={handleCloseDetail}
        chunk={selectedChunk}
      />
    </div>
  )
}

export default ChunkTable 