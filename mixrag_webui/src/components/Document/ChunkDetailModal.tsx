import React from 'react'
import { 
  Modal, 
  Descriptions, 
  Typography, 
  Space, 
  Tag,
  Card
} from 'antd'
import { 
  FileTextOutlined,
  NumberOutlined,
  FieldTimeOutlined,
  BlockOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'

const { Text, Paragraph, Title } = Typography

const ChunkDetailModal = ({ 
  visible, 
  onCancel, 
  chunk 
}) => {
  if (!chunk) return null

  // 格式化时间
  const formatDateTime = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 格式化内容大小
  const formatSize = (size) => {
    if (!size) return '0'
    if (size < 1024) return `${size} 字符`
    return `${Math.round(size / 1024 * 100) / 100}K 字符`
  }

  return (
    <Modal
      title={
        <Space>
          <BlockOutlined />
          <span>文档块详情</span>
          <Tag color="blue">块 {chunk.chunk_index + 1}</Tag>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      styles={{
        body: { paddingTop: 16 }
      }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 基本信息 */}
        <Card 
          title={
            <Space>
              <InfoCircleOutlined />
              基本信息
            </Space>
          }
          size="small"
        >
          <Descriptions bordered size="small" column={2}>
            <Descriptions.Item label="Chunk ID" span={2}>
              <Text code copyable={{ text: chunk.chunk_id }}>
                {chunk.chunk_id}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="序号">
              <Tag color="blue">{chunk.chunk_index + 1}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="文档ID">
              <Text code copyable={{ text: chunk.doc_id }}>
                {chunk.doc_id.substring(0, 20)}...
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="内容大小">
              <Tag color="green" icon={<FileTextOutlined />}>
                {formatSize(chunk.chunk_size)}
              </Tag>
            </Descriptions.Item>

            <Descriptions.Item label="创建时间" span={2}>
              <Space>
                <FieldTimeOutlined style={{ color: '#8c8c8c' }} />
                <Text type="secondary">
                  {formatDateTime(chunk.created_at)}
                </Text>
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 完整内容 */}
        <Card 
          title={
            <Space>
              <FileTextOutlined />
              完整内容
              <Tag color="purple">共 {chunk.chunk_size} 字符</Tag>
            </Space>
          }
          size="small"
        >
          <div style={{ 
            maxHeight: '400px', 
            overflow: 'auto',
            padding: '12px',
            background: '#fafafa',
            borderRadius: '6px',
            border: '1px solid #d9d9d9'
          }}>
            <Paragraph 
              style={{ 
                marginBottom: 0,
                lineHeight: '1.6',
                fontSize: '14px',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
              }}
            >
              {chunk.content}
            </Paragraph>
          </div>
        </Card>

      </Space>
    </Modal>
  )
}

export default ChunkDetailModal 