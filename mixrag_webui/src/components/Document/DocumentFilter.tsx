import React from 'react'
import { Space, Select, Button } from 'antd'
import { ClearOutlined, FilterOutlined } from '@ant-design/icons'

const { Option } = Select

// 上传状态选项
const UPLOAD_STATUS_OPTIONS = [
  { value: 'pending', label: '待上传' },
  { value: 'uploaded', label: '已上传' },
  { value: 'failed', label: '上传失败' }
]

// 处理状态选项
const PROCESS_STATUS_OPTIONS = [
  { value: 'pending', label: '待处理' },
  { value: 'processing', label: '处理中' },
  { value: 'completed', label: '已处理' },
  { value: 'failed', label: '处理失败' }
]

const DocumentFilter = ({ 
  uploadStatusFilter, 
  processStatusFilter, 
  onFilterChange 
}) => {
  // 处理上传状态筛选变化
  const handleUploadStatusChange = (value) => {
    onFilterChange(value, processStatusFilter)
  }

  // 处理处理状态筛选变化
  const handleProcessStatusChange = (value) => {
    onFilterChange(uploadStatusFilter, value)
  }

  // 清除所有筛选
  const handleClearFilters = () => {
    onFilterChange(null, null)
  }

  // 检查是否有活跃的筛选
  const hasActiveFilters = uploadStatusFilter || processStatusFilter

  return (
    <Space size="middle">
      <Space.Compact>
        <Select
          value={uploadStatusFilter}
          placeholder="上传状态"
          allowClear
          style={{ width: 120 }}
          onChange={handleUploadStatusChange}
          suffixIcon={<FilterOutlined />}
        >
          {UPLOAD_STATUS_OPTIONS.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>

        <Select
          value={processStatusFilter}
          placeholder="处理状态"
          allowClear
          style={{ width: 120 }}
          onChange={handleProcessStatusChange}
          suffixIcon={<FilterOutlined />}
        >
          {PROCESS_STATUS_OPTIONS.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </Space.Compact>

      {hasActiveFilters && (
        <Button 
          size="small"
          icon={<ClearOutlined />}
          onClick={handleClearFilters}
          type="text"
        >
          清除筛选
        </Button>
      )}
    </Space>
  )
}

export default DocumentFilter 