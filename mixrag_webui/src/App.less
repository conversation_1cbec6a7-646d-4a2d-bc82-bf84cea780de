// 全局变量定义
@primary-color: #1890ff;
@bg-color: #f0f2f5;
@content-bg: #fff;
@shadow-1: 0 2px 8px rgba(0, 0, 0, 0.06);
@shadow-2: 2px 0 6px rgba(0, 0, 0, 0.1);
@border-radius: 6px;
@padding-base: 24px;
@padding-sm: 16px;
@padding-lg: 40px;

// 基本应用样式
.ant-layout {
  min-height: 100vh;
  
  &-sider {
    box-shadow: @shadow-2;
    
    &-light {
      background: @content-bg;
    }
  }
  
  &-header {
    box-shadow: @shadow-1;
  }
  
  &-content {
    background: @bg-color;
  }
}

// 菜单样式优化
.ant-menu {
  &-item, &-submenu-title {
    margin: 0 !important;
    width: 100% !important;
  }
  
  // 顶级菜单项样式 - 统一左边距
  &-root {
    > .ant-menu-item,
    > .ant-menu-submenu > .ant-menu-submenu-title {
      padding-left: @padding-sm !important;
    }
  }
  
  // 子菜单项样式 - 更大的左边距
  &-submenu .ant-menu-item {
    padding-left: @padding-lg !important;
  }
}

// 内容区域样式
.page-content {
  background: @content-bg;
  border-radius: @border-radius;
  min-height: 660px;
}