import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  {
    ignores: [
      'dist',
      'build',
      'node_modules',
      '*.config.js',
      'public'
    ]
  },
  js.configs.recommended,
  ...tseslint.configs.recommended,
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: {
        ...globals.browser,
        ...globals.node,
      },
      parserOptions: {
        ecmaVersion: 'latest',
        ecmaFeatures: { jsx: true },
        sourceType: 'module',
      },
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,

      // TypeScript 规则覆盖
      '@typescript-eslint/no-unused-vars': ['error', {
        varsIgnorePattern: '^[A-Z_]',
        argsIgnorePattern: '^_',
        ignoreRestSiblings: true
      }],
      'no-unused-vars': 'off', // 关闭 JS 版本，使用 TS 版本

      // 允许 any 类型（在重构期间）
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-expressions': 'error',

      // 控制台规则 - 在开发环境允许 console.log
      'no-console': process.env.NODE_ENV === 'production' ? ['error', {
        allow: ['warn', 'error']
      }] : ['warn', {
        allow: ['warn', 'error', 'log', 'debug', 'info', 'time', 'timeEnd', 'group', 'groupEnd', 'table']
      }],

      // 基础规则
      'prefer-const': 'error',
      'no-var': 'error',

      // React 相关规则
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],

      // 代码质量规则
      'eqeqeq': ['error', 'always'],
      'no-duplicate-imports': 'error',
      'no-unreachable': 'error',

      // 代码风格规则
      'semi': ['error', 'never'],
      'quotes': ['error', 'single', { avoidEscape: true }],
      'comma-dangle': ['error', 'never'],
      'object-curly-spacing': ['error', 'always'],
      'array-bracket-spacing': ['error', 'never'],
    },
  }
)
