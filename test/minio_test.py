#!/usr/bin/env python3
"""简化的MinIO测试"""
try:
    from minio import Minio
    import time
    from io import BytesIO
    
    print("🔧 测试MinIO连接...")
    
    # 使用正确的默认凭据
    client = Minio(
        endpoint="localhost:9000",
        access_key="minioadmin", 
        secret_key="minioadmin",
        secure=False
    )
    
    # 测试连接
    buckets = list(client.list_buckets())
    print(f"✅ 连接成功! 当前有 {len(buckets)} 个存储桶")
    
    # 创建测试桶
    bucket_name = f"test-{int(time.time())}"
    client.make_bucket(bucket_name)
    print(f"✅ 创建存储桶: {bucket_name}")
    
    # 上传文件
    content = "Hello MinIO!"
    data = BytesIO(content.encode('utf-8'))
    client.put_object(bucket_name, "test.txt", data, len(content))
    print("✅ 文件上传成功")
    
    # 下载验证
    response = client.get_object(bucket_name, "test.txt")
    downloaded = response.read().decode('utf-8')
    response.close()
    
    if downloaded == content:
        print("✅ 文件下载验证成功")
    else:
        print("❌ 文件内容不匹配")
    
    # 清理
    client.remove_object(bucket_name, "test.txt")
    client.remove_bucket(bucket_name)
    print("✅ 清理完成")
    
    print("🎉 MinIO测试全部通过!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    exit(1)
