#!/usr/bin/env python3
"""简化的PostgreSQL测试"""
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor, Json
    import time
    
    print("🔧 测试PostgreSQL连接...")
    
    # 连接到数据库
    connection = psycopg2.connect(
        host="*************",
        port=5433,
        user="root",
        password="Kaibo-123321",
        database="pgsql",
        cursor_factory=RealDictCursor
    )
    
    print("✅ 连接成功!")
    
    # 创建测试表
    table_name = f"test_simple_{int(time.time())}"
    
    with connection.cursor() as cursor:
        cursor.execute(f"""
            CREATE TABLE {table_name} (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100),
                age INTEGER
            );
        """)
        connection.commit()
        print(f"✅ 创建表: {table_name}")
        
        # 插入数据
        cursor.execute(f"""
            INSERT INTO {table_name} (name, age)
            VALUES (%s, %s)
        """, ("张三", 30))
        
        cursor.execute(f"""
            INSERT INTO {table_name} (name, age)
            VALUES (%s, %s)
        """, ("李四", 25))
        
        connection.commit()
        print("✅ 数据插入成功")
        
        # 查询数据
        cursor.execute(f"SELECT COUNT(*) as count FROM {table_name};")
        count = cursor.fetchone()['count']
        print(f"✅ 查询结果: {count} 条记录")

        # 清理
        cursor.execute(f"DROP TABLE {table_name};")
        connection.commit()
        print("✅ 清理完成")
    
    connection.close()
    print("🎉 PostgreSQL测试全部通过!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    exit(1)
