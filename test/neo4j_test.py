#!/usr/bin/env python3
"""Neo4j 图数据库测试脚本"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from neo4j import GraphDatabase
    from dotenv import load_dotenv
    print("✅ Neo4j 依赖库加载成功")
except ImportError as e:
    print(f"❌ 缺少依赖库: {e}")
    print("�� 请安装: pip install neo4j python-dotenv")
    sys.exit(1)


class Neo4jTest:
    def __init__(self):
        # 加载环境变量
        load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))
        
        self.uri = os.getenv('NEO4J_URI', 'neo4j://*************:7687')
        self.username = os.getenv('NEO4J_USERNAME', 'neo4j')
        self.password = os.getenv('NEO4J_PASSWORD', 'Kaibo-123321')
        
        self.driver = None
        
        print(f"🔧 配置 Neo4j 连接: {self.uri}")

    def connect(self):
        """连接到 Neo4j"""
        try:
            self.driver = GraphDatabase.driver(
                self.uri,
                auth=(self.username, self.password)
            )
            # 验证连接
            self.driver.verify_connectivity()
            print("✅ Neo4j 连接成功")
            return True
        except Exception as e:
            print(f"❌ Neo4j 连接失败: {e}")
            return False

    def test_basic_query(self):
        """测试基本查询"""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                value = result.single()['test']
                if value == 1:
                    print("✅ 基本查询成功")
                    return True
        except Exception as e:
            print(f"❌ 基本查询失败: {e}")
        return False

    def test_create_nodes(self):
        """测试创建节点"""
        try:
            with self.driver.session() as session:
                # 创建测试节点
                session.run("""
                    CREATE (p1:Person {name: 'Alice', age: 30, test_id: 'mixrag_test'})
                    CREATE (p2:Person {name: 'Bob', age: 25, test_id: 'mixrag_test'})
                    CREATE (c:Company {name: 'TechCorp', test_id: 'mixrag_test'})
                """)
                print("✅ 节点创建成功")
                return True
        except Exception as e:
            print(f"❌ 节点创建失败: {e}")
        return False

    def test_create_relationships(self):
        """测试创建关系"""
        try:
            with self.driver.session() as session:
                # 创建关系
                session.run("""
                    MATCH (p1:Person {name: 'Alice', test_id: 'mixrag_test'})
                    MATCH (p2:Person {name: 'Bob', test_id: 'mixrag_test'})
                    MATCH (c:Company {name: 'TechCorp', test_id: 'mixrag_test'})
                    CREATE (p1)-[:KNOWS]->(p2)
                    CREATE (p1)-[:WORKS_FOR]->(c)
                    CREATE (p2)-[:WORKS_FOR]->(c)
                """)
                print("✅ 关系创建成功")
                return True
        except Exception as e:
            print(f"❌ 关系创建失败: {e}")
        return False

    def test_query_graph(self):
        """测试图查询"""
        try:
            with self.driver.session() as session:
                # 查询图数据
                result = session.run("""
                    MATCH (p:Person {test_id: 'mixrag_test'})-[r]->(target)
                    RETURN p.name as person, type(r) as relationship, target.name as target
                """)
                
                relationships = list(result)
                if relationships:
                    print(f"✅ 图查询成功，找到 {len(relationships)} 条关系")
                    for rel in relationships:
                        print(f"   {rel['person']} --{rel['relationship']}--> {rel['target']}")
                    return True
                else:
                    print("⚠️  图查询成功但无数据")
                    return True
        except Exception as e:
            print(f"❌ 图查询失败: {e}")
        return False

    def cleanup_test_data(self):
        """清理测试数据"""
        try:
            with self.driver.session() as session:
                session.run("MATCH (n {test_id: 'mixrag_test'}) DETACH DELETE n")
                print("✅ 测试数据清理成功")
                return True
        except Exception as e:
            print(f"⚠️  测试数据清理失败: {e}")
        return False

    def close(self):
        """关闭连接"""
        if self.driver:
            self.driver.close()

    def run_all_tests(self):
        """运行所有测试"""
        print("🌟" * 25)
        print("🎯 Neo4j 图数据库测试开始")
        print("🌟" * 25)
        
        test_results = []
        
        # 测试连接
        if not self.connect():
            return False
        
        # 执行测试
        tests = [
            ("基本查询", self.test_basic_query),
            ("节点创建", self.test_create_nodes),
            ("关系创建", self.test_create_relationships),
            ("图查询", self.test_query_graph),
            ("数据清理", self.cleanup_test_data)
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 测试: {test_name}")
            result = test_func()
            test_results.append(result)
        
        self.close()
        
        # 生成测试报告
        passed = sum(test_results)
        total = len(test_results)
        
        print("\n" + "🎯" * 25)
        print("📊 Neo4j 测试报告")
        print("🎯" * 25)
        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"成功率: {(passed/total*100):.1f}%")
        
        if passed == total:
            print("🎉 Neo4j 图数据库测试全部通过！")
            print("💡 Neo4j 可用于存储和查询知识图谱数据")
            return True
        else:
            print(f"⚠️  有 {total - passed} 个测试失败")
            return False


def main():
    tester = Neo4jTest()
    success = tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
