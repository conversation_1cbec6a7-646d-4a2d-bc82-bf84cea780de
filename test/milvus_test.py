#!/usr/bin/env python3
"""
Milvus 存储和查询验证脚本

此脚本验证 Milvus 的基本存储和查询功能：
1. 连接到 Milvus 服务器
2. 创建测试集合
3. 存储测试数据（向量和文本）
4. 执行相似性查询
5. 验证查询结果
6. 清理测试数据
"""

import os
import random
import time
from typing import List, Dict, Any

from dotenv import load_dotenv
from pymilvus import (
    connections,
    Collection,
    CollectionSchema,
    DataType,
    FieldSchema,
    utility
)


class MilvusStorageQueryTest:
    """Milvus 存储和查询测试类"""
    
    def __init__(self):
        # 加载环境变量
        load_dotenv()
        
        # 从 .env 文件读取 Milvus 配置
        self.milvus_uri = os.getenv('MILVUS_URI', 'http://localhost:19530')
        self.milvus_db = os.getenv('MILVUS_DB_NAME', 'default')
        
        # 解析连接参数
        if self.milvus_uri.startswith('http://'):
            uri_parts = self.milvus_uri.replace('http://', '').split(':')
            self.host = uri_parts[0]
            self.port = int(uri_parts[1]) if len(uri_parts) > 1 else 19530
        else:
            self.host = 'localhost'
            self.port = 19530
        
        # 测试参数
        self.collection_name = 'mixrag_test_collection'
        self.connection_alias = 'mixrag_test'
        self.vector_dim = 768  # 向量维度，与 .env 中的 EMBEDDING_DIM 保持一致
        
        print("🔧 Milvus 配置信息：")
        print(f"   主机地址: {self.host}:{self.port}")
        print(f"   数据库名: {self.milvus_db}")
        print(f"   向量维度: {self.vector_dim}")
        print()

    def connect_to_milvus(self) -> bool:
        """连接到 Milvus 服务器"""
        try:
            print("🔗 正在连接到 Milvus 服务器...")
            
            connections.connect(
                alias=self.connection_alias,
                host=self.host,
                port=self.port
            )
            
            # 验证连接
            server_version = utility.get_server_version(using=self.connection_alias)
            print(f"✅ 连接成功！服务器版本: {server_version}")
            
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False

    def create_test_collection(self) -> Collection:
        """创建测试集合"""
        try:
            print(f"📋 创建测试集合: {self.collection_name}")
            
            # 删除已存在的集合
            if utility.has_collection(self.collection_name, using=self.connection_alias):
                Collection(self.collection_name, using=self.connection_alias).drop()
                print("   旧集合已删除")
            
            # 定义集合字段
            fields = [
                FieldSchema(
                    name="id",
                    dtype=DataType.INT64,
                    is_primary=True,
                    auto_id=True,
                    description="唯一标识符"
                ),
                FieldSchema(
                    name="vector",
                    dtype=DataType.FLOAT_VECTOR,
                    dim=self.vector_dim,
                    description="文档向量表示"
                ),
                FieldSchema(
                    name="content",
                    dtype=DataType.VARCHAR,
                    max_length=2000,
                    description="文档内容"
                ),
                FieldSchema(
                    name="category",
                    dtype=DataType.VARCHAR,
                    max_length=100,
                    description="文档类别"
                ),
                FieldSchema(
                    name="score",
                    dtype=DataType.FLOAT,
                    description="文档得分"
                )
            ]
            
            # 创建集合 schema
            schema = CollectionSchema(
                fields=fields,
                description="MixRAG 测试集合 - 用于验证存储和查询功能"
            )
            
            # 创建集合
            collection = Collection(
                name=self.collection_name,
                schema=schema,
                using=self.connection_alias
            )

            # 等待集合创建完成
            import time
            time.sleep(1)

            print("✅ 集合创建成功")
            return collection
            
        except Exception as e:
            print(f"❌ 创建集合失败: {e}")
            raise

    def create_index(self, collection: Collection):
        """为向量字段创建索引"""
        try:
            print("🔍 创建向量索引...")

            # 索引参数
            index_params = {
                "metric_type": "COSINE",  # 使用余弦相似度
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }

            # 创建索引
            collection.create_index(
                field_name="vector",
                index_params=index_params
            )

            # 【推荐】使用 utility 等待索引构建完成，而不是 time.sleep
            print("   等待索引构建完成...")
            utility.wait_for_index_building_complete(
                self.collection_name,
                using=self.connection_alias
            )
            # 也可以检查索引状态
            # utility.index_building_progress(self.collection_name, using=self.connection_alias)

            print("✅ 索引创建成功")

        except Exception as e:
            print(f"❌ 创建索引失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def generate_test_data(self, num_docs: int = 10) -> List[Dict[str, Any]]:
        """生成测试数据"""
        print(f"📝 生成 {num_docs} 条测试数据...")
        
        test_docs = [
            {"content": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统", "category": "AI", "score": 0.95},
            {"content": "机器学习是人工智能的子集，通过算法让计算机从数据中学习和改进", "category": "ML", "score": 0.90},
            {"content": "深度学习使用神经网络来模拟人脑的学习过程，在图像识别等领域表现出色", "category": "DL", "score": 0.88},
            {"content": "自然语言处理让计算机能够理解、解释和生成人类语言", "category": "NLP", "score": 0.85},
            {"content": "计算机视觉技术使机器能够识别和理解图像中的内容", "category": "CV", "score": 0.82},
            {"content": "强化学习通过与环境交互来学习最优策略，在游戏和机器人控制中应用广泛", "category": "RL", "score": 0.87},
            {"content": "数据挖掘是从大量数据中发现模式和知识的过程", "category": "DM", "score": 0.80},
            {"content": "大数据技术处理传统数据库难以处理的大规模、高速度、多样化的数据", "category": "BigData", "score": 0.78},
            {"content": "云计算提供可扩展的计算资源和服务，支持现代AI应用的部署", "category": "Cloud", "score": 0.75},
            {"content": "边缘计算将计算能力推向数据源附近，减少延迟并提高效率", "category": "Edge", "score": 0.73}
        ]
        
        # 生成随机向量（实际应用中应该使用真实的嵌入模型）
        mixrag_documents = []
        for i in range(min(num_docs, len(test_docs))):
            doc = test_docs[i].copy()
            # 生成随机向量，添加一些与内容相关的特征
            vector = [random.gauss(0, 1) for _ in range(self.vector_dim)]
            # 根据类别添加一些特征，使相似类别的向量更接近
            category_offset = hash(doc["category"]) % 10 * 0.1
            vector = [v + category_offset for v in vector]
            
            doc["vector"] = vector
            mixrag_documents.append(doc)
        
        print(f"✅ 测试数据生成完成")
        return mixrag_documents

    def store_data(self, collection: Collection, mixrag_documents: List[Dict[str, Any]]) -> bool:
        """存储测试数据到 Milvus"""
        try:
            print("💾 正在存储数据到 Milvus...")

            # 准备批量插入数据
            vectors = [doc["vector"] for doc in mixrag_documents]
            contents = [doc["content"] for doc in mixrag_documents]
            categories = [doc["category"] for doc in mixrag_documents]
            scores = [doc["score"] for doc in mixrag_documents]

            # 1. 插入数据
            data = [vectors, contents, categories, scores]
            insert_result = collection.insert(data)

            print(f"   插入记录数: {len(vectors)}")
            print(f"   分配的主键范围: {insert_result.primary_keys[0]} - {insert_result.primary_keys[-1]}")

            # 2. 【关键修复】执行 flush 操作，将数据从内存缓冲区刷入磁盘段
            # 这是一个阻塞操作，完成后数据才对后续加载和查询可见
            print("   正在将数据刷入磁盘 (flush)...")
            collection.flush(timeout=5)
            print("   ✅ 数据 Flush 完成")

            # 3. 加载集合到内存以供查询
            print("   正在加载集合到内存...")
            collection.load()
            # load() 是阻塞的，完成后集合即可用，无需手动 sleep

            # 4. 验证数据量
            # flush 和 load 之后，num_entities 就能获取到最新的计数值
            entity_count = collection.num_entities
            print(f"✅ 数据存储成功！集合中总计 {entity_count} 条记录")

            return True

        except Exception as e:
            print(f"❌ 数据存储失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def perform_similarity_search(self, collection: Collection, query_text: str) -> List[Dict]:
        """执行相似性搜索"""
        try:
            print(f"🔍 执行相似性搜索...")
            print(f"   查询内容: '{query_text}'")
            
            # 生成查询向量（实际应用中应该使用相同的嵌入模型）
            # 这里简单模拟：为AI相关查询生成特定模式的向量
            query_vector = [random.gauss(0, 1) for _ in range(self.vector_dim)]
            if "人工智能" in query_text or "AI" in query_text:
                # 为AI相关查询添加特定特征
                ai_offset = hash("AI") % 10 * 0.1
                query_vector = [v + ai_offset for v in query_vector]
            
            # 搜索参数
            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }
            
            # 执行搜索
            search_results = collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params,
                limit=5,  # 返回前5个最相似的结果
                output_fields=["content", "category", "score"]
            )
            
            # 处理搜索结果
            results = []
            for hits in search_results:
                for hit in hits:
                    similarity = 1 - hit.distance  # 转换距离为相似度
                    result = {
                        "id": hit.id,
                        "similarity": similarity,
                        "content": hit.entity.get("content"),
                        "category": hit.entity.get("category"),
                        "score": hit.entity.get("score")
                    }
                    results.append(result)
            
            print(f"✅ 搜索完成，找到 {len(results)} 条相似记录:")
            for i, result in enumerate(results, 1):
                print(f"   {i}. [相似度: {result['similarity']:.4f}] [{result['category']}] {result['content'][:50]}...")
            
            return results
            
        except Exception as e:
            print(f"❌ 相似性搜索失败: {e}")
            return []

    def perform_filter_search(self, collection: Collection, category: str) -> List[Dict]:
        """执行条件过滤搜索"""
        try:
            print(f"🔍 执行条件过滤搜索...")
            print(f"   过滤条件: category = '{category}'")
            
            # 使用表达式查询
            results = collection.query(
                expr=f'category == "{category}"',
                output_fields=["content", "category", "score"],
                limit=10
            )
            
            print(f"✅ 过滤搜索完成，找到 {len(results)} 条匹配记录:")
            for i, result in enumerate(results, 1):
                print(f"   {i}. [{result['category']}] {result['content'][:50]}...")
            
            return results
            
        except Exception as e:
            print(f"❌ 条件过滤搜索失败: {e}")
            return []

    def verify_data_integrity(self, collection: Collection, expected_count: int) -> bool:
        """验证数据完整性"""
        try:
            print("🔍 验证数据完整性...")
            
            # 检查数据量
            actual_count = collection.num_entities
            print(f"   期望记录数: {expected_count}")
            print(f"   实际记录数: {actual_count}")
            
            if actual_count == expected_count:
                print("✅ 数据完整性验证通过")
                return True
            else:
                print("❌ 数据完整性验证失败：记录数不匹配")
                return False
                
        except Exception as e:
            print(f"❌ 数据完整性验证失败: {e}")
            return False

    def cleanup(self, collection: Collection):
        """清理测试数据"""
        try:
            print("🧹 清理测试数据...")
            
            # 删除集合
            collection.drop()
            print(f"   集合 {self.collection_name} 已删除")
            
            # 断开连接
            connections.disconnect(self.connection_alias)
            print("   Milvus 连接已断开")
            
            print("✅ 清理完成")
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")

    def run_complete_test(self):
        """运行完整的存储和查询测试"""
        print("=" * 80)
        print("🚀 开始 Milvus 存储和查询验证测试")
        print("=" * 80)
        
        start_time = time.time()
        
        try:
            # 1. 连接到 Milvus
            if not self.connect_to_milvus():
                return False
            
            # 2. 创建测试集合
            collection = self.create_test_collection()
            
            # 3. 创建索引
            self.create_index(collection)
            
            # 4. 生成测试数据
            test_documents = self.generate_test_data(10)
            
            # 5. 存储数据
            if not self.store_data(collection, test_documents):
                return False
            
            # 6. 验证数据完整性
            if not self.verify_data_integrity(collection, len(test_documents)):
                return False
            
            print("\n" + "=" * 50)
            print("📊 执行查询测试")
            print("=" * 50)
            
            # 7. 执行相似性搜索
            similarity_results = self.perform_similarity_search(
                collection, 
                "人工智能和机器学习的相关技术"
            )
            
            # 8. 执行条件过滤搜索
            filter_results = self.perform_filter_search(collection, "AI")
            
            # 9. 验证查询结果
            if similarity_results and filter_results:
                print("\n✅ 所有查询测试通过！")
            else:
                print("\n❌ 部分查询测试失败！")
            
            # 10. 清理测试数据
            print("\n" + "=" * 50)
            self.cleanup(collection)
            
            # 测试总结
            end_time = time.time()
            duration = end_time - start_time
            
            print("=" * 80)
            print("🎉 Milvus 存储和查询验证测试完成！")
            print("=" * 80)
            print(f"✅ 存储功能: 成功存储 {len(test_documents)} 条记录")
            print(f"✅ 查询功能: 相似性搜索和条件过滤均正常工作")
            print(f"✅ 数据完整性: 验证通过")
            print(f"⏱️  总耗时: {duration:.2f} 秒")
            print("\n🔥 Milvus 数据库连接正常，存储和查询功能完全可用！")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("Milvus 存储和查询验证工具")
    print("此工具将验证 Milvus 的基本存储和查询功能\n")
    
    # 创建测试实例并运行
    tester = MilvusStorageQueryTest()
    success = tester.run_complete_test()
    
    if success:
        print("\n🎯 测试结论: Milvus 部署成功，可以正常使用！")
    else:
        print("\n⚠️  测试结论: Milvus 部署存在问题，请检查配置和服务状态")


if __name__ == "__main__":
    main() 