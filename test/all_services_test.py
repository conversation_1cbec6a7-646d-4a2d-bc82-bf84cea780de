#!/usr/bin/env python3
"""MixRAG存储服务集成测试脚本"""
import os
import sys
import time
import subprocess
from datetime import datetime

def main():
    print("MixRAG 存储服务集成测试工具")
    print("此工具将测试 MixRAG 系统的所有5种存储组件\n")
    
    # 移除代理设置
    print("🔧 移除代理设置...")
    os.environ.pop('https_proxy', None)
    os.environ.pop('http_proxy', None)
    os.environ.pop('HTTPS_PROXY', None)
    os.environ.pop('HTTP_PROXY', None)
    print("✅ 代理已移除\n")
    
    # 确定脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    test_scripts = [
        {"name": "Milvus 向量数据库", "script": os.path.join(script_dir, "milvus_test.py")},
        {"name": "MinIO 对象存储", "script": os.path.join(script_dir, "minio_test.py")},
        {"name": "Redis 键值存储", "script": os.path.join(script_dir, "redis_test.py")},
        {"name": "PostgreSQL 关系数据库", "script": os.path.join(script_dir, "postgresql_test.py")},
        {"name": "Neo4j 图数据库", "script": os.path.join(script_dir, "neo4j_test.py")}
    ]
    
    print(f"🔧 配置: {len(test_scripts)} 个存储服务")
    print("🌟" * 30)
    print("🎯 开始 MixRAG 存储服务集成测试")
    print("🌟" * 30)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = {}
    overall_start = time.time()
    
    for test_config in test_scripts:
        script_name = test_config["script"]
        service_name = test_config["name"]
        
        print("=" * 70)
        print(f"🚀 测试: {service_name}")
        print(f"�� 脚本: {script_name}")
        print("=" * 70)
        
        start_time = time.time()
        
        try:
            # 设置无代理环境
            env = os.environ.copy()
            env.pop('https_proxy', None)
            env.pop('http_proxy', None)
            env.pop('HTTPS_PROXY', None)
            env.pop('HTTP_PROXY', None)
            
            result = subprocess.run(
                [sys.executable, script_name],
                capture_output=True,
                text=True,
                env=env
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ {service_name} 测试通过 ({duration:.2f}s)")
                results[service_name] = {"success": True, "duration": duration}
            else:
                print(f"❌ {service_name} 测试失败 ({duration:.2f}s)")
                print("错误信息:")
                print(result.stderr)
                results[service_name] = {"success": False, "duration": duration}
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {service_name} 测试异常: {e}")
            results[service_name] = {"success": False, "duration": duration}
        
        print()
    
    overall_duration = time.time() - overall_start
    
    # 生成测试报告
    print("🎯" * 30)
    print("📊 MixRAG 存储服务测试报告")
    print("🎯" * 30)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results.values() if r["success"])
    
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总测试数: {total_tests}")
    print(f"通过数: {passed_tests}")
    print(f"失败数: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
    print(f"总耗时: {overall_duration:.2f} 秒")
    print()
    
    print("详细测试结果:")
    for service_name, result in results.items():
        status = "✅ 通过" if result["success"] else "❌ 失败"
        print(f"  {service_name}: {status} ({result['duration']:.2f}s)")
    
    print()
    
    if passed_tests == total_tests:
        print("�� 所有存储服务测试通过！")
        print("🚀 MixRAG 系统存储层完全可用！")
        print("💡 系统已准备就绪，可以开始使用 MixRAG 进行知识图谱构建和检索")
        print()
        print("📋 已验证的存储服务:")
        print("  ✅ Milvus - 向量数据库 (嵌入向量存储与相似性搜索)")
        print("  ✅ MinIO - 对象存储 (文档和文件存储)")
        print("  ✅ Redis - 键值存储 (缓存和会话管理)")
        print("  ✅ PostgreSQL - 关系数据库 (结构化数据和元数据)")
        print("  ✅ Neo4j - 图数据库 (知识图谱和实体关系)")
        print()
        print("🎯 系统状态: 所有存储服务可用，MixRAG 系统完全就绪！")
        return 0
    else:
        print(f"⚠️  有 {total_tests - passed_tests} 个服务测试失败")
        print("🔧 请检查失败服务的配置和运行状态")
        print("💡 提示：确保所有相关的 Docker 容器都在运行")
        print("⚠️  系统状态: 部分存储服务异常，请检查并修复失败的服务")
        return 1


if __name__ == "__main__":
    exit(main())
