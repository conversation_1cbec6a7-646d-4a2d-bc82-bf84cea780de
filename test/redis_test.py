#!/usr/bin/env python3
"""简化的Redis测试"""
try:
    import redis
    import time
    
    print("🔧 测试Redis连接...")
    
    # 尝试不同的连接方式
    connection_methods = [
        {"host": "*************", "port": 6379, "password": "Kaibo-123321"},
        {"host": "*************", "port": 6379, "password": None},
        {"host": "localhost", "port": 6379, "password": None}
    ]
    
    client = None
    for method in connection_methods:
        try:
            print(f"尝试连接: {method['host']}:{method['port']}")
            
            client = redis.Redis(
                host=method['host'],
                port=method['port'],
                password=method['password'],
                decode_responses=True
            )
            
            # 测试连接
            pong = client.ping()
            if pong:
                print(f"✅ 连接成功!")
                break
        except Exception as e:
            print(f"   连接失败: {e}")
            continue
    
    if not client:
        print("❌ 所有连接方法都失败")
        exit(1)
    
    # 测试基本操作
    test_key = f"test_{int(time.time())}"
    
    # 字符串操作
    client.set(test_key, "Hello Redis!")
    value = client.get(test_key)
    print(f"✅ 字符串操作: {value}")
    
    # 哈希操作
    hash_key = f"hash_{int(time.time())}"
    client.hset(hash_key, "name", "测试用户")
    hash_value = client.hget(hash_key, "name")
    print(f"✅ 哈希操作: {hash_value}")
    
    # 清理
    client.delete(test_key, hash_key)
    print("✅ 清理完成")
    
    print("🎉 Redis测试全部通过!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    exit(1)
