#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：将 data/documents_dup_part_1_part_1 文件中的前100行分别保存为100个txt文件
每一行保存为一个txt文件，文件名为 data/documents_dup_part_1_part_1/{index}.txt
注意：由于原路径是文件，我们将在 data/documents_dup_part_1_part_1_files/ 目录下创建文件
"""

import os
import sys

def split_documents():
    """
    将源文件的前100行分别保存为100个txt文件
    """
    # 源文件路径
    source_file = "data/documents_dup_part_1_part_1"
    
    # 目标目录路径 - 由于原路径是文件，使用新的目录名
    target_dir = "data/documents_dup_part_1_part_1_files"
    
    # 检查源文件是否存在
    if not os.path.exists(source_file):
        print(f"错误：源文件 {source_file} 不存在")
        return False
    
    # 创建目标目录（如果不存在）
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"创建目录：{target_dir}")
    
    try:
        # 读取源文件
        with open(source_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 检查文件行数
        total_lines = len(lines)
        print(f"源文件总行数：{total_lines}")
        
        if total_lines < 100:
            print(f"警告：源文件只有 {total_lines} 行，少于100行")
            lines_to_process = total_lines
        else:
            lines_to_process = 100
        
        # 处理前100行（或文件的所有行，如果少于100行）
        success_count = 0
        for i in range(lines_to_process):
            line_content = lines[i].strip()  # 去除行尾的换行符
            
            # 跳过空行
            if not line_content:
                print(f"跳过第 {i+1} 行（空行）")
                continue
            
            # 生成目标文件路径，文件名为 {index}.txt
            target_file = os.path.join(target_dir, f"{i+1}.txt")
            
            try:
                # 写入文件
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(line_content)
                
                success_count += 1
                print(f"已保存第 {i+1} 行到文件：{target_file}")
                
            except Exception as e:
                print(f"错误：保存第 {i+1} 行到文件 {target_file} 时出错：{e}")
        
        print(f"\n处理完成！成功保存了 {success_count} 个文件到目录：{target_dir}")
        print(f"文件命名格式：1.txt, 2.txt, ..., {success_count}.txt")
        return True
        
    except Exception as e:
        print(f"错误：读取源文件时出错：{e}")
        return False

def main():
    """
    主函数
    """
    print("开始分割文档...")
    print("=" * 60)
    print("功能：将 data/documents_dup_part_1_part_1 中的前100行")
    print("     分别保存为100个txt文件")
    print("目标：data/documents_dup_part_1_part_1_files/{index}.txt")
    print("=" * 60)
    
    success = split_documents()
    
    print("=" * 60)
    if success:
        print("脚本执行成功！")
        print("您可以在 data/documents_dup_part_1_part_1_files/ 目录下找到生成的文件")
    else:
        print("脚本执行失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
