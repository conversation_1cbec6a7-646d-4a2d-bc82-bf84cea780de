#!/bin/bash

echo "🛑 停止 MixRAG 系统服务..."

# 停止 MixRAG 系统服务
MAIN_PIDS=$(ps aux | grep -E "python.*src/main\.py|python.*main\.py" | grep -v grep | awk '{print $2}')
if [ -n "$MAIN_PIDS" ]; then
    echo "   停止 MixRAG 系统服务..."
    for pid in $MAIN_PIDS; do
        kill -TERM $pid 2>/dev/null || true
    done
fi

# 停止任何遗留的API服务
API_PIDS=$(ps aux | grep -E "python.*start_api\.py|uvicorn.*api\.main:app" | grep -v grep | awk '{print $2}')
if [ -n "$API_PIDS" ]; then
    echo "   停止遗留的API服务..."
    for pid in $API_PIDS; do
        kill -TERM $pid 2>/dev/null || true
    done
fi

# 停止任何独立的流水线服务（如果存在）
PIPELINE_PIDS=$(ps aux | grep -E "python.*pipeline_service" | grep -v grep | awk '{print $2}')
if [ -n "$PIPELINE_PIDS" ]; then
    echo "   停止独立的流水线服务..."
    for pid in $PIPELINE_PIDS; do
        kill -TERM $pid 2>/dev/null || true
    done
fi

echo "✅ 所有服务已停止"
