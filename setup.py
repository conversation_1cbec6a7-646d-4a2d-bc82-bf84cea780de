#!/usr/bin/env python3
"""
MixRAG 项目安装脚本
"""
from setuptools import setup, find_packages
import os

# 读取 README 文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "MixRAG - 简化版 MixRAG 核心功能"

# 读取 requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f.readlines() 
                   if line.strip() and not line.startswith('#')]
    return []

setup(
    name="mixrag",
    version="0.1.0",
    author="MixRAG Team",
    author_email="<EMAIL>",
    description="简化版 MixRAG 核心功能 - 知识图谱增强的检索生成系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/HKUDS/MixRAG",
    project_urls={
        "Bug Tracker": "https://github.com/HKUDS/MixRAG/issues",
        "Documentation": "https://github.com/HKUDS/MixRAG",
        "Source Code": "https://github.com/HKUDS/MixRAG",
    },
    
    # 包配置
    packages=find_packages(where="."),
    package_dir={"": "."},
    
    # Python 版本要求
    python_requires=">=3.8",
    
    # 依赖
    install_requires=read_requirements(),
    
    # 额外依赖组
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-asyncio>=0.18.0",
            "black>=22.0",
            "flake8>=4.0",
            "mypy>=0.950",
        ],
        "api": [
            "fastapi>=0.100.0",
            "uvicorn[standard]>=0.23.0",
            "python-multipart>=0.0.6",
        ],
        "storage": [
            "redis>=4.5.0",
            "pymilvus>=2.3.0",
            "neo4j>=5.0.0",
            "psycopg2-binary>=2.9.0",
            "minio>=7.1.0",
        ],
        "ml": [
            "torch>=2.0.0",
            "transformers>=4.30.0",
            "sentence-transformers>=2.2.0",
            "openai>=1.0.0",
        ]
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Text Processing :: Linguistic",
    ],
    
    # 关键词
    keywords="rag, knowledge-graph, retrieval, generation, nlp, ai",
    
    # 入口点
    entry_points={
        "console_scripts": [
            "mixrag-api=src.api.start_api:main",
            "mixrag-test=test.all_services_test:main",
        ],
    },
    
    # 包含数据文件
    include_package_data=True,
    package_data={
        "src": ["*.yaml", "*.yml", "*.json", "*.txt"],
        "test": ["*.py"],
        "db": ["*.sql"],
    },
    
    # 项目状态
    zip_safe=False,
) 